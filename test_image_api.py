#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的图片获取API功能
"""

import requests
import re
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_image_apis():
    """测试所有图片API"""
    session = requests.Session()
    session.verify = False
    
    image_apis = [
        {
            'name': 'API1 - bimg.cc',
            'url': 'https://api.bimg.cc/random?w=1920&h=1080&mkt=zh-CN',
            'method': 'redirect'
        },
        {
            'name': 'API2 - bing.ee123.net',
            'url': 'https://bing.ee123.net/img/rand',
            'method': 'redirect'
        },
        {
            'name': 'API3 - xsot.cn',
            'url': 'https://api.xsot.cn/bing/?jump=true',
            'method': 'html_parse'
        },
        {
            'name': 'API4 - vvhan.com (备用)',
            'url': 'https://api.vvhan.com/api/bing?type=json&rand=sj',
            'method': 'json'
        }
    ]
    
    print("🔍 开始测试图片获取API...")
    print("=" * 60)
    
    for i, api in enumerate(image_apis, 1):
        print(f"\n📡 测试 {api['name']}")
        print(f"URL: {api['url']}")
        print(f"方法: {api['method']}")
        
        try:
            if api['method'] == 'redirect':
                # 获取重定向的Location头
                print("🔄 发送HEAD请求获取重定向...")
                response = session.head(api['url'], allow_redirects=False, timeout=10)
                print(f"状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")
                
                if response.status_code in [301, 302, 303, 307, 308]:
                    location = response.headers.get('Location', '')
                    if location:
                        print(f"✅ 成功获取图片URL: {location}")
                    else:
                        print("❌ Location头为空")
                else:
                    print(f"❌ 未返回重定向，状态码: {response.status_code}")
                    
            elif api['method'] == 'html_parse':
                # 解析HTML内容获取图片链接
                print("🌐 发送GET请求解析HTML...")
                response = session.get(api['url'], timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print(f"响应内容: {response.text[:200]}...")
                    # 提取 <a href="...">Found</a> 中的链接
                    match = re.search(r'<a href="([^"]+)">Found</a>', response.text)
                    if match:
                        image_url = match.group(1)
                        print(f"✅ 成功解析图片URL: {image_url}")
                    else:
                        print("❌ 未找到图片链接")
                        print(f"完整响应: {response.text}")
                else:
                    print(f"❌ 请求失败，状态码: {response.status_code}")
                    
            elif api['method'] == 'json':
                # JSON格式API
                print("📄 发送GET请求获取JSON...")
                response = session.get(api['url'], timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"JSON响应: {data}")
                    
                    if data.get('success') and 'data' in data:
                        url = data['data'].get('url', '')
                        if '&w=4096' in url:
                            url = url.replace('&w=4096', '')
                        if url:
                            print(f"✅ 成功获取图片URL: {url}")
                        else:
                            print("❌ data.url为空")
                    else:
                        # 兼容旧格式
                        url = data.get('url', '')
                        if '&w=4096' in url:
                            url = url.replace('&w=4096', '')
                        if url:
                            print(f"✅ 成功获取图片URL (旧格式): {url}")
                        else:
                            print("❌ 返回的JSON中未找到有效图片URL")
                else:
                    print(f"❌ 请求失败，状态码: {response.status_code}")
                    
        except Exception as e:
            print(f"❌ {api['name']} 测试失败: {str(e)}")
        
        print("-" * 40)
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    print("🚀 开始测试图片获取API功能")
    print("=" * 60)
    
    # 测试各个API
    test_image_apis()
    
    print("\n✨ 所有测试完成！")
