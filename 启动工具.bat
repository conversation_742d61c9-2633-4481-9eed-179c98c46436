@echo off
chcp 65001 >nul
echo ================================
echo     短信发送工具启动脚本
echo ================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境！
    echo 请先安装Python 3.x
    pause
    exit /b 1
)

echo Python环境检查通过！
echo.

echo 正在检查依赖库...
python -c "import requests" >nul 2>&1
if errorlevel 1 (
    echo 正在安装requests库...
    pip install requests
    if errorlevel 1 (
        echo 错误：requests库安装失败！
        pause
        exit /b 1
    )
)

echo 依赖库检查完成！
echo.

echo 正在启动短信发送工具...
echo 默认密码：1995
echo.

python "短信工具可编辑版.py"

if errorlevel 1 (
    echo.
    echo 程序运行出错！
    pause
)
