// 简单的编译测试文件
// 用于验证代码语法是否正确，不需要Qt6也能编译

#include <iostream>
#include <vector>
#include <map>
#include <string>
#include <set>
#include <algorithm>
#include <sstream>

// 模拟Qt类型
namespace Qt {
    enum MouseButton { LeftButton = 1, RightButton = 2 };
    enum AlignmentFlag { AlignCenter = 1 };
}

class QWidget {};
class QPushButton : public QWidget {};
class QMainWindow : public QWidget {};
class QObject {};
class QThread : public QObject {};
class QMutex {};
class QEvent {};
class QMouseEvent {};

// 测试核心数据结构
struct TetrisShape {
    std::vector<std::vector<std::pair<int, int>>> rotations;
};

struct Solution {
    std::vector<std::vector<int>> board;
    std::string algorithm;
    int step;
    long long timestamp;
    std::map<std::string, int> piecesUsed;
    bool isComplete;
    std::string hash;
    int score;
    int filledCells;
    int requiredCovered;
    int forbiddenViolated;
    bool isValid;
};

// 测试核心算法逻辑
class TetrisPuzzleCore {
private:
    int m_boardSize;
    std::vector<std::vector<int>> m_board;
    std::map<std::string, TetrisShape> m_tetrisShapes;
    std::map<std::string, int> m_pieceCounts;
    std::vector<Solution> m_solutions;

public:
    TetrisPuzzleCore(int boardSize = 7) : m_boardSize(boardSize) {
        initBoard();
        initShapes();
        initPieceCounts();
    }

    void initBoard() {
        m_board = std::vector<std::vector<int>>(m_boardSize, std::vector<int>(m_boardSize, 0));
    }

    void initShapes() {
        // T形状
        m_tetrisShapes["T"] = {{{
            {{0,1}, {1,0}, {1,1}, {1,2}},  // T形状 - 上
            {{0,1}, {1,1}, {1,2}, {2,1}},  // T形状 - 右
            {{0,0}, {1,0}, {1,1}, {2,0}},  // T形状 - 右
            {{0,0}, {0,1}, {0,2}, {1,1}},  // T形状 - 下
            {{0,1}, {1,0}, {1,1}, {2,1}}   // T形状 - 左
        }}};
        
        // 田字形状
        m_tetrisShapes["田"] = {{{
            {{0,0}, {0,1}, {1,0}, {1,1}}   // O形状（方块）
        }}};
        
        // I形状
        m_tetrisShapes["横杠竖条"] = {{{
            {{0,0}, {0,1}, {0,2}, {0,3}},  // I形状 - 水平
            {{0,0}, {1,0}, {2,0}, {3,0}}   // I形状 - 垂直
        }}};
        
        // Z形状
        m_tetrisShapes["Z"] = {{{
            {{0,0}, {0,1}, {1,1}, {1,2}},  // Z形状 - 0度（水平）
            {{0,1}, {1,0}, {1,1}, {2,0}},  // Z形状 - 90度（垂直）
            {{0,1}, {0,2}, {1,0}, {1,1}},  // S形状 - 0度（水平）
            {{0,0}, {1,0}, {1,1}, {2,1}}   // S形状 - 90度（垂直）
        }}};
        
        // L形状
        m_tetrisShapes["L"] = {{{
            {{0,0}, {1,0}, {2,0}, {2,1}},  // L形状 - 0度
            {{0,1}, {1,1}, {2,1}, {2,0}},  // J形状 - 0度
            {{0,0}, {1,0}, {1,1}, {1,2}},  // J形状 - 90度
            {{0,0}, {0,1}, {1,0}, {2,0}},  // J形状 - 180度
            {{0,0}, {0,1}, {0,2}, {1,2}}   // J形状 - 270度
        }}};
    }

    void initPieceCounts() {
        m_pieceCounts["T"] = 1;
        m_pieceCounts["田"] = 1;
        m_pieceCounts["横杠竖条"] = 1;
        m_pieceCounts["Z"] = 1;
        m_pieceCounts["L"] = 1;
    }

    bool canPlacePiece(const std::vector<std::vector<int>>& board,
                      const std::vector<std::pair<int, int>>& shape,
                      int startRow, int startCol, int pieceId) {
        for (const auto& offset : shape) {
            int row = startRow + offset.first;
            int col = startCol + offset.second;
            
            if (row < 0 || row >= m_boardSize || col < 0 || col >= m_boardSize) {
                return false; // 超出边界
            }
            
            if (board[row][col] == 2) {
                return false; // 禁止位置
            }
            
            if (board[row][col] > 2) {
                return false; // 已被其他方块占用
            }
        }
        return true;
    }

    void placePiece(std::vector<std::vector<int>>& board,
                   const std::vector<std::pair<int, int>>& shape,
                   int startRow, int startCol, int pieceId) {
        for (const auto& offset : shape) {
            int row = startRow + offset.first;
            int col = startCol + offset.second;
            board[row][col] = pieceId;
        }
    }

    void removePiece(std::vector<std::vector<int>>& board,
                    const std::vector<std::pair<int, int>>& shape,
                    int startRow, int startCol) {
        for (const auto& offset : shape) {
            int row = startRow + offset.first;
            int col = startCol + offset.second;
            if (m_board[row][col] <= 2) {
                board[row][col] = m_board[row][col];
            } else {
                board[row][col] = 0;
            }
        }
    }

    std::string generateBoardHash(const std::vector<std::vector<int>>& board) {
        std::stringstream ss;
        for (const auto& row : board) {
            for (int cell : row) {
                ss << cell << ",";
            }
        }
        return ss.str();
    }

    bool isCompleteSolution(const std::vector<std::vector<int>>& board,
                           const std::map<std::string, int>& piecesUsed) {
        // 检查所有必需位置是否被覆盖
        for (int i = 0; i < m_boardSize; ++i) {
            for (int j = 0; j < m_boardSize; ++j) {
                if (m_board[i][j] == 1 && board[i][j] <= 2) {
                    return false; // 必需位置未被覆盖
                }
                if (m_board[i][j] == 2 && board[i][j] > 2) {
                    return false; // 禁止位置被覆盖
                }
            }
        }

        // 检查是否所有方块都被使用
        for (const auto& pair : m_pieceCounts) {
            if (pair.second > 0) {
                auto it = piecesUsed.find(pair.first);
                int usedCount = (it != piecesUsed.end()) ? it->second : 0;
                if (usedCount != pair.second) {
                    return false; // 方块使用数量不匹配
                }
            }
        }

        return true;
    }

    void printBoard(const std::vector<std::vector<int>>& board) {
        for (const auto& row : board) {
            for (int cell : row) {
                std::cout << cell << " ";
            }
            std::cout << std::endl;
        }
    }

    void testBasicFunctionality() {
        std::cout << "测试基本功能..." << std::endl;
        
        // 测试棋盘初始化
        std::cout << "棋盘大小: " << m_boardSize << "x" << m_boardSize << std::endl;
        
        // 测试方块形状
        std::cout << "方块类型数量: " << m_tetrisShapes.size() << std::endl;
        for (const auto& pair : m_tetrisShapes) {
            std::cout << "方块 " << pair.first << " 有 " << pair.second.rotations.size() << " 种旋转" << std::endl;
        }
        
        // 测试方块放置
        auto board = m_board;
        auto tShape = m_tetrisShapes["T"].rotations[0];
        if (canPlacePiece(board, tShape, 0, 0, 3)) {
            placePiece(board, tShape, 0, 0, 3);
            std::cout << "成功放置T形方块:" << std::endl;
            printBoard(board);
        }
        
        std::cout << "基本功能测试完成!" << std::endl;
    }
};

int main() {
    std::cout << "俄罗斯方块拼图游戏 - 编译测试" << std::endl;
    std::cout << "================================" << std::endl;
    
    try {
        TetrisPuzzleCore game(5);
        game.testBasicFunctionality();
        
        std::cout << std::endl << "✅ 所有测试通过! 代码编译正确。" << std::endl;
        std::cout << "现在可以安装Qt6并构建完整的GUI应用程序。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
