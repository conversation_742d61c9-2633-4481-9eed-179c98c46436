#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU拼图破解器启动脚本
"""

import sys
import os

def check_dependencies():
    """检查依赖"""
    print("📦 检查依赖包...")
    
    required_packages = {
        'cupy': 'CuPy (GPU加速)',
        'numpy': 'NumPy',
        'matplotlib': 'Matplotlib (可视化)'
    }
    
    missing = []
    
    for package, description in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {description}")
        except ImportError:
            print(f"❌ {description}")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing)}")
        print("安装命令:")
        for package in missing:
            if package == 'cupy':
                print("  pip install cupy-cuda11x  # 或 cupy-cuda12x")
            else:
                print(f"  pip install {package}")
        return False
    
    return True

def show_menu():
    """显示启动菜单"""
    print("\n🧩 GPU拼图破解器")
    print("=" * 50)
    print("1. 🖥️ GUI界面版本 (推荐) - 带界面操作")
    print("2. 🎮 交互式命令行版本")
    print("3. 🧪 演示版本 (自动测试)")
    print("4. ⚡ GPU性能测试")
    print("5. 📊 您的8x8棋盘测试")
    print("0. 退出")
    print("=" * 50)

def run_gui():
    """运行GUI版本"""
    try:
        from GUI拼图破解器 import main
        main()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保安装了tkinter: pip install tk")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_interactive():
    """运行交互式版本"""
    try:
        from 交互式GPU拼图破解器 import InteractiveGPUPuzzleSolver
        app = InteractiveGPUPuzzleSolver()
        app.run()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_demo():
    """运行演示版本"""
    try:
        from GPU拼图破解器完整版 import main
        main()
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_performance_test():
    """运行性能测试"""
    try:
        from 重度GPU加速回溯法 import test_heavy_gpu
        test_heavy_gpu()
    except ImportError:
        try:
            from GPU负载测试 import main
            main()
        except ImportError as e:
            print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def run_your_board_test():
    """运行您的8x8棋盘测试"""
    try:
        from GPU拼图破解器完整版 import GPUTetrisPuzzleSolver
        
        print("🧪 测试您的8x8棋盘")
        print("=" * 50)
        
        solver = GPUTetrisPuzzleSolver()
        
        # 您的8x8棋盘
        board = [
            [0, 0, 0, 0, 0, 1, 2, 0],
            [0, 0, 0, 0, 0, 1, 1, 0],
            [0, 0, 0, 0, 0, 0, 1, 2],
            [0, 0, 0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 0, 2, 0],
            [0, 0, 0, 0, 0, 0, 0, 0],
            [0, 2, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0]
        ]
        
        pieces = {
            "T": 1,
            "田": 1,
            "横杠竖条": 1,
            "Z": 1,
            "L": 1
        }
        
        print("🚀 开始GPU加速求解您的8x8棋盘...")
        print("💡 请观察GPU使用率")
        
        solutions = solver.gpu_accelerated_solve(board, pieces, max_solutions=3)
        
        if solutions:
            print(f"\n🎉 找到 {len(solutions)} 个解决方案！")
            
            # 可视化第一个解决方案
            solver.visualize_solution(0, "your_board_solution.png")
            
            print(f"⚡ GPU计算时间: {solver.gpu_time:.3f}秒")
            print(f"📊 总求解时间: {solver.solve_time:.3f}秒")
            
            if solver.gpu_available:
                speedup = solver.solve_time / max(solver.gpu_time, 0.001)
                print(f"🚀 GPU加速效果: {speedup:.1f}x")
        else:
            print("❌ 未找到解决方案")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 运行失败: {e}")

def main():
    """主函数"""
    print("🎮 GPU拼图破解器启动器")
    print("基于您的HTML游戏重写的Python版本")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺少的包后重试")
        input("按回车键退出...")
        return
    
    print("✅ 依赖检查通过")
    
    while True:
        show_menu()
        
        try:
            choice = int(input("\n请选择: "))
            
            if choice == 0:
                print("👋 再见！")
                break
            elif choice == 1:
                run_gui()
            elif choice == 2:
                run_interactive()
            elif choice == 3:
                run_demo()
            elif choice == 4:
                run_performance_test()
            elif choice == 5:
                run_your_board_test()
            else:
                print("❌ 无效选择，请重试")
                
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出程序")
            break

if __name__ == "__main__":
    main()
