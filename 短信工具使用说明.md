# 短信发送工具使用说明

## 概述
这是一个从易语言代码转换而来的Python GUI短信发送工具，支持多个平台的短信验证码发送功能。

## 功能特点

### 1. 安全验证
- 程序启动时需要输入密码（默认：1995）
- 只有通过验证才能使用工具

### 2. 界面布局
- **配置保存**: 可以保存当前配置到文件
- **网址**: 自定义请求的目标URL
- **请求方式**: 选择GET或POST方法
- **提交内容**: POST请求的数据内容
- **协议头**: HTTP请求头信息
- **手机号**: 目标手机号码（所有请求都会用到）

### 3. 快捷触发区域
包含9个预设的短信平台按钮：
- **按钮25 - 大众工商**: 大众工商网短信
- **按钮2 - 索尼音乐**: 索尼音乐短信（需要验证码）
- **按钮28 - 人大金仓**: 人大金仓论坛短信
- **按钮24 - 水果批发**: 水果批发平台短信
- **按钮4 - 水利部**: 水利部网站短信
- **按钮5 - 云标科技**: 云标科技短信
- **按钮23 - 温柔怪物**: 温柔怪物短信
- **按钮8 - 7net**: 7net平台短信（需要验证码）
- **按钮3 - 天启教育**: 天启教育短信

### 4. 操作功能
- **一键发送所有请求**: 自动执行所有9个平台的短信发送
- **发送自定义请求**: 使用自定义参数发送请求
- **日志记录**: 实时显示请求过程和结果
- **配置保存/加载**: 保存常用配置到文件

## 使用步骤

### 基本使用
1. 启动程序，输入密码 `1995`
2. 在"手机号"框中输入目标手机号
3. 点击任意快捷按钮发送对应平台的短信
4. 查看日志了解发送结果

### 一键发送
1. 输入手机号
2. 点击"一键发送所有请求"按钮
3. 程序会自动依次执行所有9个平台的请求
4. 每个请求间隔1秒，避免频率过高

### 自定义请求
1. 填写"网址"、"请求方式"、"提交内容"、"协议头"等信息
2. 在内容中可以使用 `{phone}` 占位符，会自动替换为手机号
3. 通过菜单"请求" -> "发送自定义请求"执行

### 配置管理
- **保存配置**: 菜单"文件" -> "保存配置"
- **加载配置**: 菜单"文件" -> "加载配置"
- 配置文件保存为 `sms_tool_config.json`

## 注意事项

### 安全提醒
1. **合法使用**: 请确保只对自己的手机号或经过授权的号码发送短信
2. **频率控制**: 避免频繁发送，以免被平台限制
3. **隐私保护**: 不要泄露他人手机号信息

### 技术说明
1. **验证码处理**: 部分平台需要图形验证码，目前使用默认值
2. **网络环境**: 需要稳定的网络连接
3. **SSL证书**: 程序忽略SSL证书验证，适用于测试环境

### 错误处理
- 网络请求失败会在日志中显示错误信息
- 超时时间设置为30秒
- 所有操作都在后台线程执行，不会卡死界面

## 文件结构
```
短信发送工具.py          # 主程序文件
短信工具使用说明.md       # 使用说明文档
sms_tool_config.json    # 配置文件（运行后生成）
```

## 依赖库
程序需要以下Python库：
- tkinter (GUI界面)
- requests (HTTP请求)
- json (配置文件处理)
- threading (多线程)
- datetime (时间处理)
- base64 (编码处理)
- urllib.parse (URL编码)

## 安装依赖
```bash
pip install requests
```
注：tkinter通常随Python一起安装

## 运行方式
```bash
python 短信发送工具.py
```

## 版本信息
- 版本: v1.0
- 转换来源: 易语言短信发送工具
- 开发语言: Python 3.x
- GUI框架: tkinter

## 更新日志
- v1.0: 初始版本，完成易语言代码转换
  - 实现9个平台的短信发送功能
  - 添加GUI界面和日志系统
  - 支持配置保存和加载
  - 添加安全验证机制
