#!/bin/bash

echo "Building Tetris Puzzle Game..."

# Check if Qt6 is available
if ! command -v qmake &> /dev/null; then
    echo "Error: qmake not found. Please ensure Qt6 is installed."
    echo "On Ubuntu/Debian: sudo apt install qt6-base-dev qt6-tools-dev"
    echo "On macOS: brew install qt6"
    exit 1
fi

# Function to build with CMake
build_with_cmake() {
    echo "Using CMake build..."
    mkdir -p build
    cd build
    
    if ! cmake .. -DCMAKE_BUILD_TYPE=Release; then
        echo "CMake configuration failed. Trying qmake build..."
        cd ..
        return 1
    fi
    
    if ! cmake --build . --config Release; then
        echo "CMake build failed. Trying qmake build..."
        cd ..
        return 1
    fi
    
    echo "Build successful! Executable is in build/"
    cd ..
    return 0
}

# Function to build with qmake
build_with_qmake() {
    echo "Using qmake build..."
    mkdir -p build-qmake
    cd build-qmake
    
    if ! qmake ../TetrisPuzzleGame.pro; then
        echo "qmake configuration failed."
        cd ..
        return 1
    fi
    
    if ! make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4); then
        echo "Build failed."
        cd ..
        return 1
    fi
    
    echo "Build successful! Executable is in build-qmake/"
    cd ..
    return 0
}

# Try CMake first, then qmake
if command -v cmake &> /dev/null; then
    if build_with_cmake; then
        echo "Build completed successfully with CMake!"
        exit 0
    fi
fi

# Fallback to qmake
if build_with_qmake; then
    echo "Build completed successfully with qmake!"
    exit 0
fi

echo "Build failed. Please check the error messages above."
exit 1
