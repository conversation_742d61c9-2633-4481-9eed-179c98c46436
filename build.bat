@echo off
echo Building Tetris Puzzle Game...

REM Check if Qt6 is available
where qmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: qmake not found. Please ensure Qt6 is installed and in PATH.
    echo You can download Qt6 from: https://www.qt.io/download
    pause
    exit /b 1
)

REM Check if CMake is available
where cmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Warning: CMake not found. Trying qmake build instead...
    goto QMAKE_BUILD
)

REM CMake build
echo Using CMake build...
if not exist build mkdir build
cd build
cmake .. -DCMAKE_BUILD_TYPE=Release
if %ERRORLEVEL% NEQ 0 (
    echo CMake configuration failed. Trying qmake build...
    cd ..
    goto QMAKE_BUILD
)

cmake --build . --config Release
if %ERRORLEVEL% NEQ 0 (
    echo CMake build failed. Trying qmake build...
    cd ..
    goto QMAKE_BUILD
)

echo Build successful! Executable is in build/Release/
cd ..
goto END

:QMAKE_BUILD
echo Using qmake build...
if not exist build-qmake mkdir build-qmake
cd build-qmake
qmake ../TetrisPuzzleGame.pro
if %ERRORLEVEL% NEQ 0 (
    echo qmake configuration failed.
    cd ..
    goto ERROR
)

nmake
if %ERRORLEVEL% NEQ 0 (
    REM Try mingw32-make if nmake fails
    mingw32-make
    if %ERRORLEVEL% NEQ 0 (
        echo Build failed.
        cd ..
        goto ERROR
    )
)

echo Build successful! Executable is in build-qmake/release/
cd ..
goto END

:ERROR
echo Build failed. Please check the error messages above.
pause
exit /b 1

:END
echo Build completed successfully!
echo You can now run the game.
pause
