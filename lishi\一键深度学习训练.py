#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键深度学习拼图破解器训练脚本
自动检查环境、安装依赖、训练模型直到达到90%成功率
"""

import os
import sys
import subprocess
import time
import torch

def check_gpu():
    """检查GPU可用性"""
    print("🖥️ 硬件检测")
    print("=" * 50)
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name(0)
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        cuda_version = torch.version.cuda
        
        print(f"✅ GPU可用: {gpu_name}")
        print(f"📊 显存: {gpu_memory:.1f}GB")
        print(f"🔥 CUDA版本: {cuda_version}")
        
        # 检查显存是否足够
        if gpu_memory < 2.0:
            print("⚠️ 显存较小，建议使用较小的批次大小")
        
        return True
    else:
        print("❌ GPU不可用")
        print("💡 建议:")
        print("   1. 安装CUDA版本的PyTorch")
        print("   2. 检查NVIDIA驱动")
        print("   3. 或使用CPU训练（速度较慢）")
        return False

def check_dependencies():
    """检查和安装依赖"""
    print("\n📦 检查依赖包")
    print("=" * 50)

    required_packages = {
        'torch': 'torch',
        'numpy': 'numpy',
        'matplotlib': 'matplotlib',
        'tqdm': 'tqdm'
    }

    # 检查PyTorch版本兼容性
    try:
        import torch
        torch_version = torch.__version__
        print(f"📦 PyTorch版本: {torch_version}")

        # 检查学习率调度器兼容性
        from torch.optim.lr_scheduler import ReduceLROnPlateau
        import torch.optim as optim

        model = torch.nn.Linear(1, 1)
        optimizer = optim.Adam(model.parameters())

        try:
            _ = ReduceLROnPlateau(optimizer, verbose=True)
            print("✅ 学习率调度器兼容")
        except TypeError as e:
            if "verbose" in str(e):
                print("⚠️ 检测到学习率调度器兼容性问题")
                print("🔧 建议运行: python 环境兼容性检查.py")

    except ImportError:
        print("❌ PyTorch未安装")
    except Exception as e:
        print(f"⚠️ PyTorch兼容性检查失败: {e}")
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(pip_name)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n🔧 需要安装: {', '.join(missing_packages)}")
        choice = input("是否自动安装? (y/n): ").lower().strip()
        
        if choice == 'y':
            try:
                for package in missing_packages:
                    print(f"正在安装 {package}...")
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print("✅ 所有依赖安装完成")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ 安装失败: {e}")
                return False
        else:
            print("请手动安装缺失的依赖包")
            return False
    
    print("✅ 所有依赖已满足")
    return True

def setup_training_environment():
    """设置训练环境"""
    print("\n🔧 设置训练环境")
    print("=" * 50)
    
    # 创建必要的目录
    os.makedirs('models', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    
    print("✅ 目录结构创建完成")
    
    # 设置PyTorch优化
    if torch.cuda.is_available():
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        print("✅ GPU优化设置完成")
    
    return True

def run_training(target_accuracy=0.90):
    """运行训练"""
    print(f"\n🚀 开始深度学习训练")
    print("=" * 50)
    print(f"🎯 目标准确率: {target_accuracy*100:.1f}%")
    print(f"📊 训练范围: 3x3 到 8x8 棋盘")
    print(f"🔄 持续训练直到达到目标")
    
    try:
        # 导入训练模块
        from GPU深度学习训练 import DeepPuzzleTrainer
        
        # 创建训练器
        trainer = DeepPuzzleTrainer(target_accuracy=target_accuracy)
        
        # 开始训练
        success = trainer.train_until_target()
        
        if success:
            print("\n🎉 训练成功完成!")
            print("📁 最佳模型: best_puzzle_model.pth")
            print("📊 训练历史: training_history.json")
            print("📈 训练曲线: training_curves.png")
            return True
        else:
            print("\n⚠️ 未完全达到目标，但已保存最佳模型")
            return False
            
    except ImportError as e:
        print(f"❌ 导入训练模块失败: {e}")
        print("💡 请确保 GPU深度学习训练.py 和 深度学习拼图破解器.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False

def test_trained_model():
    """测试训练好的模型"""
    print("\n🧪 测试训练好的模型")
    print("=" * 50)
    
    try:
        from 深度学习拼图破解器 import DeepPuzzleSolver
        
        # 创建求解器
        solver = DeepPuzzleSolver("best_puzzle_model.pth")
        
        # 测试用例
        test_cases = [
            {
                'name': '3x3 基础测试',
                'board': [[1, 0, 0], [0, 0, 0], [0, 0, 2]],
                'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
            },
            {
                'name': '4x4 中等测试', 
                'board': [[1, 0, 0, 1], [0, 0, 0, 0], [0, 0, 0, 0], [2, 0, 0, 2]],
                'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
            },
            {
                'name': '5x5 复杂测试',
                'board': [[0, 1, 0, 0, 0], [0, 0, 0, 1, 0], [2, 0, 0, 0, 0], [0, 0, 1, 0, 2], [0, 0, 0, 0, 0]],
                'pieces': {"T": 1, "田": 1, "横杠竖条": 1, "Z": 0, "L": 0}
            },
            {
                'name': '6x6 高难度测试',
                'board': [[1, 0, 0, 0, 0, 1], [0, 0, 0, 0, 0, 0], [0, 0, 2, 2, 0, 0], [0, 0, 2, 2, 0, 0], [0, 0, 0, 0, 0, 0], [1, 0, 0, 0, 0, 1]],
                'pieces': {"T": 2, "田": 1, "横杠竖条": 1, "Z": 1, "L": 1}
            }
        ]
        
        success_count = 0
        total_time = 0
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {test_case['name']}")
            
            start_time = time.time()
            solution_steps, _ = solver.solve(test_case['board'], test_case['pieces'])
            solve_time = time.time() - start_time
            total_time += solve_time
            
            if solution_steps:
                success_count += 1
                print(f"✅ 成功! {len(solution_steps)}步, {solve_time*1000:.1f}ms")
            else:
                print(f"❌ 失败, {solve_time*1000:.1f}ms")
        
        success_rate = success_count / len(test_cases)
        avg_time = total_time / len(test_cases)
        
        print(f"\n📊 测试结果:")
        print(f"成功率: {success_count}/{len(test_cases)} ({success_rate*100:.1f}%)")
        print(f"平均时间: {avg_time*1000:.1f}ms")
        
        return success_rate >= 0.75  # 75%以上认为测试通过
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_usage_guide():
    """创建使用指南"""
    guide_content = """# 🧠 深度学习拼图破解器使用指南

## 📁 生成的文件

### 模型文件
- `best_puzzle_model.pth` - 训练好的最佳模型
- `training_history.json` - 训练历史数据
- `training_curves.png` - 训练曲线图

### 使用方法

#### 1. Python中使用
```python
from 深度学习拼图破解器 import DeepPuzzleSolver

# 创建求解器
solver = DeepPuzzleSolver("best_puzzle_model.pth")

# 求解拼图
board = [[1, 0, 0], [0, 0, 0], [0, 0, 2]]
pieces = {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}

solution_steps, final_board = solver.solve(board, pieces)

# 获取提示
hint = solver.get_hint(board, pieces)
print(hint['message'])
```

#### 2. 网页中集成
将模型集成到您的网页拼图游戏中，提供AI智能求解功能。

## 🎯 性能指标
- 支持棋盘大小: 3x3 到 8x8
- 目标成功率: ≥90%
- 平均求解时间: <200ms (GPU) / <1s (CPU)
- 支持所有方块类型: T, 田, 横杠竖条, Z, L

## 🔧 优化建议
1. 使用GPU可显著提升训练和推理速度
2. 增加训练数据可提高模型泛化能力
3. 调整网络结构可适应特定拼图类型
"""
    
    with open('深度学习使用指南.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("📖 使用指南已生成: 深度学习使用指南.md")

def main():
    """主函数"""
    print("🧠 一键深度学习拼图破解器训练")
    print("🎯 目标: 训练到90%成功率")
    print("📊 支持: 3x3到8x8棋盘")
    print("=" * 60)
    
    # 1. 检查GPU
    gpu_available = check_gpu()
    if not gpu_available:
        choice = input("\n是否继续使用CPU训练? (y/n): ").lower().strip()
        if choice != 'y':
            print("👋 训练已取消")
            return
    
    # 2. 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，无法继续")
        return
    
    # 3. 设置环境
    if not setup_training_environment():
        print("❌ 环境设置失败，无法继续")
        return
    
    # 4. 开始训练
    print(f"\n{'='*60}")
    print("🚀 开始训练阶段")
    print(f"{'='*60}")
    
    target_acc = 0.90
    training_success = run_training(target_acc)
    
    # 5. 测试模型
    if training_success:
        print(f"\n{'='*60}")
        print("🧪 开始测试阶段") 
        print(f"{'='*60}")
        
        test_success = test_trained_model()
        
        if test_success:
            print("\n🎉 恭喜！深度学习模型训练和测试都成功完成！")
            print("✅ 模型已达到预期性能")
        else:
            print("\n⚠️ 模型训练完成，但测试性能有待提升")
            print("💡 建议继续训练或调整参数")
    
    # 6. 生成使用指南
    create_usage_guide()
    
    # 7. 总结
    print(f"\n{'='*60}")
    print("📋 训练完成总结")
    print(f"{'='*60}")
    print("📁 生成的文件:")
    print("   - best_puzzle_model.pth (最佳模型)")
    print("   - training_history.json (训练历史)")
    print("   - training_curves.png (训练曲线)")
    print("   - 深度学习使用指南.md (使用说明)")
    
    print("\n🎮 下一步:")
    print("   1. 查看训练曲线了解训练过程")
    print("   2. 使用模型进行拼图求解")
    print("   3. 集成到您的网页应用中")
    
    print("\n👋 训练流程完成！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")
