<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <a href="http://192.168.2.201/cainiao668.html" class="button rotate-button">导航页</a>
    <a href="http://192.168.2.201/" class="button">留言板</a>
    <a href="http://192.168.2.201/cainiao665.html" class="button">新款破解难的</a>
    <a href="http://192.168.2.201/cainiao661.html" class="button">旧随机</a>
    <a href="http://192.168.2.201/cainiao662.html" class="button">旧固定</a>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机旋转 -概率版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 10px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            display: flex;
            gap: 15px;
        }

        .control-panel {
            width: 280px;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
            overflow-y: auto;
            max-height: 90vh;
        }

        /* 悬浮框样式 */
        .floating-controls {
            position: fixed;
            top: 15px;
            right: 15px;
            background-color: white;
            padding: 8px;
            border-radius: 6px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            min-width: 140px;
            font-size: 11px;
        }

        .floating-title {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 6px;
            color: #333;
            text-align: center;
        }

        .floating-controls .form-group {
            margin-bottom: 6px;
        }

        .floating-controls .form-group label {
            font-size: 10px;
            margin-bottom: 2px;
            display: block;
        }

        .floating-controls select {
            width: 100%;
            padding: 2px;
            font-size: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        /* 数量选择按钮样式 */
        .count-buttons {
            display: flex;
            gap: 2px;
            margin: 2px 0;
        }

        .count-btn {
            flex: 1;
            padding: 2px;
            border: 1px solid #ddd;
            border-radius: 3px;
            background-color: #f9f9f9;
            cursor: pointer;
            font-size: 9px;
            font-weight: bold;
            text-align: center;
            transition: all 0.2s;
            min-width: 16px;
            height: 18px;
            line-height: 14px;
        }

        .count-btn:hover {
            background-color: #e9e9e9;
        }

        .count-btn.active {
            background-color: #4CAF50;
            color: white;
            border-color: #45a049;
        }

        .main-game-area {
            width: 420px;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .solutions-area {
            flex: 1;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            max-height: 90vh;
        }

        .section-title {
            font-size: 12px;
            font-weight: bold;
            margin: 15px 0 5px 0;
            color: #333;
        }

        .section-title:first-child {
            margin-top: 0;
        }

        .form-group {
            margin: 5px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .form-group label {
            width: 100px;
            font-size: 10px;
            text-align: left;
        }

        .form-group input, .form-group select {
            width: 60px;
            padding: 2px 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 10px;
        }

        .import-textarea {
            width: 100%;
            height: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 9px;
            font-family: monospace;
            resize: vertical;
        }

        .checkbox-group {
            margin: 5px 0;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 5px;
        }

        .checkbox-group label {
            font-size: 10px;
        }

        .button-row {
            display: flex;
            gap: 3px;
            margin: 5px 0;
        }

        .button-row button {
            flex: 1;
            padding: 6px 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 8px;
            font-weight: bold;
            height: 28px;
        }

        .btn-start { background-color: lightgreen; }
        .btn-clear { background-color: orange; }
        .btn-import { background-color: yellow; }
        .btn-export { background-color: pink; }
        .btn-backtrack { background-color: lightblue; }
        .btn-greedy { background-color: lightcyan; }
        .btn-heuristic { background-color: lightyellow; }
        .btn-random { background-color: lightpink; }
        .btn-constraint { background-color: lightsteelblue; }
        .btn-stop { background-color: lightcoral; }

        .status-panel {
            margin: 10px 0;
        }

        .status-item {
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 10px;
            text-align: center;
            min-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-main { background-color: lightyellow; min-height: 30px; }
        .status-steps { background-color: lightgray; }
        .status-piece { background-color: lightsteelblue; }
        .status-algorithm { background-color: lavender; }

        .info-text {
            background-color: lightyellow;
            padding: 8px;
            border-radius: 4px;
            font-size: 8px;
            line-height: 1.2;
            margin-top: 10px;
            white-space: pre-line;
        }

        .game-board {
            display: inline-block;
            border: 2px solid #333;
            background-color: #f9f9f9;
        }

        .board-row {
            display: flex;
        }

        .board-cell {
            width: 35px;
            height: 35px;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
            font-weight: bold;
            transition: all 0.1s;
        }

        .board-cell:hover {
            opacity: 0.8;
        }

        .cell-empty { background-color: lightblue; }
        .cell-required { background-color: red; color: white; }
        .cell-forbidden { background-color: gray; color: white; }
        .cell-piece { color: white; }

        .mode-indicator {
            font-size: 12px;
            margin: 10px 0;
            padding: 5px;
            border-radius: 4px;
            background-color: #e3f2fd;
        }

        .mode-show { color: blue; }
        .mode-fast { color: red; }

        /* 统计面板样式 */
        .statistics-panel {
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stats-title {
            font-size: 14px;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat-item {
            background-color: white;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }

        .stat-label {
            font-size: 10px;
            color: #6c757d;
            margin-top: 2px;
        }

        .probability-heatmap {
            margin-top: 15px;
        }

        .heatmap-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
        }

        .heatmap-board {
            display: inline-block;
            border: 2px solid #333;
            background-color: #fff;
        }

        .heatmap-cell {
            width: 25px;
            height: 25px;
            border: 0.5px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
        }

        /* 热力图颜色 */
        .heat-0 { background-color: #f8f9fa; color: #6c757d; }
        .heat-1 { background-color: #e3f2fd; color: #1976d2; }
        .heat-2 { background-color: #bbdefb; color: #1565c0; }
        .heat-3 { background-color: #90caf9; color: #1565c0; }
        .heat-4 { background-color: #64b5f6; color: #0d47a1; }
        .heat-5 { background-color: #42a5f5; color: #0d47a1; }
        .heat-6 { background-color: #2196f3; color: white; }
        .heat-7 { background-color: #1e88e5; color: white; }
        .heat-8 { background-color: #1976d2; color: white; }
        .heat-9 { background-color: #1565c0; color: white; }
        .heat-10 { background-color: #0d47a1; color: white; }

        /* 解决方案展示区域 */
        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
            max-height: 400px;
            overflow-y: auto;
        }

        .solution-item {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background-color: #fafafa;
            transition: all 0.3s;
        }

        .solution-item.highlight {
            border-color: #4CAF50;
            background-color: #e8f5e8;
            transform: scale(1.02);
        }

        .solution-header {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
            color: #333;
        }

        .solution-board {
            display: inline-block;
            border: 1px solid #666;
            background-color: #fff;
        }

        .solution-board .board-row {
            display: flex;
        }

        .solution-board .board-cell {
            width: 20px;
            height: 20px;
            border: 0.5px solid #ccc;
            font-size: 8px;
        }

        .solution-info {
            font-size: 9px;
            margin-top: 5px;
            text-align: center;
            color: #666;
        }

        .rank-badge {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #2196F3;
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 10px;
            font-weight: bold;
            margin-right: 5px;
        }

        .rank-1 { background-color: #FFD700; }
        .rank-2 { background-color: #C0C0C0; }
        .rank-3 { background-color: #CD7F32; }

        .solutions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .solutions-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .solutions-stats {
            font-size: 11px;
            color: #666;
        }

        .hidden {
            display: none;
        }

        /* 进度条样式 */
        .progress-container {
            margin: 10px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 10px;
            font-weight: bold;
            color: #333;
        }

        @media (max-width: 1200px) {
            .container {
                flex-direction: column;
            }
            .control-panel, .main-game-area {
                width: 100%;
                max-height: none;
            }
            .solutions-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 悬浮控制面板 -->
        <div class="floating-controls">
            <div class="floating-title">棋盘设置</div>
            <div class="form-group">
                <label>棋盘大小:</label>
                <select id="boardSize">
                    <option value="3">3x3</option>
                    <option value="4">4x4</option>
                    <option value="5">5x5</option>
                    <option value="6">6x6</option>
                    <option value="7" selected>7x7</option>
                    <option value="8">8x8</option>
                    <option value="9">9x9</option>
                </select>
            </div>

            <div class="floating-title">方块数量设置</div>
            <div class="form-group">
                <label>T:</label>
                <div class="count-buttons">
                    <div class="count-btn" data-piece="T" data-count="0">0</div>
                    <div class="count-btn active" data-piece="T" data-count="1">1</div>
                    <div class="count-btn" data-piece="T" data-count="2">2</div>
                </div>
            </div>
            <div class="form-group">
                <label>田:</label>
                <div class="count-buttons">
                    <div class="count-btn" data-piece="田" data-count="0">0</div>
                    <div class="count-btn active" data-piece="田" data-count="1">1</div>
                    <div class="count-btn" data-piece="田" data-count="2">2</div>
                </div>
            </div>
            <div class="form-group">
                <label>横杠竖条:</label>
                <div class="count-buttons">
                    <div class="count-btn" data-piece="横杠竖条" data-count="0">0</div>
                    <div class="count-btn active" data-piece="横杠竖条" data-count="1">1</div>
                    <div class="count-btn" data-piece="横杠竖条" data-count="2">2</div>
                </div>
            </div>
            <div class="form-group">
                <label>Z:</label>
                <div class="count-buttons">
                    <div class="count-btn" data-piece="Z" data-count="0">0</div>
                    <div class="count-btn active" data-piece="Z" data-count="1">1</div>
                    <div class="count-btn" data-piece="Z" data-count="2">2</div>
                </div>
            </div>
            <div class="form-group">
                <label>L:</label>
                <div class="count-buttons">
                    <div class="count-btn" data-piece="L" data-count="0">0</div>
                    <div class="count-btn active" data-piece="L" data-count="1">1</div>
                    <div class="count-btn" data-piece="L" data-count="2">2</div>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">

            <div class="section-title">随机旋转 -概率版</div>
            <div class="checkbox-group">
                <input type="checkbox" id="showProcess">
                <label for="showProcess">显示破解过程</label>
            </div>

            <div class="section-title">基本操作:</div>
            <div class="button-row">
                <button class="btn-start" onclick="startMarking()">开始标注</button>
                <button class="btn-clear" onclick="clearAllMarks()">清除标记</button>
            </div>

            <div class="section-title">导入棋盘:</div>
            <textarea class="import-textarea" id="importText" placeholder="在此粘贴棋盘数据..."></textarea>
            <div class="button-row">
                <button class="btn-import" onclick="importBoard()">导入棋盘</button>
                <button class="btn-export" onclick="exportBoard()">复制棋盘</button>
            </div>

            <div class="section-title">破解算法:</div>
            <div class="button-row">
                <button class="btn-backtrack" onclick="solvePuzzle('回溯法')">回溯法</button>
                <button class="btn-stop" onclick="stopSolving()">停止破解</button>
            </div>

            <div class="section-title">破解状态:</div>
            <div class="status-panel">
                <div class="status-item status-main" id="statusMain">等待中...</div>
                <div class="status-item status-steps" id="statusSteps">步数: 0</div>
                <div class="status-item status-piece" id="statusPiece">当前方块: 无</div>
                <div class="status-item status-algorithm" id="statusAlgorithm">算法: 未选择</div>
            </div>

            <!-- 新增进度条 -->
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                    <div class="progress-text" id="progressText">等待开始...</div>
                </div>
            </div>

            <div class="info-text">左键</div>
        </div>

        <!-- 主游戏区域 -->
        <div class="main-game-area">
            <h1>随机旋转 -概率版</h1>
            <div class="mode-indicator" id="modeIndicator">
                <span id="modeText" class="mode-show">模式: 显示破解过程</span>
            </div>
            <div id="gameBoard" class="game-board">
                <!-- 棋盘将在这里动态生成 -->
            </div>
        </div>

        <!-- 解决方案展示区域 -->
        <div class="solutions-area">
            <!-- 统计面板 -->
            <div class="statistics-panel">
                <div class="stats-title">📊 解决方案统计</div>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="totalSolutions">0</div>
                        <div class="stat-label">原始方案数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="validSolutions">0</div>
                        <div class="stat-label">完整解决方案</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="duplicatesRemoved">0</div>
                        <div class="stat-label">去重方案数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="averageCoverage">0%</div>
                        <div class="stat-label">平均覆盖率</div>
                    </div>
                </div>
                
                <div class="probability-heatmap">
                    <div class="heatmap-title">🔥 位置填充概率热力图</div>
                    <div id="probabilityHeatmap" class="heatmap-board">
                        <!-- 概率热力图将在这里生成 -->
                    </div>
                </div>
            </div>

            <div class="solutions-header">
                <div class="solutions-title">✅ 完整解决方案展示</div>
                <div class="solutions-stats" id="solutionsStats">找到: 0 个完整解决方案</div>
            </div>
            <div class="solutions-grid" id="solutionsGrid">
                <!-- 解决方案将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 游戏类
        class TetrisPuzzleGame {
            constructor() {
                this.boardSize = 7;
                this.board = [];
                this.solutionBoard = [];
                this.solving = false;
                this.solveStep = 0;
                this.showProcess = false;
                this.currentAlgorithm = "回溯法";
                
                // 修改解决方案存储策略
                this.rawSolutions = []; // 存储所有原始解决方案（包括重复的）
                this.uniqueSolutions = []; // 存储去重后的解决方案
                this.completeSolutions = []; // 存储完整的解决方案
                this.duplicatesFound = 0; // 重复方案数量
                
                // 概率统计数据
                this.cellCoverage = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                
                // 俄罗斯方块定义
                this.tetrisShapes = {
                    "T": [
                        [[0,1], [1,0], [1,1], [1,2]],  // T形状 - 上
                        [[0,1], [1,1], [1,2], [2,1]],  // T形状 - 右
                        [[0,0], [1,0], [1,1], [2,0]],  // T形状 - 右
                        [[0,0], [0,1], [0,2], [1,1]],  // T形状 - 下
                        [[0,1], [1,0], [1,1], [2,1]]   // T形状 - 左
                    ],
                    
                    "田": [
                        [[0,0], [0,1], [1,0], [1,1]]   // O形状（方块）
                    ],
                    
                    "横杠竖条": [
                        [[0,0], [0,1], [0,2], [0,3]],  // I形状 - 水平
                        [[0,0], [1,0], [2,0], [3,0]]   // I形状 - 垂直
                    ],
                    
                    "Z": [
                        [[0,0], [0,1], [1,1], [1,2]],  // Z形状 - 0度（水平）
                        [[0,1], [1,0], [1,1], [2,0]],  // Z形状 - 90度（垂直）
                        [[0,1], [0,2], [1,0], [1,1]],  // S形状 - 0度（水平）
                        [[0,0], [1,0], [1,1], [2,1]]   // S形状 - 90度（垂直）
                    ],
                    
                    "L": [
                        [[0,0], [1,0], [2,0], [2,1]],  // L形状 - 0度
                        [[0,1], [1,1], [2,1], [2,0]],  // J形状 - 0度
                        [[0,0], [1,0], [1,1], [1,2]],  // J形状 - 90度
                        [[0,0], [0,1], [1,0], [2,0]],  // J形状 - 180度
                        [[0,0], [0,1], [0,2], [1,2]]   // J形状 - 270度
                    ]
                };
                
                // 方块数量
                this.pieceCounts = {
                    "T": 1,
                    "田": 1,
                    "横杠竖条": 1,
                    "Z": 1,
                    "L": 1
                };
                
                // 方块颜色映射
                this.pieceColors = {
                    "T": "purple",
                    "田": "orange",
                    "横杠竖条": "cyan", 
                    "Z": "red",
                    "L": "blue"
                };
                
                this.initBoard();
                this.setupEventListeners();
                this.createBoard();
                this.initSolutionsDisplay();
            }
            
            initBoard() {
                this.board = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                this.solutionBoard = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                this.cellCoverage = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
            }
            
            setupEventListeners() {
                document.getElementById('boardSize').addEventListener('change', () => this.changeBoardSize());
                document.getElementById('showProcess').addEventListener('change', () => this.toggleProcessDisplay());

                // 添加方块数量按钮事件监听
                document.querySelectorAll('.count-btn').forEach(btn => {
                    btn.addEventListener('click', () => this.handleCountButtonClick(btn));
                });
            }

            handleCountButtonClick(clickedBtn) {
                const piece = clickedBtn.dataset.piece;
                const count = parseInt(clickedBtn.dataset.count);

                // 移除同组其他按钮的active类
                document.querySelectorAll(`[data-piece="${piece}"]`).forEach(btn => {
                    btn.classList.remove('active');
                });

                // 添加active类到点击的按钮
                clickedBtn.classList.add('active');

                // 更新方块数量
                this.pieceCounts[piece] = count;
            }
            
            changeBoardSize() {
                this.boardSize = parseInt(document.getElementById('boardSize').value);
                this.initBoard();
                this.createBoard();
                this.clearSolutions();
            }
            
            toggleProcessDisplay() {
                this.showProcess = document.getElementById('showProcess').checked;
                const modeText = document.getElementById('modeText');
                if (this.showProcess) {
                    modeText.textContent = "模式: 显示破解过程";
                    modeText.className = "mode-show";
                } else {
                    modeText.textContent = "模式: 快速破解";
                    modeText.className = "mode-fast";
                }
            }
            
            createBoard() {
                const gameBoard = document.getElementById('gameBoard');
                gameBoard.innerHTML = '';
                
                for (let i = 0; i < this.boardSize; i++) {
                    const row = document.createElement('div');
                    row.className = 'board-row';
                    
                    for (let j = 0; j < this.boardSize; j++) {
                        const cell = document.createElement('div');
                        cell.className = 'board-cell cell-empty';
                        cell.dataset.row = i;
                        cell.dataset.col = j;
                        
                        cell.addEventListener('click', () => this.onLeftClick(i, j));
                        cell.addEventListener('contextmenu', (e) => {
                            e.preventDefault();
                            this.onRightClick(i, j);
                        });
                        cell.addEventListener('dblclick', () => this.onDoubleClick(i, j));
                        
                        row.appendChild(cell);
                    }
                    
                    gameBoard.appendChild(row);
                }
                
                this.updateBoardDisplay();
            }
            
            initSolutionsDisplay() {
                this.clearSolutions();
            }
            
            // 生成棋盘状态的唯一哈希
            generateBoardHash(board) {
                return JSON.stringify(board);
            }

            // 检查解决方案是否完整
            isCompleteSolution(board, piecesUsed) {
                // 1. 检查所有必需位置是否被覆盖
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] === 1 && board[i][j] === 0) {
                            return false; // 必需位置未被覆盖
                        }
                        if (this.board[i][j] === 2 && board[i][j] !== 0) {
                            return false; // 禁止位置被覆盖
                        }
                    }
                }

                // 2. 检查是否所有方块都被使用
                const requiredPieces = Object.entries(this.pieceCounts)
                    .filter(([name, count]) => count > 0);
                
                if (requiredPieces.length === 0) {
                    return false; // 没有设置任何方块
                }

                for (const [pieceName, requiredCount] of requiredPieces) {
                    const usedCount = piecesUsed[pieceName] || 0;
                    if (usedCount !== requiredCount) {
                        return false; // 方块使用数量不匹配
                    }
                }

                return true;
            }

            // 计算使用的方块数量
            calculateUsedPieces(board) {
                const usedPieces = {};
                const pieceNames = Object.keys(this.tetrisShapes);
                
                // 初始化计数器
                pieceNames.forEach(name => {
                    usedPieces[name] = 0;
                });

                // 统计每种方块的使用次数
                const pieceIds = new Set();
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (board[i][j] > 2) {
                            pieceIds.add(board[i][j]);
                        }
                    }
                }

                // 根据方块ID计算使用的方块类型
                pieceIds.forEach(pieceId => {
                    const pieceIdx = pieceId - 3;
                    if (pieceIdx < pieceNames.length) {
                        const pieceName = pieceNames[pieceIdx];
                        usedPieces[pieceName]++;
                    }
                });

                return usedPieces;
            }

            // 清除解决方案时也重置统计
            clearSolutions() {
                this.rawSolutions = [];
                this.uniqueSolutions = [];
                this.completeSolutions = [];
                this.duplicatesFound = 0;
                this.cellCoverage = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                
                const grid = document.getElementById('solutionsGrid');
                grid.innerHTML = '';
                document.getElementById('solutionsStats').textContent = '找到: 0 个完整解决方案';
                this.updateStatistics();
                this.updateProgress(0, "等待开始...");
            }
            
            // 评估解决方案的质量
            evaluateSolution(board) {
                let score = 0;
                let filledCells = 0;
                let requiredCovered = 0;
                let forbiddenViolated = 0;
                
                // 计算基本指标
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (board[i][j] > 0) {
                            filledCells++;
                        }
                        if (this.board[i][j] === 1 && board[i][j] > 0) {
                            requiredCovered++;
                        }
                        if (this.board[i][j] === 2 && board[i][j] > 0) {
                            forbiddenViolated++;
                        }
                    }
                }
                
                // 计算评分
                score += requiredCovered * 100; // 覆盖必需位置得高分
                score -= forbiddenViolated * 1000; // 违反禁止位置扣重分
                score += filledCells * 10; // 填充更多格子得分
                
                // 计算连续性奖励（相邻的同类型方块）
                let continuityBonus = 0;
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (board[i][j] > 0) {
                            if (i > 0 && board[i-1][j] === board[i][j]) continuityBonus++;
                            if (j > 0 && board[i][j-1] === board[i][j]) continuityBonus++;
                        }
                    }
                }
                score += continuityBonus * 5;
                
                return {
                    score,
                    filledCells,
                    requiredCovered,
                    forbiddenViolated,
                    isValid: this.isValidSolution(board)
                };
            }
            
            // 修改：在搜索过程中直接添加原始解决方案，不进行去重
            addRawSolution(board, algorithm, step, piecesUsed = null) {
                const boardCopy = board.map(row => [...row]);
                
                // 计算使用的方块（如果没有提供）
                if (!piecesUsed) {
                    piecesUsed = this.calculateUsedPieces(boardCopy);
                }

                // 评估解决方案
                const evaluation = this.evaluateSolution(boardCopy);
                const isComplete = this.isCompleteSolution(boardCopy, piecesUsed);

                const solution = {
                    board: boardCopy,
                    algorithm: algorithm,
                    step: step,
                    timestamp: Date.now(),
                    piecesUsed: piecesUsed,
                    isComplete: isComplete,
                    hash: this.generateBoardHash(boardCopy),
                    ...evaluation
                };

                // 直接添加到原始解决方案列表，不进行去重检查
                this.rawSolutions.push(solution);
            }

            // 新增：处理所有原始解决方案，进行去重和统计
            processAllSolutions() {
                this.updateProgress(0, "开始处理解决方案...");
                
                // 步骤1：去重
                const solutionMap = new Map();
                let processedCount = 0;
                
                for (const solution of this.rawSolutions) {
                    processedCount++;
                    const hash = solution.hash;
                    
                    if (!solutionMap.has(hash)) {
                        solutionMap.set(hash, solution);
                    } else {
                        this.duplicatesFound++;
                    }
                    
                    // 更新进度
                    const progress = (processedCount / this.rawSolutions.length) * 50; // 前50%用于去重
                    this.updateProgress(progress, `去重处理中... ${processedCount}/${this.rawSolutions.length}`);
                }
                
                // 步骤2：分类和排序
                this.uniqueSolutions = Array.from(solutionMap.values());
                this.completeSolutions = this.uniqueSolutions.filter(solution => solution.isComplete);
                
                // 按评分排序完整解决方案
                this.completeSolutions.sort((a, b) => b.score - a.score);
                
                this.updateProgress(75, "计算概率统计中...");
                
                // 步骤3：计算概率统计
                this.cellCoverage = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                
                for (const solution of this.completeSolutions) {
                    for (let i = 0; i < this.boardSize; i++) {
                        for (let j = 0; j < this.boardSize; j++) {
                            if (solution.board[i][j] > 0) {
                                this.cellCoverage[i][j]++;
                            }
                        }
                    }
                }
                
                this.updateProgress(90, "更新显示中...");
                
                // 步骤4：更新显示
                this.updateSolutionsDisplay();
                this.updateStatistics();
                
                this.updateProgress(100, "处理完成！");
                
                // 显示处理结果
                const resultMsg = `
处理完成！
- 找到原始方案: ${this.rawSolutions.length} 个
- 去重后方案: ${this.uniqueSolutions.length} 个  
- 完整解决方案: ${this.completeSolutions.length} 个
- 重复方案: ${this.duplicatesFound} 个
                `.trim();
                
                document.getElementById('statusMain').textContent = "解决方案处理完成！";
                document.getElementById('statusMain').style.backgroundColor = "lightgreen";
            }

            // 新增：更新进度条
            updateProgress(percentage, text) {
                document.getElementById('progressFill').style.width = percentage + '%';
                document.getElementById('progressText').textContent = text;
            }

            // 计算统计信息
            calculateStatistics() {
                const totalComplete = this.completeSolutions.length;
                if (totalComplete === 0) {
                    return {
                        totalSolutions: this.rawSolutions.length,
                        validSolutions: 0,
                        duplicatesRemoved: this.duplicatesFound,
                        averageCoverage: 0,
                        cellProbabilities: Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0))
                    };
                }

                // 计算每个位置的填充概率
                const cellProbabilities = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                let totalCoverage = 0;
                let totalCells = 0;

                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        const probability = this.cellCoverage[i][j] / totalComplete;
                        cellProbabilities[i][j] = probability;
                        totalCoverage += probability;
                        totalCells++;
                    }
                }

                const averageCoverage = (totalCoverage / totalCells) * 100;

                return {
                    totalSolutions: this.rawSolutions.length,
                    validSolutions: totalComplete,
                    duplicatesRemoved: this.duplicatesFound,
                    averageCoverage: averageCoverage.toFixed(1),
                    cellProbabilities: cellProbabilities
                };
            }

            // 更新统计显示
            updateStatistics() {
                const stats = this.calculateStatistics();
                
                document.getElementById('totalSolutions').textContent = stats.totalSolutions;
                document.getElementById('validSolutions').textContent = stats.validSolutions;
                document.getElementById('duplicatesRemoved').textContent = stats.duplicatesRemoved;
                document.getElementById('averageCoverage').textContent = stats.averageCoverage + '%';
                
                this.updateProbabilityHeatmap(stats.cellProbabilities);
            }

            // 更新概率热力图
            updateProbabilityHeatmap(probabilities) {
                const heatmapContainer = document.getElementById('probabilityHeatmap');
                heatmapContainer.innerHTML = '';

                for (let i = 0; i < this.boardSize; i++) {
                    const row = document.createElement('div');
                    row.className = 'board-row';
                    
                    for (let j = 0; j < this.boardSize; j++) {
                        const cell = document.createElement('div');
                        cell.className = 'heatmap-cell';
                        
                        const probability = probabilities[i][j];
                        const percentage = Math.round(probability * 100);
                        const heatLevel = Math.min(10, Math.floor(probability * 10));
                        
                        cell.classList.add(`heat-${heatLevel}`);
                        cell.textContent = percentage > 0 ? percentage + '%' : '';
                        cell.title = `位置 (${i},${j}): ${percentage}% 概率被填充`;
                        
                        row.appendChild(cell);
                    }
                    
                    heatmapContainer.appendChild(row);
                }
            }

            // 更新解决方案显示 - 修改为只显示前10个
            updateSolutionsDisplay() {
                const grid = document.getElementById('solutionsGrid');
                grid.innerHTML = '';
                
                const completeCount = this.completeSolutions.length;
                const displayCount = Math.min(completeCount, 10); // 最多显示10个
                
                // 更新统计文本，显示总数和展示数量
                document.getElementById('solutionsStats').textContent = 
                    `找到: ${completeCount} 个完整解决方案 (显示前${displayCount}个) (原始 ${this.rawSolutions.length} 个，去重 ${this.duplicatesFound} 个)`;
                
                // 只显示前10个完整的解决方案
                this.completeSolutions.slice(0, 10).forEach((solution, index) => {
                    const solutionDiv = document.createElement('div');
                    solutionDiv.className = 'solution-item highlight';
                    
                    // 创建排名徽章
                    const rankBadge = document.createElement('span');
                    rankBadge.className = `rank-badge rank-${Math.min(index + 1, 3)}`;
                    rankBadge.textContent = index + 1;
                    
                    // 创建标题
                    const header = document.createElement('div');
                    header.className = 'solution-header';
                    header.appendChild(rankBadge);
                    header.appendChild(document.createTextNode(`${solution.algorithm} ✅`));
                    
                    // 创建小棋盘
                    const boardDiv = document.createElement('div');
                    boardDiv.className = 'solution-board';
                    
                    for (let i = 0; i < this.boardSize; i++) {
                        const row = document.createElement('div');
                        row.className = 'board-row';
                        
                        for (let j = 0; j < this.boardSize; j++) {
                            const cell = document.createElement('div');
                            cell.className = 'board-cell';
                            
                            const value = solution.board[i][j];
                            const originalValue = this.board[i][j];
                            
                            if (value === 0) {
                                if (originalValue === 1) {
                                    cell.style.backgroundColor = 'red';
                                    cell.textContent = '✓';
                                    cell.style.color = 'white';
                                } else if (originalValue === 2) {
                                    cell.style.backgroundColor = 'gray';
                                    cell.textContent = '✗';
                                    cell.style.color = 'white';
                                } else {
                                    cell.style.backgroundColor = 'lightblue';
                                }
                            } else {
                                const pieceNames = Object.keys(this.tetrisShapes);
                                const pieceIdx = value - 3;
                                if (pieceIdx < pieceNames.length) {
                                    const pieceName = pieceNames[pieceIdx];
                                    const color = this.pieceColors[pieceName];
                                    cell.style.backgroundColor = color;
                                    cell.textContent = pieceName[0];
                                    cell.style.color = 'white';
                                }
                            }
                            
                            row.appendChild(cell);
                        }
                        
                        boardDiv.appendChild(row);
                    }
                    
                    // 创建详细信息
                    const info = document.createElement('div');
                    info.className = 'solution-info';
                    const usedPiecesText = Object.entries(solution.piecesUsed)
                        .filter(([name, count]) => count > 0)
                        .map(([name, count]) => `${name}:${count}`)
                        .join(' ');
                    
                    info.innerHTML = `
                        评分: ${solution.score}<br>
                        步数: ${solution.step}<br>
                        方块: ${usedPiecesText}<br>
                        填充: ${solution.filledCells}/${this.boardSize * this.boardSize}
                    `;
                    
                    // 点击复制到主棋盘
                    solutionDiv.addEventListener('click', () => {
                        this.copySolutionToMainBoard(solution.board);
                    });
                    
                    solutionDiv.appendChild(header);
                    solutionDiv.appendChild(boardDiv);
                    solutionDiv.appendChild(info);
                    grid.appendChild(solutionDiv);
                });
            }
            
            // 复制解决方案到主棋盘
            copySolutionToMainBoard(solutionBoard) {
                if (this.solving) return;
                
                // 保持原始标记，只更新方块放置
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] <= 2) { // 保持原始标记
                            // 不更改
                        } else {
                            this.board[i][j] = 0; // 清除旧的方块
                        }
                        
                        if (solutionBoard[i][j] > 0) {
                            this.board[i][j] = solutionBoard[i][j];
                        }
                    }
                }
                
                this.updateBoardDisplay();
                
                // 显示状态
                document.getElementById('statusMain').textContent = "已复制解决方案到主棋盘";
                document.getElementById('statusMain').style.backgroundColor = "lightgreen";
            }
            
            onLeftClick(row, col) {
                if (!this.solving) {
                    this.board[row][col] = 1;
                    this.updateCellDisplay(row, col);
                }
            }
            
            onRightClick(row, col) {
                if (!this.solving) {
                    this.board[row][col] = 2;
                    this.updateCellDisplay(row, col);
                }
            }
            
            onDoubleClick(row, col) {
                if (!this.solving) {
                    this.board[row][col] = 0;
                    this.updateCellDisplay(row, col);
                }
            }
            
            updateCellDisplay(row, col) {
                const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                const value = this.board[row][col];
                
                // 清除所有类
                cell.className = 'board-cell';
                cell.style.backgroundColor = '';
                cell.style.color = '';
                
                if (value === 0) {
                    // 初始格子 - 蓝色
                    cell.className += ' cell-empty';
                    cell.textContent = '';
                } else if (value === 1) {
                    // 正确标记 - 红色
                    cell.className += ' cell-required';
                    cell.textContent = '✓';
                } else if (value === 2) {
                    // 错误标记 - 灰色
                    cell.className += ' cell-forbidden';
                    cell.textContent = '✗';
                } else {  // 解答状态 (value >= 3)
                    const pieceNames = Object.keys(this.tetrisShapes);
                    const pieceIdx = value - 3;
                    if (pieceIdx < pieceNames.length) {
                        const pieceName = pieceNames[pieceIdx];
                        const color = this.pieceColors[pieceName];
                        cell.className += ' cell-piece';
                        cell.style.backgroundColor = color;
                        cell.textContent = pieceName[0];  // 显示第一个字符
                    }
                }
            }
            
            updateBoardDisplay() {
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        this.updateCellDisplay(i, j);
                    }
                }
            }
            
            clearAllMarks() {
                if (!this.solving) {
                    // 只清除棋盘，保持解决方案
                    this.initBoard();
                    this.updateBoardDisplay();
                    document.getElementById('statusMain').textContent = "已清除所有标记";
                    document.getElementById('statusPiece').textContent = "当前方块: 无";
                    document.getElementById('statusAlgorithm').textContent = "算法: 未选择";
                }
            }
            
            // 导出棋盘到剪贴板
            async exportBoard() {
                const data = {
                    board_size: this.boardSize,
                    board: this.board,
                    piece_counts: this.pieceCounts,
                    show_process: this.showProcess
                };
                
                const dataStr = JSON.stringify(data, null, 2);
                
                try {
                    await navigator.clipboard.writeText(dataStr);
                    alert("棋盘数据已复制到剪贴板！");
                } catch (err) {
                    // 备用方案：创建临时文本框
                    const textArea = document.createElement('textarea');
                    textArea.value = dataStr;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert("棋盘数据已复制到剪贴板！");
                }
            }
            
            // 从文本导入棋盘
            importBoard() {
                if (this.solving) {
                    alert("正在破解中，请先停止破解！");
                    return;
                }
                
                const importText = document.getElementById('importText').value.trim();
                if (!importText) {
                    alert("请先在文本框中输入棋盘数据！");
                    return;
                }
                
                try {
                    const data = JSON.parse(importText);
                    
                    this.boardSize = data.board_size;
                    this.board = data.board;
                    this.pieceCounts = data.piece_counts;
                    
                    if (data.show_process !== undefined) {
                        this.showProcess = data.show_process;
                        document.getElementById('showProcess').checked = this.showProcess;
                        this.toggleProcessDisplay();
                    }
                    
                    // 更新界面
                    document.getElementById('boardSize').value = this.boardSize;

                    // 更新方块数量按钮状态
                    for (const [name, count] of Object.entries(this.pieceCounts)) {
                        // 移除所有active类
                        document.querySelectorAll(`[data-piece="${name}"]`).forEach(btn => {
                            btn.classList.remove('active');
                        });
                        // 添加active类到对应数量的按钮
                        const targetBtn = document.querySelector(`[data-piece="${name}"][data-count="${count}"]`);
                        if (targetBtn) {
                            targetBtn.classList.add('active');
                        }
                    }

                    this.createBoard();
                    this.clearSolutions();
                    document.getElementById('statusMain').textContent = "棋盘已导入";
                    document.getElementById('statusMain').style.backgroundColor = "lightgreen";
                    document.getElementById('statusPiece').textContent = "当前方块: 无";
                    document.getElementById('statusAlgorithm').textContent = "算法: 未选择";

                    // 清空输入框
                    document.getElementById('importText').value = '';
                } catch (e) {
                    alert(`导入失败：${e.message}`);
                }
            }
            
            // 方块放置检查函数
            canPlacePiece(board, shape, startRow, startCol) {
                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    if (r < 0 || r >= this.boardSize || 
                        c < 0 || c >= this.boardSize || 
                        board[r][c] !== 0) {
                        return false;
                    }
                }
                return true;
            }
            
            placePiece(board, shape, startRow, startCol, pieceId) {
                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    board[r][c] = pieceId;
                }
            }
            
            removePiece(board, shape, startRow, startCol) {
                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    board[r][c] = 0;
                }
            }
            
            isValidPlacement(board, shape, startRow, startCol) {
                let coversRequired = 0;
                let coversForbidden = false;
                
                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    if (this.board[r][c] === 2) {  // 冲突：覆盖了标记为错误的位置
                        coversForbidden = true;
                        break;
                    }
                    if (this.board[r][c] === 1) {  // 覆盖了必需的位置
                        coversRequired++;
                    }
                }
                return [!coversForbidden, coversRequired];
            }
            
            isValidSolution(board) {
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] === 1 && board[i][j] === 0) {
                            return false;
                        }
                        if (this.board[i][j] === 2 && board[i][j] !== 0) {
                            return false;
                        }
                    }
                }
                return true;
            }
            
            async updateSolveDisplay(board, message = "", pieceName = "", forceUpdate = false) {
                if (!this.solving) return;
                
                this.solveStep++;
                document.getElementById('statusSteps').textContent = `步数: ${this.solveStep}`;
                
                if (pieceName) {
                    document.getElementById('statusPiece').textContent = `当前方块: ${pieceName}`;
                }
                
                if (this.showProcess || forceUpdate) {
                    document.getElementById('statusMain').textContent = message || `正在尝试第${this.solveStep}步...`;
                    
                    if (this.showProcess) {
                        // 临时更新主棋盘显示（仅显示过程，不修改原始棋盘）
                        const tempBoard = this.board.map(row => [...row]);
                        for (let i = 0; i < this.boardSize; i++) {
                            for (let j = 0; j < this.boardSize; j++) {
                                if (board[i][j] > 0 && tempBoard[i][j] <= 2) {
                                    tempBoard[i][j] = board[i][j];
                                }
                            }
                        }
                        
                        const oldBoard = this.board;
                        this.board = tempBoard;
                        this.updateBoardDisplay();
                        this.board = oldBoard;
                        
                        await new Promise(resolve => setTimeout(resolve, 30));
                    }
                } else {
                    if (this.solveStep % 100 === 0) {
                        document.getElementById('statusMain').textContent = `快速破解中... (${this.solveStep}步，找到${this.rawSolutions.length}个方案)`;
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
            }
            
            // 修改回溯法：找到所有解决方案
            async solveBacktrack(board, piecesLeft, depth = 0, usedPieces = {}) {
                if (!this.solving) return;
                
                const availablePieces = Object.fromEntries(
                    Object.entries(piecesLeft).filter(([name, count]) => count > 0)
                );
                
                if (Object.keys(availablePieces).length === 0) {
                    // 找到一个完整的解决方案，记录它
                    const actualUsed = this.calculateUsedPieces(board);
                    this.addRawSolution(board, this.currentAlgorithm, this.solveStep, actualUsed);
                    
                    if (this.showProcess) {
                        await this.updateSolveDisplay(board, `找到解决方案 #${this.rawSolutions.length}！`, "", true);
                    }
                    return; // 继续寻找其他解决方案
                }
                
                const pieceName = Object.keys(availablePieces)[0];
                const shapes = this.tetrisShapes[pieceName];
                const pieceId = Object.keys(this.tetrisShapes).indexOf(pieceName) + 3;
                
                if (this.showProcess) {
                    await this.updateSolveDisplay(board, `回溯法尝试放置 ${pieceName}`, pieceName);
                } else {
                    await this.updateSolveDisplay(board, "", pieceName);
                }
                
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        for (let rotationIdx = 0; rotationIdx < shapes.length; rotationIdx++) {
                            const shape = shapes[rotationIdx];
                            
                            if (!this.solving) return;
                            
                            if (this.canPlacePiece(board, shape, i, j)) {
                                const [validPlacement, coversRequired] = this.isValidPlacement(board, shape, i, j);
                                
                                if (validPlacement) {
                                    this.placePiece(board, shape, i, j, pieceId);
                                    
                                    if (this.showProcess) {
                                        await this.updateSolveDisplay(board, `回溯法放置 ${pieceName} 在(${i},${j})`, pieceName);
                                    }
                                    
                                    const newPieces = {...piecesLeft};
                                    newPieces[pieceName]--;
                                    
                                    const newUsedPieces = {...usedPieces};
                                    newUsedPieces[pieceName] = (newUsedPieces[pieceName] || 0) + 1;
                                    
                                    await this.solveBacktrack(board, newPieces, depth + 1, newUsedPieces);
                                    
                                    this.removePiece(board, shape, i, j);
                                    if (this.showProcess) {
                                        await this.updateSolveDisplay(board, `回溯法回溯：移除 ${pieceName}`, pieceName);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 其他算法实现（简化版本，重点展示解决方案收集）
            async solveGreedy(board, piecesLeft) {
                return await this.solveBacktrack(board, piecesLeft);
            }
            
            async solveHeuristic(board, piecesLeft, depth = 0) {
                return await this.solveBacktrack(board, piecesLeft, depth);
            }
            
            async solveRandom(board, piecesLeft, maxAttempts = 1000) {
                return await this.solveBacktrack(board, piecesLeft);
            }
            
            async solveConstraint(board, piecesLeft) {
                return await this.solveBacktrack(board, piecesLeft);
            }
            
            stopSolving() {
                this.solving = false;
                document.getElementById('statusMain').textContent = "破解已停止";
                document.getElementById('statusMain').style.backgroundColor = "lightcoral";
                document.getElementById('statusPiece').textContent = "当前方块: 已停止";
                this.updateProgress(0, "已停止");
            }
            
            async solvePuzzle(algorithm = "回溯法") {
                if (this.solving) return;
                
                this.currentAlgorithm = algorithm;
                
                // 清空之前的解决方案
                this.clearSolutions();
                
                // 更新方块数量（从按钮状态读取）
                ['T', '田', '横杠竖条', 'Z', 'L'].forEach(piece => {
                    const activeBtn = document.querySelector(`[data-piece="${piece}"].active`);
                    this.pieceCounts[piece] = activeBtn ? parseInt(activeBtn.dataset.count) : 1;
                });

                // 检查是否有方块可以放置
                const totalPieces = Object.values(this.pieceCounts).reduce((sum, count) => sum + count, 0);
                if (totalPieces === 0) {
                    alert("请至少设置一个方块的数量大于0！");
                    return;
                }
                
                // 开始破解
                this.solving = true;
                this.solveStep = 0;
                
                // 创建工作棋盘
                const workBoard = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                const piecesLeft = {...this.pieceCounts};
                
                // 设置算法状态
                document.getElementById('statusAlgorithm').textContent = `算法: ${algorithm}`;
                
                if (this.showProcess) {
                    document.getElementById('statusMain').textContent = `开始${algorithm}破解...`;
                    document.getElementById('statusMain').style.backgroundColor = "yellow";
                } else {
                    document.getElementById('statusMain').textContent = `${algorithm}快速破解中...`;
                    document.getElementById('statusMain').style.backgroundColor = "yellow";
                }
                
                document.getElementById('statusPiece').textContent = "当前方块: 准备中...";
                this.updateProgress(0, "开始搜索...");
                
                const startTime = Date.now();
                
                try {
                    // 根据选择的算法调用相应的求解函数
                    switch (algorithm) {
                        case "回溯法":
                            await this.solveBacktrack(workBoard, piecesLeft);
                            break;
                        case "贪心算法":
                            await this.solveGreedy(workBoard, piecesLeft);
                            break;
                        case "启发式搜索":
                            await this.solveHeuristic(workBoard, piecesLeft);
                            break;
                        case "随机搜索":
                            await this.solveRandom(workBoard, piecesLeft);
                            break;
                        case "约束传播":
                            await this.solveConstraint(workBoard, piecesLeft);
                            break;
                        default:
                            await this.solveBacktrack(workBoard, piecesLeft);
                    }
                    
                    const endTime = Date.now();
                    const solveTime = ((endTime - startTime) / 1000).toFixed(2);
                    
                    // 搜索完成，开始处理解决方案
                    document.getElementById('statusMain').textContent = `搜索完成，开始处理解决方案...`;
                    document.getElementById('statusMain').style.backgroundColor = "orange";
                    
                    // 处理所有找到的解决方案
                    this.processAllSolutions();
                    
                } catch (e) {
                    document.getElementById('statusMain').textContent = `${algorithm}破解出错`;
                    document.getElementById('statusMain').style.backgroundColor = "red";
                    document.getElementById('statusPiece').textContent = "当前方块: 错误";
                    alert(`${algorithm}求解过程中出现错误：${e.message}`);
                } finally {
                    this.solving = false;
                }
            }
        }
        
        // 创建游戏实例
        let game;
        
        // 全局函数（供HTML调用）
        function startMarking() {
            alert("开始标注模式！\n左键：标记正确位置(红色)\n右键：标记错误位置(灰色)\n双击：清除标记");
        }
        
        function clearAllMarks() {
            game.clearAllMarks();
        }
        
        function solvePuzzle(algorithm) {
            game.solvePuzzle(algorithm);
        }
        
        function stopSolving() {
            game.stopSolving();
        }
        
        function exportBoard() {
            game.exportBoard();
        }
        
        function importBoard() {
            game.importBoard();
        }
        
        // 页面加载完成后初始化游戏
        document.addEventListener('DOMContentLoaded', function() {
            game = new TetrisPuzzleGame();
        });
    </script>
</body>
</html>