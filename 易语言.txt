.版本 2
.支持库 spec

.程序集 窗口程序集_启动窗口

.子程序 _按钮1_被单击


时间 ＝ 时间_到文本 (, , , )

编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问1 () ＋ #换行符



.子程序 功能_网页访问1, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 ADD_协议头, 类_POST数据类
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.landcloud.org.cn/landCloudWork3/register/sendsmscode?phoneNumber=” ＋ 编辑框1.内容
ADD_协议头.添加 (“Host”, “www.landcloud.org.cn”)
ADD_协议头.添加 (“Connection”, “keep-alive”)
ADD_协议头.添加 (“Cache-Control”, “max-age=0”)
' ADD_协议头.添加 ("sec-ch-ua",""Chromium";v="119", "Not?A_Brand";v="24""）
ADD_协议头.添加 (“Accept”, “application/json”)
ADD_协议头.添加 (“sec_ch_wjw”, “kjnfakjfna8*klsmf^-dad”)
ADD_协议头.添加 (“sec-ch-ua-mobile”, “?0”)
ADD_协议头.添加 (“User-Agent”, “Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36”)
ADD_协议头.添加 (“diu”, “undefined”)
' ADD_协议头.添加 ("sec-ch-ua-platform",""Windows""）
ADD_协议头.添加 (“Sec-Fetch-Site”, “same-origin”)
ADD_协议头.添加 (“Sec-Fetch-Mode”, “cors”)
ADD_协议头.添加 (“Sec-Fetch-Dest”, “empty”)
ADD_协议头.添加 (“Referer”, “https://www.landcloud.org.cn/”)
' ADD_协议头.添加 ("Accept-Encoding","gzip, deflate, br"）' 压缩头 一般不用设置
ADD_协议头.添加 (“Accept-Language”, “zh-CN,zh;q=0.9”)
局_提交协议头 ＝ ADD_协议头.获取协议头数据 ()
局_提交cookie ＝ “SESSION=61e”
局_结果 ＝ 网页_访问_对象 (局_网址, 0, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮2_被单击

时间 ＝ 时间_到文本 (, , , )

编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问2 () ＋ #换行符


.子程序 功能_网页访问2, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型
.局部变量 img_code, 文本型

时间 ＝ 时间_取现行时间戳 ()
图片 ＝ 网页_访问_对象 (“https://music.sonyselect.net/getSMSImageAuthCode?id=0.5046450420530237”, 0, , , cookie, 协议头)
' 答题结果 ＝ 到文本 (网页_访问 (“http://*************:25888”, 1, , , , , , , 图片))
' 调试输出 (答题结果)
' 局_网址 ＝ “https://music.sonyselect.net/getSmsAuthCode”
' 局_提交数据 ＝ “phoneNo=” ＋ 编辑框1.内容 ＋ “&smsAuthCode=” ＋ 答题结果

' 文本 ＝ 到文本 (网页_访问_对象 (“https://h5forphone.wostore.cn/h5forphone/changxiang/getCode?exchangecodeCode=247WAOY949”, 0, , “arp_scroll_position=348”, cookie, #常量5))

' img_code ＝ 文本_取出中间文本 (文本, “base64,”, “#引号”)

' img_code ＝ 文本_替换 (img_code, , , , “\/”, “/”)
' img_code ＝ 文本_替换 (img_code, , , , “\n”, “”)



' 调试输出 (img_code)
' 图片 ＝ 编码_BASE64编码 (图片)



' http://192.168.2.42:19956/ocr

图片框1.图片 ＝ 图片
答题结果 ＝ “data:image/jpeg;base64,” ＋ 编码_BASE64编码 (图片)

' 调试输出 (答题结果)
答题结果 ＝ 编码_URL编码 (答题结果, )

' {#引号#引号image#引号#引号: #引号#引号#引号 ＋ base64编码 ＋ “#引号#引号}
' “{""image"": """ ＋ base64编码 ＋ “""}”

答题结果 ＝ 到文本 (网页_访问 (“http://*************:19956/ocr”, 1, “image=” ＋ 答题结果, , , “Content-Type: application/x-www-form-urlencoded”, , , , ))


调试输出 (答题结果)



答题结果 ＝ 到文本 (网页_访问 (“http://*************:25888”, 1, , , , , , , 图片))
调试输出 (答题结果)

局_网址 ＝ “https://music.sonyselect.net/getSmsAuthCode”
局_提交数据 ＝ “phoneNo=” ＋ 编辑框1.内容 ＋ “&smsAuthCode=” ＋ 答题结果



' 局_提交协议头 = "Host:music.sonyselect.net" ＋ #换行符 ＋ "Connection:keep-alive" ＋ #换行符 ＋ "sec-ch-ua:"Chromium";v=119Not?A_Brand";v="24"" ＋ #换行符 ＋ "Accept:*/*" ＋ #换行符 ＋ "Content-Type:application/x-www-form-urlencoded; charset=UTF-8" ＋ #换行符 ＋ "X-Requested-With:XMLHttpRequest" ＋ #换行符 ＋ "sec-ch-ua-mobile:?0" ＋ #换行符 ＋ "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36" ＋ #换行符 ＋ "sec-ch-ua-platform:"Windows"" ＋ #换行符 ＋ "Origin:https://music.sonyselect.net" ＋ #换行符 ＋ "Sec-Fetch-Site:same-origin" ＋ #换行符 ＋ "Sec-Fetch-Mode:cors" ＋ #换行符 ＋ "Sec-Fetch-Dest:empty" ＋ #换行符 ＋ "Referer:https://music.sonyselect.net/" ＋ #换行符 ＋ "Accept-Encoding:gzip, deflate, br" ＋ #换行符 ＋ "Accept-Language:zh-CN,zh;q=0.9"
' 局_提交cookie ＝ “origin=direct; JSESSIONID=0868577932AEC2B31B4E3456753BA294; Hm_lvt_13d91d128b66b17ad38ffa796197c95f=1704092261; Hm_lpvt_13d91d128b66b17ad38ffa796197c95f=1704092261; Hm_lvt_a9fb0759abef2fb3407d991272fa1e17=1704092261; Hm_lpvt_a9fb0759abef2fb3407d991272fa1e17=1704092261; USEIDENTIFY=20662329-57bf-4b64-a447-70d7a69ce213”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)



.子程序 _按钮3_被单击

时间 ＝ 时间_到文本 (, , , )

编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问3 () ＋ #换行符


.子程序 功能_网页访问3, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “http://bb.tqjy666.com:10005/wind/public/login/registerphone.html”
局_提交数据 ＝ “__token__=e7b9841c8a85bf3b4d91588ce0c011&phone=” ＋ 编辑框1.内容 ＋ “&inviter=0”
局_提交协议头 ＝ “Host:bb.tqjy666.com:10005” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “Cache-Control:max-age=0” ＋ #换行符 ＋ “Upgrade-Insecure-Requests:1” ＋ #换行符 ＋ “Origin:http://bb.tqjy666.com:10005” ＋ #换行符 ＋ “Content-Type:application/x-www-form-urlencoded” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36” ＋ #换行符 ＋ “Accept:text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7” ＋ #换行符 ＋ “Referer:http://bb.tqjy666.com:10005/wind/public/login/registerphone/inviter/0.html” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”
局_提交cookie ＝ “PHPSESSID=0”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮4_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问4 () ＋ #换行符


.子程序 功能_网页访问4, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.waterchina.com/api/sys-service/security/sendVerifyCodeByCopmany?phone=” ＋ 编辑框1.内容 ＋ “&companyCode=SXWEB&templateCodeEnum=USER_LOGIN”
' 局_提交协议头 = "Host:www.waterchina.com" ＋ #换行符 ＋ "Connection:keep-alive" ＋ #换行符 ＋ "sec-ch-ua:"Chromium";v="119", "Not?A_Brand";v="24"" ＋ #换行符 ＋ "Accept:application/json, text/plain, */*" ＋ #换行符 ＋ "clientKey:WEB" ＋ #换行符 ＋ "appAccessToken:ZFZW1aMmFYTnBkRzl5ZF.2s7YYih7VJ" ＋ #换行符 ＋ "sec-ch-ua-mobile:?0" ＋ #换行符 ＋ "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36" ＋ #换行符 ＋ "sec-ch-ua-platform:"Windows"" ＋ #换行符 ＋ "Sec-Fetch-Site:same-origin" ＋ #换行符 ＋ "Sec-Fetch-Mode:cors" ＋ #换行符 ＋ "Sec-Fetch-Dest:empty" ＋ #换行符 ＋ "Referer:https://www.waterchina.com/login?tk=ZFZW1aMmFYTnBkRzl5ZF.2s7YYih7VJ" ＋ #换行符 ＋ "Accept-Encoding:gzip, deflate, br" ＋ #换行符 ＋ "Accept-Language:zh-CN,zh;q=0.9"
局_提交cookie ＝ “”
局_结果 ＝ 网页_访问_对象 (局_网址, 0, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮5_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问5 () ＋ #换行符


.子程序 功能_网页访问5, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.yunbiao.tv/user/sendShortMsg.html”
局_提交数据 ＝ “userPhone=” ＋ 编辑框1.内容 ＋ “&date=Mon+Jan+01+2024+15%3A11%3A39+GMT%2B0800+(%E4%B8%AD%E5%9B%BD%E6%A0%87%E5%87%86%E6%97%B6%E9%97%B4)”
' 局_提交协议头 = "Host:www.yunbiao.tv" ＋ #换行符 ＋ "Connection:keep-alive" ＋ #换行符 ＋ "sec-ch-ua:"Chromium";v="119", "Not?A_Brand";v="24"" ＋ #换行符 ＋ "Accept:application/json, text/javascript, */*; q=0.01" ＋ #换行符 ＋ "Content-Type:application/x-www-form-urlencoded; charset=UTF-8" ＋ #换行符 ＋ "X-Requested-With:XMLHttpRequest" ＋ #换行符 ＋ "sec-ch-ua-mobile:?0" ＋ #换行符 ＋ "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36" ＋ #换行符 ＋ "sec-ch-ua-platform:"Windows"" ＋ #换行符 ＋ "Origin:https://www.yunbiao.tv" ＋ #换行符 ＋ "Sec-Fetch-Site:same-origin" ＋ #换行符 ＋ "Sec-Fetch-Mode:cors" ＋ #换行符 ＋ "Sec-Fetch-Dest:empty" ＋ #换行符 ＋ "Referer:https://www.yunbiao.tv/user/regeditUi.html" ＋ #换行符 ＋ "Accept-Encoding:gzip, deflate, br" ＋ #换行符 ＋ "Accept-Language:zh-CN,zh;q=0.9"
' 局_提交cookie = "JSESSIONID=CF45749A435EB7341F4A10247220B7D0"
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮6_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问6 () ＋ #换行符




.子程序 功能_网页访问6, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.juip.com/user/SendPhoneCodevefy?key=User_Code&phone=” ＋ 编辑框1.内容
局_提交协议头 ＝ #常量8
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮12_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问12 () ＋ #换行符


.子程序 功能_网页访问12, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://iflow.cn/api/login/sendCode”
局_提交数据 ＝ #常量9 ＋ 编辑框1.内容 ＋ “#引号}”

局_提交协议头 ＝ “Host:iflow.cn” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “Content-Type:application/json” ＋ #换行符 ＋ “bx-v:2.5.28” ＋ #换行符 ＋ “sec-ch-ua-mobile:?0” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36” ＋ #换行符 ＋ “Origin:https://iflow.cn” ＋ #换行符 ＋ “Sec-Fetch-Site:same-origin” ＋ #换行符 ＋ “Sec-Fetch-Mode:cors” ＋ #换行符 ＋ “Sec-Fetch-Dest:empty” ＋ #换行符 ＋ “Referer:https://iflow.cn/” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”

局_提交cookie ＝ “_xl=eyJr”

局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮10_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问10 () ＋ #换行符


.子程序 功能_网页访问10, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.winhc.cn/gw/firefly-login/login/open/sendCheckNo?mobileNo=” ＋ 编辑框1.内容 ＋ “&smsKind=checkNo”
局_提交协议头 ＝ “Cache-Control:no-cache” ＋ #换行符 ＋ “Connection:Keep-Alive” ＋ #换行符 ＋ “Pragma:no-cache” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9” ＋ #换行符 ＋ “Host:www.winhc.cn” ＋ #换行符 ＋ “Referer:https://www.winhc.cn/” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36” ＋ #换行符 ＋ “Appid:WEB_SITE” ＋ #换行符 ＋ “SessionId:null”
局_结果 ＝ 网页_访问 (局_网址, 0, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮9_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问9 () ＋ #换行符


.子程序 功能_网页访问9, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://api.service.longmaosoft.com/v1/api/unsafe/common/sdk/message/sms/code?mobile=” ＋ 编辑框1.内容 ＋ “&bid=1015”
局_提交协议头 ＝ “Connection:Keep-Alive” ＋ #换行符 ＋ “Content-Type:application/x-www-form-urlencoded” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9” ＋ #换行符 ＋ “Host:api.service.longmaosoft.com” ＋ #换行符 ＋ “Referer:http://login.longmaosoft.com/mobile?referer=https%3A%2F%2Fwww.longmaosoft.com” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36” ＋ #换行符 ＋ “Origin:http://login.longmaosoft.com” ＋ #换行符 ＋ “Token:8eadff287dd5c9b7508d708f9f931f0f”
局_结果 ＝ 网页_访问 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)



.子程序 _按钮11_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问11 () ＋ #换行符


.子程序 功能_网页访问11, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://api.xnzb.org.cn/finance-admin-api/userinfo/sendphonecode”
局_提交数据 ＝ “phone=” ＋ 编辑框1.内容
局_提交协议头 ＝ “Host:api.xnzb.org.cn” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/86.0.4240.198 Safari/537.36” ＋ #换行符 ＋ “Content-Type:application/x-www-form-urlencoded” ＋ #换行符 ＋ “Origin:https://xnzbadmin.xnzb.org.cn” ＋ #换行符 ＋ “Sec-Fetch-Site:same-site” ＋ #换行符 ＋ “Sec-Fetch-Mode:cors” ＋ #换行符 ＋ “Sec-Fetch-Dest:empty” ＋ #换行符 ＋ “Referer:https://xnzbadmin.xnzb.org.cn/finance-admin/” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮8_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问8 () ＋ #换行符



.子程序 功能_网页访问8, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

时间 ＝ 时间_取现行时间戳 ()
图片 ＝ 网页_访问_对象 (“https://www.7net.cc/CheckCode/Register?length=4&fontsize=18&1704095738920”, 0, , , cookie, 协议头)
答题结果 ＝ 到文本 (网页_访问 (“http://*************:25888”, 1, , , , , , , 图片))
调试输出 (答题结果)


局_网址 ＝ “https://www.7net.cc/User/SmsRegistSend”
局_提交数据 ＝ “RegisterCode=3944&phone=” ＋ 编辑框1.内容
局_提交协议头 ＝ “Connection:Keep-Alive” ＋ #换行符 ＋ “Content-Type:application/x-www-form-urlencoded; charset=UTF-8” ＋ #换行符 ＋ “Accept:application/json, text/javascript, */*; q=0.01” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9” ＋ #换行符 ＋ “Host:www.7net.cc” ＋ #换行符 ＋ “Referer:https://www.7net.cc/build/form/register/index.html” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36” ＋ #换行符 ＋ “Origin:https://www.7net.cc” ＋ #换行符 ＋ “X-Requested-With:XMLHttpRequest”
局_提交cookie ＝ “aliyungf_tc=8a84bddc9135b882839eec377de54dec0cb33bd783213c78eea7a9f6e72edad2; Gw_RegisterCode=F9FE83F1EA3DD2108188FB7BF8AA5B3C; ”
局_结果 ＝ 网页_访问 (局_网址, 1, 局_提交数据, cookie, , 局_提交协议头, , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮7_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问7 () ＋ #换行符


.子程序 功能_网页访问7, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.jkta.com.cn/user/user/register/sendRegisterSmsVerificationCode/” ＋ 编辑框1.内容 ＋ “?code=&publicServiceType=050000&requestSource=web&serviceType=050000&userSource=0&uuid=”
局_提交数据 ＝ “up=y&wechat=” ＋ 编辑框1.内容 ＋ “&passWord=gjm453190&mobileTel=” ＋ 编辑框1.内容 ＋ “&act=mobileTelVerify”
局_提交协议头 ＝ “Connection:Keep-Alive” ＋ #换行符 ＋ “Content-Type:text/plain; Charset=UTF-8” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9” ＋ #换行符 ＋ “Host:www.jkta.com.cn” ＋ #换行符 ＋ “Referer:https://www.jkta.com.cn/user/register/” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36”
局_结果 ＝ 网页_访问 (局_网址, 0, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮13_被单击
.局部变量 i, 整数型

.' 计次循环首 (30, i)

    ' _按钮+到文本（1）+_被单击 ()
    _按钮1_被单击 ()
    _按钮2_被单击 ()
    _按钮3_被单击 ()
    _按钮4_被单击 ()
    _按钮5_被单击 ()
    _按钮6_被单击 ()
    _按钮7_被单击 ()
    _按钮8_被单击 ()
    _按钮9_被单击 ()
    _按钮10_被单击 ()
    _按钮11_被单击 ()
    _按钮12_被单击 ()
    _按钮13_被单击 ()
    ' _按钮14_被单击 ()
    ' _按钮15_被单击 ()
    ' _按钮16_被单击 ()
    ' _按钮17_被单击 ()
    ' _按钮18_被单击 ()
    ' _按钮19_被单击 ()
    ' _按钮20_被单击 ()
    ' _按钮21_被单击 ()
    ' _按钮22_被单击 ()
    ' _按钮1_被单击 ()

.' 计次循环尾 ()

.子程序 _按钮14_被单击

编辑框2.内容 ＝ “”



.子程序 _按钮15_被单击


手机号 ＝ 取文本右边 (组合框1.内容, 11)

' 文本_取右边 (, , , )
编辑框1.内容 ＝ 手机号

.子程序 _组合框1_列表项被选择

_按钮15_被单击 ()

.子程序 _按钮16_被单击
.局部变量 短信, 文本型
.局部变量 i, 整数型
.局部变量 CD, 整数型

编辑框5.内容 ＝ “99”


.计次循环首 (10000, i)

    短信 ＝ 功能_网页访问读短信2 ()
    调试输出 (短信)

    时间 ＝ 时间_到文本 (, , , )
    编辑框2.内容 ＝ “”
    编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 短信 ＋ #换行符

    .如果真 (寻找文本 (短信, “码”, , 假) ≠ -1)
        ' 短信 ＝ 正则_匹配单个 (短信, 1)
        ' 调试输出 (短信)

        短信 ＝ 文本_取出中间文本 (短信, “<br />”, “SIM”)




        编辑框3.内容 ＝ “”
        验证码 ＝ 文本_取出中间文本 (短信, “移动】”, “是”)
        调试输出 (验证码)
        CD ＝ 取文本长度 (验证码)
        调试输出 (CD)

        .如果真 (CD ＝ 0)
            验证码 ＝ 正则_匹配单个短信验证码 (短信, 1, )
            调试输出 (验证码)
        .如果真结束

        ' 如果真 (取文本长度 (短信) ＞ 0)


        编辑框3.内容 ＝ 编辑框3.内容 ＋ 时间 ＋ “----” ＋ 短信 ＋ #换行符
        编辑框3.加入文本 (“短信验证码是：” ＋ 验证码)
        置剪辑板文本 (验证码)


    .如果真结束





    .如果真 (编辑框5.内容 ＝ “100”)
        跳出循环 ()
    .如果真结束

    延时_不卡死 (5000)
.计次循环尾 ()








.子程序 正则_匹配单个短信验证码, 文本型, , 本子程序采用【精易编程助手】生成。成功返回匹配的文本，失败返回空文本。
.参数 源文本, 文本型, , 欲被匹配的文本
.参数 匹配索引, 整数型, , 表达式的索引，从1开始将代表为子匹配文本的索引。
.参数 子匹配索引, 整数型, 可空, 可空。 子表达式的索引，从1开始将代表为子匹配文本的索引
.局部变量 局_正则, 正则表达式类, , , 此类为精易模块里面的正则类，精易模块下载地址：http://ec.125.la/

.如果真 (局_正则.创建 (“\d{4,6}”, 源文本, 假, 假, 真, 真) ＝ 假)
    信息框 (“正则创建失败，请检查正则表达式语句是否有误！”, #错误图标, , )
    返回 (“”)
.如果真结束
.如果真 (局_正则.取匹配数量 () ＝ 0)
    信息框 (“匹配失败，请检检查正则语句是否有误，数量：0”, #错误图标, , )
    返回 (“”)
.如果真结束
.如果真 (匹配索引 ＜ 1)
    信息框 (“参数二 - 匹配索引必须大于0。”, #错误图标, , )
    返回 (“”)
.如果真结束

.判断开始 (匹配索引 ＞ 0 且 子匹配索引 ＞ 0)  ' 如果 匹配索引 和 子匹配索引 都大于0，则匹配子匹配文本
    返回 (局_正则.取子匹配文本 (匹配索引, 子匹配索引))

.判断 (匹配索引 ＞ 0)  ' 如果只有匹配索引大于0，则返回指定的匹配文本
    返回 (局_正则.取匹配文本 (匹配索引))
.默认

.判断结束
返回 (“”)

.子程序 功能_网页访问读短信2, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “http://**************/55/js_content.php”
局_提交数据 ＝ “page=1&rand=0.4727184796607129”
局_提交协议头 ＝ “Host:**************” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “Accept:*/*” ＋ #换行符 ＋ “X-Requested-With:XMLHttpRequest” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36” ＋ #换行符 ＋ “Content-Type:application/x-www-form-urlencoded; charset=UTF-8” ＋ #换行符 ＋ “Origin:http://**************” ＋ #换行符 ＋ “Referer:http://**************/55/” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”
局_提交cookie ＝ “Hm_lvt_0635661383aaa290c3690f94520a59f1=1708521814,1710133463; PHPSESSID=ui5c61pf48escff5bn1j48d005”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 功能_网页访问读短信, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “http://www.pushplus.plus/api/customer/message/webLog”
局_提交协议头 ＝ #常量3


局_提交cookie ＝ “Hm_lvt_1c61e24eff639e825f5a3d7f957635c6=1703756648,1704074744,1704358332,1704372794; pushToken=d771198217674b908a07c222fbfdc2bf; Hm_lpvt_1c61e24eff639e825f5a3d7f957635c6=1705220858”
局_结果 ＝ 网页_访问_对象 (局_网址, 0, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)




.子程序 正则_匹配单个, 文本型, , 本子程序采用【精易编程助手】生成。成功返回匹配的文本，失败返回空文本。
.参数 源文本, 文本型, , 欲被匹配的文本
.参数 匹配索引, 整数型, , 表达式的索引，从1开始将代表为子匹配文本的索引。
.参数 子匹配索引, 整数型, 可空, 可空。 子表达式的索引，从1开始将代表为子匹配文本的索引
.局部变量 局_正则, 正则表达式类, , , 此类为精易模块里面的正则类，精易模块下载地址：http://ec.125.la/

.如果真 (局_正则.创建 (“[\u4e00-\u9fa5][1-9]{4,6}”, 源文本, 假, 假, 真) ＝ 假)
    信息框 (“正则创建失败，请检查正则表达式语句是否有误！”, #错误图标, , )
    返回 (“”)
.如果真结束
.如果真 (局_正则.取匹配数量 () ＝ 0)
    信息框 (“匹配失败，请检检查正则语句是否有误，数量：0”, #错误图标, , )
    返回 (“”)
.如果真结束
.如果真 (匹配索引 ＜ 1)
    信息框 (“参数二 - 匹配索引必须大于0。”, #错误图标, , )
    返回 (“”)
.如果真结束

.判断开始 (匹配索引 ＞ 0 且 子匹配索引 ＞ 0)  ' 如果 匹配索引 和 子匹配索引 都大于0，则匹配子匹配文本
    返回 (局_正则.取子匹配文本 (匹配索引, 子匹配索引))

.判断 (匹配索引 ＞ 0)  ' 如果只有匹配索引大于0，则返回指定的匹配文本
    返回 (局_正则.取匹配文本 (匹配索引))
.默认

.判断结束
返回 (“”)

.子程序 _按钮17_被单击

编辑框5.内容 ＝ “100”

.子程序 _按钮18_被单击

编辑框5.内容 ＝ “99”


.子程序 _按钮19_被单击
.局部变量 短信, 文本型
.局部变量 i, 整数型
.局部变量 CD, 整数型
.局部变量 短信1, 文本型

短信 ＝ 功能_网页访问读短信2 ()
调试输出 (短信)
短信1 ＝ 短信

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ “”
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 短信 ＋ #换行符

.' 如果真 (寻找文本 (短信, “码”, , 假) ≠ -1)
    ' 短信 ＝ 正则_匹配单个 (短信, 1)
    ' 调试输出 (短信)

    短信 ＝ 文本_取出中间文本 (短信, “】”, “</div”)


    .如果真 (取文本长度 (短信) ＝ 0)
        短信 ＝ 文本_取出中间文本 (短信1, “ <div ”, “<br />”)

    .如果真结束



    编辑框3.内容 ＝ “”
    ' 验证码 ＝ 文本_取出中间文本 (短信, “移动】”, “是”)
    ' 调试输出 (验证码)
    ' CD ＝ 取文本长度 (验证码)
    ' 调试输出 (CD)

    .' 如果真 (CD ＝ 0)
        验证码 ＝ 正则_匹配单个短信验证码 (短信, 1, )
        调试输出 (验证码)
    .如果真结束

    ' 如果真 (取文本长度 (短信) ＞ 0)


    编辑框3.内容 ＝ 编辑框3.内容 ＋ 时间 ＋ “----” ＋ 短信 ＋ #换行符
    编辑框3.加入文本 (“短信验证码是：” ＋ 验证码)
    置剪辑板文本 (验证码)
.如果真结束


.子程序 _按钮20_被单击


.如果真 (编辑框5.内容 ＝ “1995”)
    编辑框6.可视 ＝ 真
    组合框1.可视 ＝ 真
    编辑框1.可视 ＝ 真
.如果真结束


.子程序 _编辑框6_内容被改变



.子程序 _按钮21_被单击

编辑框1.内容 ＝ 取剪辑板文本 ()


.子程序 _按钮22_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问13 () ＋ #换行符


.子程序 功能_网页访问13, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “http://www.19x19.com/api/auth/sms/code?username=” ＋ 编辑框1.内容 ＋ “&login=false&area=0086”
局_提交协议头 ＝ “Host:www.19x19.com” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “Authorization:Basic Z” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36” ＋ #换行符 ＋ “Content-Type:application/x-www-form-urlencoded; charset=UTF-8” ＋ #换行符 ＋ “Referer:http://www.19x19.com/engine/register” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”
' 局_提交cookie ＝ “_gcl_”
局_结果 ＝ 网页_访问_对象 (局_网址, 0, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮23_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问15 () ＋ #换行符


.子程序 功能_网页访问15, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.gentlemonster.com/cn/customer/api/get_verification_code?phone_number=” ＋ 编辑框1.内容
局_提交数据 ＝ “{” ＋ #引号 ＋ “phone_number” ＋ #引号 ＋ “:” ＋ #引号 ＋ “” ＋ 编辑框1.内容 ＋ “” ＋ #引号 ＋ “}”
' 局_提交协议头 = "Host:www.gentlemonster.com" ＋ #换行符 ＋ "Connection:keep-alive" ＋ #换行符 ＋ "sec-ch-ua:"Chromium";v="119", "Not?A_Brand";v="24"" ＋ #换行符 ＋ "Accept:application/json, text/plain, */*" ＋ #换行符 ＋ "Content-Type:application/json" ＋ #换行符 ＋ "sec-ch-ua-mobile:?0" ＋ #换行符 ＋ "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36" ＋ #换行符 ＋ "sec-ch-ua-platform:"Windows"" ＋ #换行符 ＋ "Origin:https://www.gentlemonster.com" ＋ #换行符 ＋ "Sec-Fetch-Site:same-origin" ＋ #换行符 ＋ "Sec-Fetch-Mode:cors" ＋ #换行符 ＋ "Sec-Fetch-Dest:empty" ＋ #换行符 ＋ "Referer:https://www.gentlemonster.com/cn/customer/account_register" ＋ #换行符 ＋ "Accept-Encoding:gzip, deflate, br" ＋ #换行符 ＋ "Accept-Language:zh-CN,zh;q=0.9"
' 局_提交cookie ＝ “_fwb=67W”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮25_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问16 () ＋ #换行符


.子程序 功能_网页访问16, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://www.dzzgsw.com/phoneCaptcha?phone=” ＋ 编辑框1.内容 ＋ “&token=”

' 局_提交协议头 = "Host:www.dzzgsw.com" ＋ #换行符 ＋ "Connection:keep-alive" ＋ #换行符 ＋ "sec-ch-ua:"Chromium";v="119", "Not?A_Brand";v="24"" ＋ #换行符 ＋ "Accept:application/json, text/javascript, */*; q=0.01" ＋ #换行符 ＋ "X-Requested-With:XMLHttpRequest" ＋ #换行符 ＋ "sec-ch-ua-mobile:?0" ＋ #换行符 ＋ "User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36" ＋ #换行符 ＋ "sec-ch-ua-platform:"Windows"" ＋ #换行符 ＋ "Origin:https://www.dzzgsw.com" ＋ #换行符 ＋ "Sec-Fetch-Site:same-origin" ＋ #换行符 ＋ "Sec-Fetch-Mode:cors" ＋ #换行符 ＋ "Sec-Fetch-Dest:empty" ＋ #换行符 ＋ "Referer:https://www.dzzgsw.com/" ＋ #换行符 ＋ "Accept-Encoding:gzip, deflate, br" ＋ #换行符 ＋ "Accept-Language:zh-CN,zh;q=0.9"
' 局_提交cookie = "JSESSIONID=01D1F15C922D8D4FDDE00366C5096BAE; _trs_user=; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2218e666b4f2226b-0600091a2061a4-623b5e53-2073600-18e666b4f23957%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.baidu.com%2F%22%7D%2C%22%24device_id%22%3A%2218e666b4f2226b-0600091a2061a4-623b5e53-2073600-18e666b4f23957%22%7D; code=6469"
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮29_被单击

时间 ＝ 时间_到文本 (, , , )
编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问17 () ＋ #换行符


.子程序 功能_网页访问17, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://roi.kaogujia.com/api/passport/login/sms/send”
局_提交数据 ＝ “{” ＋ #引号 ＋ “mobile” ＋ #引号 ＋ “:” ＋ 编辑框1.内容 ＋ “}”
局_提交协议头 ＝ “Host:roi.kaogujia.com” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “Content-Type:application/json” ＋ #换行符 ＋ “version_code:3.1” ＋ #换行符 ＋ “sec-ch-ua-mobile:?0” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36” ＋ #换行符 ＋ “Origin:https://dyj.kaogujia.com” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮24_被单击

时间 ＝ 时间_到文本 (, , , )

编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问24 () ＋ #换行符




.子程序 功能_网页访问24, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://shuiguopp.com/bapi/sms/code”
局_提交数据 ＝ “{” ＋ #引号 ＋ “phoneNumber” ＋ #引号 ＋ “:” ＋ #引号 ＋ 编辑框1.内容 ＋ #引号 ＋ “}”

局_提交协议头 ＝ “Host:shuiguopp.com” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “sec” ＋ #换行符 ＋ “x-version:v2” ＋ #换行符 ＋ “X-Signature:33” ＋ #换行符 ＋ “sec-ch-ua-mobile:?0” ＋ #换行符 ＋ “Authorization:Bearer” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36” ＋ #换行符 ＋ “Content-Type:application/json” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “X-Timestamp:1741956790658” ＋ #换行符 ＋ “X-Nonce:7DrKE204” ＋ #换行符 ＋ “sec-ch-ua-pl” ＋ #换行符 ＋ “Origin:https://shuiguopp.com” ＋ #换行符 ＋ “Sec-Fetch-Site:same-origin” ＋ #换行符 ＋ “Sec-Fetch-Mode:cors” ＋ #换行符 ＋ “Sec-Fetch-Dest:empty” ＋ #换行符 ＋ “Referer:https://shuiguopp.com/login” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮27_被单击

时间 ＝ 时间_到文本 (, , , )

编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问27 () ＋ #换行符



.子程序 功能_网页访问27, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型
.局部变量 img_code, 文本型


时间 ＝ 时间_取现行时间戳 ()
图片 ＝ 网页_访问_对象 (“https://xbk.189.cn/xbkapi/api/auth/captcha?guid=5427854452”, 0, , , cookie, 协议头)

' 答题结果 ＝ 到文本 (网页_访问 (“http://*************:25888”, 1, , , , , , , 图片))
' 调试输出 (答题结果)
' 局_网址 ＝ “https://music.sonyselect.net/getSmsAuthCode”
' 局_提交数据 ＝ “phoneNo=” ＋ 编辑框1.内容 ＋ “&smsAuthCode=” ＋ 答题结果

' 文本 ＝ 到文本 (网页_访问_对象 (“https://h5forphone.wostore.cn/h5forphone/changxiang/getCode?exchangecodeCode=247WAOY949”, 0, , “arp_scroll_position=348”, cookie, #常量5))
' img_code ＝ 文本_取出中间文本 (文本, “base64,”, “#引号”)
' img_code ＝ 文本_替换 (img_code, , , , “\/”, “/”)
' img_code ＝ 文本_替换 (img_code, , , , “\n”, “”)
' 调试输出 (img_code)
' 图片 ＝ 编码_BASE64编码 (图片)



' http://192.168.2.42:19956/ocr

图片框1.图片 ＝ 图片
答题结果 ＝ “data:image/jpeg;base64,” ＋ 编码_BASE64编码 (图片)


答题结果 ＝ 编码_URL编码 (答题结果, )


答题结果 ＝ 到文本 (网页_访问 (“http://192.168.2.42:19956/ocr”, 1, “image=” ＋ 答题结果, , , “Content-Type: application/x-www-form-urlencoded”, , , , ))


调试输出 (答题结果)

答题结果 ＝ 到文本 (网页_访问 (“http://*************:25888”, 1, , , , , , , 图片))


调试输出 (答题结果)



局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮26_被单击

时间 ＝ 时间_到文本 (, , , )

编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问26 () ＋ #换行符



.子程序 功能_网页访问26, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://metaso.cn/verify?type=signup”
局_提交数据 ＝ “{” ＋ #引号 ＋ “phone” ＋ #引号 ＋ “:” ＋ #引号 ＋ “86-” ＋ 编辑框1.内容 ＋ #引号 ＋ “}”

局_提交协议头 ＝ “Host:metaso.cn” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “sec-ch-ua-mobile:?0” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36” ＋ #换行符 ＋ “Content-Type:application/json” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “metaso-pc:pc” ＋ #换行符 ＋ “token:wr8+pHu3KYryzz0O2MaBSNUZbVLjLUYC1FR4sKqSW0qFxcvJIDolBQE3Qg2gaNtI/sMhwNAwGDKL5y7otuDHPKnrz+N8EQYAPE1jk51O28SHHtqPkgMzbk2htN5Vf17BYVVarxzk3k05CggHNW/16w==” ＋ #换行符 ＋ “Origin:https://metaso.cn” ＋ #换行符 ＋ “Sec-Fetch-Site:same-origin” ＋ #换行符 ＋ “Sec-Fetch-Mode:cors” ＋ #换行符 ＋ “Sec-Fetch-Dest:empty” ＋ #换行符 ＋ “Accept-Language:zh-CN,zh;q=0.9”
局_提交cookie ＝ “aliyungf_tc=bd272f2202d3171dc6cf28a66c53e84da4a184baf26daa659e0b45f2072867de; tid=2d7da501-0cde-479a-a175-744497edda39; __eventn_id_UMO2dYNwFz=6m336s1z0f; sid=02b4bdbd532d4a2387b58d990970d02e; traceid=40ec22312b654ff0”

局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )
局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)

.子程序 _按钮28_被单击

时间 ＝ 时间_到文本 (, , , )

编辑框2.内容 ＝ 编辑框2.内容 ＋ 时间 ＋ “----” ＋ 功能_网页访问28 () ＋ #换行符



.子程序 功能_网页访问28, 文本型, , 本子程序由编程猫内部插件生成,请配合精易模块使用。QQ群:*********
.局部变量 局_网址, 文本型
.局部变量 局_提交数据, 文本型
.局部变量 局_提交cookie, 文本型
.局部变量 局_提交协议头, 文本型
.局部变量 局_结果, 字节集
.局部变量 局_返回, 文本型

局_网址 ＝ “https://bbs.kingbase.com.cn/web-api/web/system/user/getRegisterSmsCode”
局_提交数据 ＝ 编辑框1.内容
局_提交协议头 ＝ “Host:bbs.kingbase.com.cn” ＋ #换行符 ＋ “Connection:keep-alive” ＋ #换行符 ＋ “s” ＋ #换行符 ＋ “Accept:application/json, text/plain, */*” ＋ #换行符 ＋ “Content-Type:application/json;charset=UTF-8” ＋ #换行符 ＋ “Accept-Language:zh-CN” ＋ #换行符 ＋ “sec-ch-ua-mobile:?0” ＋ #换行符 ＋ “User-Agent:Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36” ＋ #换行符 ＋ “sec-ch-ua-platform:” ＋ #换行符 ＋ “Origin:https://bbs.kingbase.com.cn” ＋ #换行符 ＋ “Sec-Fetch-Site:same-origin” ＋ #换行符 ＋ “Sec-Fetch-Mode:cors” ＋ #换行符 ＋ “Sec-Fetch-Dest:empty” ＋ #换行符 ＋ “Referer:https://bbs.kingbase.com.cn/register” ＋ #换行符 ＋ “Accept-Encoding:gzip, deflate, br”
局_结果 ＝ 网页_访问_对象 (局_网址, 1, 局_提交数据, 局_提交cookie, , 局_提交协议头, , , , , , , , , , , )

局_返回 ＝ 编码_Utf8到Ansi (局_结果)  ' 自己去找编码
返回 (局_返回)
