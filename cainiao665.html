<!DOCTYPE html>
<html lang="zh-CN">
<head>
        <a href="http://192.168.2.201/cainiao665.html" class="button rotate-button">随机旋转</a>
        <a href="http://192.168.2.201/cainiao666.html" class="button">固定</a>
        <a href="http://192.168.2.201/cainiao667.html" class="button">识别图片</a>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>随机旋转</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 10px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            display: flex;
            gap: 15px;
        }

        .control-panel {
            width: 280px;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: fit-content;
            overflow-y: auto;
            max-height: 90vh;
        }

        .main-game-area {
            width: 420px;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .solutions-area {
            flex: 1;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-y: auto;
            max-height: 90vh;
        }

        .section-title {
            font-size: 12px;
            font-weight: bold;
            margin: 15px 0 5px 0;
            color: #333;
        }

        .section-title:first-child {
            margin-top: 0;
        }

        .form-group {
            margin: 5px 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .form-group label {
            width: 100px;
            font-size: 10px;
            text-align: left;
        }

        .form-group input, .form-group select {
            width: 60px;
            padding: 2px 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 10px;
        }

        .import-textarea {
            width: 100%;
            height: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            font-size: 9px;
            font-family: monospace;
            resize: vertical;
        }

        .checkbox-group {
            margin: 5px 0;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 5px;
        }

        .checkbox-group label {
            font-size: 10px;
        }

        .button-row {
            display: flex;
            gap: 3px;
            margin: 5px 0;
        }

        .button-row button {
            flex: 1;
            padding: 6px 2px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 8px;
            font-weight: bold;
            height: 28px;
        }

        .btn-start { background-color: lightgreen; }
        .btn-clear { background-color: orange; }
        .btn-import { background-color: yellow; }
        .btn-export { background-color: pink; }
        .btn-backtrack { background-color: lightblue; }
        .btn-greedy { background-color: lightcyan; }
        .btn-heuristic { background-color: lightyellow; }
        .btn-random { background-color: lightpink; }
        .btn-constraint { background-color: lightsteelblue; }
        .btn-stop { background-color: lightcoral; }

        .status-panel {
            margin: 10px 0;
        }

        .status-item {
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 10px;
            text-align: center;
            min-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .status-main { background-color: lightyellow; min-height: 30px; }
        .status-steps { background-color: lightgray; }
        .status-piece { background-color: lightsteelblue; }
        .status-algorithm { background-color: lavender; }

        .info-text {
            background-color: lightyellow;
            padding: 8px;
            border-radius: 4px;
            font-size: 8px;
            line-height: 1.2;
            margin-top: 10px;
            white-space: pre-line;
        }

        .game-board {
            display: inline-block;
            border: 2px solid #333;
            background-color: #f9f9f9;
        }

        .board-row {
            display: flex;
        }

        .board-cell {
            width: 35px;
            height: 35px;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
            font-weight: bold;
            transition: all 0.1s;
        }

        .board-cell:hover {
            opacity: 0.8;
        }

        .cell-empty { background-color: lightblue; }
        .cell-required { background-color: red; color: white; }
        .cell-forbidden { background-color: gray; color: white; }
        .cell-piece { color: white; }

        .mode-indicator {
            font-size: 12px;
            margin: 10px 0;
            padding: 5px;
            border-radius: 4px;
            background-color: #e3f2fd;
        }

        .mode-show { color: blue; }
        .mode-fast { color: red; }

        /* 解决方案展示区域 */
        .solutions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }

        .solution-item {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background-color: #fafafa;
            transition: all 0.3s;
        }

        .solution-item.highlight {
            border-color: #4CAF50;
            background-color: #e8f5e8;
            transform: scale(1.02);
        }

        .solution-header {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 8px;
            text-align: center;
            color: #333;
        }

        .solution-board {
            display: inline-block;
            border: 1px solid #666;
            background-color: #fff;
        }

        .solution-board .board-row {
            display: flex;
        }

        .solution-board .board-cell {
            width: 20px;
            height: 20px;
            border: 0.5px solid #ccc;
            font-size: 8px;
        }

        .solution-info {
            font-size: 9px;
            margin-top: 5px;
            text-align: center;
            color: #666;
        }

        .rank-badge {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: #2196F3;
            color: white;
            text-align: center;
            line-height: 20px;
            font-size: 10px;
            font-weight: bold;
            margin-right: 5px;
        }

        .rank-1 { background-color: #FFD700; }
        .rank-2 { background-color: #C0C0C0; }
        .rank-3 { background-color: #CD7F32; }

        .solutions-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .solutions-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .solutions-stats {
            font-size: 11px;
            color: #666;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 1200px) {
            .container {
                flex-direction: column;
            }
            .control-panel, .main-game-area {
                width: 100%;
                max-height: none;
            }
            .solutions-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 控制面板 -->
        <div class="control-panel">
            <div class="section-title">随机旋转    棋盘大小:</div>
            <div class="form-group">
                <select id="boardSize">
                    <option value="5">5x5</option>
                    <option value="6">6x6</option>
                    <option value="7" selected>7x7</option>
                    <option value="8">8x8</option>
                    <option value="9">9x9</option>
                </select>
            </div>

            <div class="section-title">方块数量设置:</div>
            <div class="form-group">
                <label>T:</label>
                <input type="number" id="count-T" value="1" min="0" max="10">
            </div>
            <div class="form-group">
                <label>田:</label>
                <input type="number" id="count-田" value="1" min="0" max="10">
            </div>
            <div class="form-group">
                <label>横杠竖条:</label>
                <input type="number" id="count-横杠竖条" value="1" min="0" max="10">
            </div>
            <div class="form-group">
                <label>Z:</label>
                <input type="number" id="count-Z" value="1" min="0" max="10">
            </div>
            <div class="form-group">
                <label>L:</label>
                <input type="number" id="count-L" value="1" min="0" max="10">
            </div>

            <div class="section-title">破解设置:</div>
            <div class="checkbox-group">
                <input type="checkbox" id="showProcess">
                <label for="showProcess">显示破解过程</label>
            </div>

            <div class="section-title">基本操作:</div>
            <div class="button-row">
                <button class="btn-start" onclick="startMarking()">开始标注</button>
                <button class="btn-clear" onclick="clearAllMarks()">清除标记</button>
            </div>

            <div class="section-title">导入棋盘:</div>
            <textarea class="import-textarea" id="importText" placeholder="在此粘贴棋盘数据..."></textarea>
            <div class="button-row">
                <button class="btn-import" onclick="importBoard()">导入棋盘</button>
                <button class="btn-export" onclick="exportBoard()">复制棋盘</button>
            </div>

            <div class="section-title">破解算法:</div>
            <div class="button-row">
                <button class="btn-backtrack" onclick="solvePuzzle('智能回溯法')">智能回溯</button>
                <button class="btn-heuristic" onclick="solvePuzzle('回溯法')">传统回溯</button>
            </div>
            <div class="button-row">
                <button class="btn-greedy" onclick="solvePuzzle('贪心算法')">贪心算法</button>
                <button class="btn-stop" onclick="stopSolving()">停止破解</button>
            </div>

            <div class="section-title">破解状态:</div>
            <div class="status-panel">
                <div class="status-item status-main" id="statusMain">等待中...</div>
                <div class="status-item status-steps" id="statusSteps">步数: 0</div>
                <div class="status-item status-piece" id="statusPiece">当前方块: 无</div>
                <div class="status-item status-algorithm" id="statusAlgorithm">算法: 未选择</div>
            </div>

            <div class="info-text">算法说明：
智能回溯：先计算需要覆盖的红色格子数量，优先在红色格子周围放置方块，能更快找到解答。

传统回溯：按顺序尝试所有位置，适合作为对比。

贪心算法：每次选择能覆盖最多红色格子的方块放置，快速但可能不是最优解。</div>
        </div>

        <!-- 主游戏区域 -->
        <div class="main-game-area">
            <h1>主棋盘</h1>
            <div class="mode-indicator" id="modeIndicator">
                <span id="modeText" class="mode-show">模式: 显示破解过程</span>
            </div>
            <div id="gameBoard" class="game-board">
                <!-- 棋盘将在这里动态生成 -->
            </div>
        </div>

        <!-- 解决方案展示区域 -->
        <div class="solutions-area">
            <div class="solutions-header">
                <div class="solutions-title">解决方案排行榜</div>
                <div class="solutions-stats" id="solutionsStats">找到: 0 个解决方案</div>
            </div>
            <div class="solutions-grid" id="solutionsGrid">
                <!-- 解决方案将在这里动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 游戏类
        class TetrisPuzzleGame {
            constructor() {
                this.boardSize = 7;
                this.board = [];
                this.solutionBoard = [];
                this.solving = false;
                this.solveStep = 0;
                this.showProcess = false;
                this.currentAlgorithm = "回溯法";
                this.solutions = []; // 存储找到的解决方案
                this.maxSolutions = 10; // 最多显示10个解决方案

                // 俄罗斯方块定义
                this.tetrisShapes = {
                    "T": [
                        [[0,1], [1,0], [1,1], [1,2]],  // T形状 - 上
                        [[0,1], [1,1], [1,2], [2,1]],  // T形状 - 右
                        [[0,0], [1,0], [1,1], [2,0]],  // T形状 - 右
                        [[0,0], [0,1], [0,2], [1,1]],  // T形状 - 下
                        [[0,1], [1,0], [1,1], [2,1]]   // T形状 - 左
                    ],

                    "田": [
                        [[0,0], [0,1], [1,0], [1,1]]   // O形状（方块）
                    ],

                    "横杠竖条": [
                        [[0,0], [0,1], [0,2], [0,3]],  // I形状 - 水平
                        [[0,0], [1,0], [2,0], [3,0]]   // I形状 - 垂直
                    ],

                    "Z": [
                        [[0,0], [0,1], [1,1], [1,2]],  // Z形状 - 0度（水平）
                        [[0,1], [1,0], [1,1], [2,0]],  // Z形状 - 90度（垂直）
                        [[0,1], [0,2], [1,0], [1,1]],  // S形状 - 0度（水平）
                        [[0,0], [1,0], [1,1], [2,1]]   // S形状 - 90度（垂直）
                    ],

                    "L": [
                        [[0,0], [1,0], [2,0], [2,1]],  // L形状 - 0度
                        [[0,1], [1,1], [2,1], [2,0]],  // J形状 - 0度
                        [[0,0], [1,0], [1,1], [1,2]],  // J形状 - 90度
                        [[0,0], [0,1], [1,0], [2,0]],  // J形状 - 180度
                        [[0,0], [0,1], [0,2], [1,2]]   // J形状 - 270度
                    ]
                };

                // 方块数量
                this.pieceCounts = {
                    "T": 1,
                    "田": 1,
                    "横杠竖条": 1,
                    "Z": 1,
                    "L": 1
                };

                // 方块颜色映射
                this.pieceColors = {
                    "T": "purple",
                    "田": "orange",
                    "横杠竖条": "cyan",
                    "Z": "red",
                    "L": "blue"
                };
                // 状态去重
                this.visitedStates = new Set();


                this.initBoard();
                this.setupEventListeners();
                this.createBoard();
            encodeBoard(board) {
                let s = '';
                for (let i = 0; i < this.boardSize; i++) s += board[i].join(',');
                return s;
            }

            getStateKey(board, piecesLeft) {
                return this.encodeBoard(board) + '|' + JSON.stringify(piecesLeft);
            }

                this.initSolutionsDisplay();
            }

            initBoard() {
                this.board = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                this.solutionBoard = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
            }

            setupEventListeners() {
                document.getElementById('boardSize').addEventListener('change', () => this.changeBoardSize());
                document.getElementById('showProcess').addEventListener('change', () => this.toggleProcessDisplay());
            }

            changeBoardSize() {
                this.boardSize = parseInt(document.getElementById('boardSize').value);
                this.initBoard();
                this.createBoard();
                this.clearSolutions();
            }

            toggleProcessDisplay() {
                this.showProcess = document.getElementById('showProcess').checked;
                const modeText = document.getElementById('modeText');
                if (this.showProcess) {
                    modeText.textContent = "模式: 显示破解过程";
                    modeText.className = "mode-show";
                } else {
                    modeText.textContent = "模式: 快速破解";
                    modeText.className = "mode-fast";
                }
            }

            createBoard() {
                const gameBoard = document.getElementById('gameBoard');
                gameBoard.innerHTML = '';

                for (let i = 0; i < this.boardSize; i++) {
                    const row = document.createElement('div');
                    row.className = 'board-row';

                    for (let j = 0; j < this.boardSize; j++) {
                        const cell = document.createElement('div');
                        cell.className = 'board-cell cell-empty';
                        cell.dataset.row = i;
                        cell.dataset.col = j;

                        cell.addEventListener('click', () => this.onLeftClick(i, j));
                        cell.addEventListener('contextmenu', (e) => {
                            e.preventDefault();
                            this.onRightClick(i, j);
                        });
                        cell.addEventListener('dblclick', () => this.onDoubleClick(i, j));

                        row.appendChild(cell);
                    }

                    gameBoard.appendChild(row);
                }

                this.updateBoardDisplay();
            }

            initSolutionsDisplay() {
                this.clearSolutions();
            }

            clearSolutions() {
                this.solutions = [];
                const grid = document.getElementById('solutionsGrid');
                grid.innerHTML = '';
                document.getElementById('solutionsStats').textContent = '找到: 0 个解决方案';
            }

            // 评估解决方案的质量
            evaluateSolution(board) {
                let score = 0;
                let filledCells = 0;
                let requiredCovered = 0;
                let forbiddenViolated = 0;

                // 计算基本指标
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (board[i][j] > 0) {
                            filledCells++;
                        }
                        if (this.board[i][j] === 1 && board[i][j] > 0) {
                            requiredCovered++;
                        }
                        if (this.board[i][j] === 2 && board[i][j] > 0) {
                            forbiddenViolated++;
                        }
                    }
                }

                // 计算评分
                score += requiredCovered * 100; // 覆盖必需位置得高分
                score -= forbiddenViolated * 1000; // 违反禁止位置扣重分
                score += filledCells * 10; // 填充更多格子得分

                // 计算连续性奖励（相邻的同类型方块）
                let continuityBonus = 0;
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (board[i][j] > 0) {
                            if (i > 0 && board[i-1][j] === board[i][j]) continuityBonus++;
                            if (j > 0 && board[i][j-1] === board[i][j]) continuityBonus++;
                        }
                    }
                }
                score += continuityBonus * 5;

                return {
                    score,
                    filledCells,
                    requiredCovered,
                    forbiddenViolated,
                    isValid: this.isValidSolution(board)
                };
            }

            // 添加新的解决方案
            addSolution(board, algorithm, step) {
                const boardCopy = board.map(row => [...row]);
                const evaluation = this.evaluateSolution(boardCopy);

                const solution = {
                    board: boardCopy,
                    algorithm: algorithm,
                    step: step,
                    timestamp: Date.now(),
                    ...evaluation
                };

                // 检查是否已存在相同的解决方案
                const exists = this.solutions.some(s =>
                    JSON.stringify(s.board) === JSON.stringify(boardCopy)
                );

                if (!exists) {
                    this.solutions.push(solution);

                    // 按评分排序
                    this.solutions.sort((a, b) => {
                        if (a.isValid !== b.isValid) {
                            return b.isValid - a.isValid; // 有效解决方案优先
                        }
                        return b.score - a.score; // 按评分降序
                    });

                    // 保持最多10个解决方案
                    if (this.solutions.length > this.maxSolutions) {
                        this.solutions = this.solutions.slice(0, this.maxSolutions);
                    }

                    this.updateSolutionsDisplay();
                }
            }

            // 更新解决方案显示
            updateSolutionsDisplay() {
                const grid = document.getElementById('solutionsGrid');
                grid.innerHTML = '';

                document.getElementById('solutionsStats').textContent =
                    `找到: ${this.solutions.length} 个解决方案`;

                this.solutions.forEach((solution, index) => {
                    const solutionDiv = document.createElement('div');
                    solutionDiv.className = 'solution-item';
                    if (solution.isValid) {
                        solutionDiv.classList.add('highlight');
                    }

                    // 创建排名徽章
                    const rankBadge = document.createElement('span');
                    rankBadge.className = `rank-badge rank-${Math.min(index + 1, 3)}`;
                    rankBadge.textContent = index + 1;

                    // 创建标题
                    const header = document.createElement('div');
                    header.className = 'solution-header';
                    header.appendChild(rankBadge);
                    header.appendChild(document.createTextNode(
                        `${solution.algorithm} ${solution.isValid ? '✓' : '✗'}`
                    ));

                    // 创建小棋盘
                    const boardDiv = document.createElement('div');
                    boardDiv.className = 'solution-board';

                    for (let i = 0; i < this.boardSize; i++) {
                        const row = document.createElement('div');
                        row.className = 'board-row';

                        for (let j = 0; j < this.boardSize; j++) {
                            const cell = document.createElement('div');
                            cell.className = 'board-cell';

                            const value = solution.board[i][j];
                            const originalValue = this.board[i][j];

                            if (value === 0) {
                                if (originalValue === 1) {
                                    cell.style.backgroundColor = 'red';
                                    cell.textContent = '✓';
                                    cell.style.color = 'white';
                                } else if (originalValue === 2) {
                                    cell.style.backgroundColor = 'gray';
                                    cell.textContent = '✗';
                                    cell.style.color = 'white';
                                } else {
                                    cell.style.backgroundColor = 'lightblue';
                                }
                            } else {
                                const pieceNames = Object.keys(this.tetrisShapes);
                                const pieceIdx = value - 3;
                                if (pieceIdx < pieceNames.length) {
                                    const pieceName = pieceNames[pieceIdx];
                                    const color = this.pieceColors[pieceName];
                                    cell.style.backgroundColor = color;
                                    cell.textContent = pieceName[0];
                                    cell.style.color = 'white';
                                }
                            }

                            row.appendChild(cell);
                        }

                        boardDiv.appendChild(row);
                    }

                    // 创建信息
                    const info = document.createElement('div');
                    info.className = 'solution-info';
                    info.innerHTML = `
                        评分: ${solution.score}<br>
                        步数: ${solution.step}<br>
                        填充: ${solution.filledCells}/${this.boardSize * this.boardSize}<br>
                        覆盖: ${solution.requiredCovered}
                    `;

                    // 点击复制到主棋盘
                    solutionDiv.addEventListener('click', () => {
                        this.copySolutionToMainBoard(solution.board);
                    });

                    solutionDiv.appendChild(header);
                    solutionDiv.appendChild(boardDiv);
                    solutionDiv.appendChild(info);
                    grid.appendChild(solutionDiv);
                });
            }

            // 复制解决方案到主棋盘
            copySolutionToMainBoard(solutionBoard) {
                if (this.solving) return;

                // 保持原始标记，只更新方块放置
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] <= 2) { // 保持原始标记
                            // 不更改
                        } else {
                            this.board[i][j] = 0; // 清除旧的方块
                        }

                        if (solutionBoard[i][j] > 0) {
                            this.board[i][j] = solutionBoard[i][j];
                        }
                    }
                }

                this.updateBoardDisplay();

                // 显示状态
                document.getElementById('statusMain').textContent = "已复制解决方案到主棋盘";
                document.getElementById('statusMain').style.backgroundColor = "lightgreen";
            }

            onLeftClick(row, col) {
                if (!this.solving) {
                    this.board[row][col] = 1;
                    this.updateCellDisplay(row, col);
                }
            }

            onRightClick(row, col) {
                if (!this.solving) {
                    this.board[row][col] = 2;
                    this.updateCellDisplay(row, col);
                }
            }

            onDoubleClick(row, col) {
                if (!this.solving) {
                    this.board[row][col] = 0;
                    this.updateCellDisplay(row, col);
                }
            }

            updateCellDisplay(row, col) {
                const cell = document.querySelector(`[data-row="${row}"][data-col="${col}"]`);
                const value = this.board[row][col];

                // 清除所有类
                cell.className = 'board-cell';
                cell.style.backgroundColor = '';
                cell.style.color = '';

                if (value === 0) {
                    // 初始格子 - 蓝色
                    cell.className += ' cell-empty';
                    cell.textContent = '';
                } else if (value === 1) {
                    // 正确标记 - 红色
                    cell.className += ' cell-required';
                    cell.textContent = '✓';
                } else if (value === 2) {
                    // 错误标记 - 灰色
                    cell.className += ' cell-forbidden';
                    cell.textContent = '✗';
                } else {  // 解答状态 (value >= 3)
                    const pieceNames = Object.keys(this.tetrisShapes);
                    const pieceIdx = value - 3;
                    if (pieceIdx < pieceNames.length) {
                        const pieceName = pieceNames[pieceIdx];
                        const color = this.pieceColors[pieceName];
                        cell.className += ' cell-piece';
                        cell.style.backgroundColor = color;
                        cell.textContent = pieceName[0];  // 显示第一个字符
                    }
                }
            }

            updateBoardDisplay() {
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        this.updateCellDisplay(i, j);
                    }
                }
            }

            clearAllMarks() {
                if (!this.solving) {
                    // 只清除棋盘，保持解决方案
                    this.initBoard();
                    this.updateBoardDisplay();
                    document.getElementById('statusMain').textContent = "已清除所有标记";
                    document.getElementById('statusPiece').textContent = "当前方块: 无";
                    document.getElementById('statusAlgorithm').textContent = "算法: 未选择";
                }
            }

            // 导出棋盘到剪贴板
            async exportBoard() {
                const data = {
                    board_size: this.boardSize,
                    board: this.board,
                    piece_counts: this.pieceCounts,
                    show_process: this.showProcess
                };

                const dataStr = JSON.stringify(data, null, 2);

                try {
                    await navigator.clipboard.writeText(dataStr);
                    alert("棋盘数据已复制到剪贴板！");
                } catch (err) {
                    // 备用方案：创建临时文本框
                    const textArea = document.createElement('textarea');
                    textArea.value = dataStr;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert("棋盘数据已复制到剪贴板！");
                }
            }

            // 从文本导入棋盘
            importBoard() {
                if (this.solving) {
                    alert("正在破解中，请先停止破解！");
                    return;
                }

                const importText = document.getElementById('importText').value.trim();
                if (!importText) {
                    alert("请先在文本框中输入棋盘数据！");
                    return;
                }

                try {
                    const data = JSON.parse(importText);

                    this.boardSize = data.board_size;
                    this.board = data.board;
                    this.pieceCounts = data.piece_counts;

                    if (data.show_process !== undefined) {
                        this.showProcess = data.show_process;
                        document.getElementById('showProcess').checked = this.showProcess;
                        this.toggleProcessDisplay();
                    }

                    // 更新界面
                    document.getElementById('boardSize').value = this.boardSize;
                    for (const [name, count] of Object.entries(this.pieceCounts)) {
                        const elem = document.getElementById(`count-${name}`);
                        if (elem) {
                            elem.value = count;
                        }
                    }

                    this.createBoard();
                    this.clearSolutions();
                    document.getElementById('statusMain').textContent = "棋盘已导入";
                    document.getElementById('statusMain').style.backgroundColor = "lightgreen";
                    document.getElementById('statusPiece').textContent = "当前方块: 无";
                    document.getElementById('statusAlgorithm').textContent = "算法: 未选择";
                    alert("棋盘已导入！");

                    // 清空输入框
                    document.getElementById('importText').value = '';
                } catch (e) {
                    alert(`导入失败：${e.message}`);
                }
            }

            // 方块放置检查函数
            canPlacePiece(board, shape, startRow, startCol) {
                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    if (r < 0 || r >= this.boardSize ||
                        c < 0 || c >= this.boardSize ||
                        board[r][c] !== 0) {
                        return false;
                    }
                }
                return true;
            }

            placePiece(board, shape, startRow, startCol, pieceId) {
                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    board[r][c] = pieceId;
                }
            }

            removePiece(board, shape, startRow, startCol) {
                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    board[r][c] = 0;
                }
            }

            isValidPlacement(board, shape, startRow, startCol) {
                let coversRequired = 0;
                let coversForbidden = false;

                for (const [dr, dc] of shape) {
                    const r = startRow + dr;
                    const c = startCol + dc;
                    if (this.board[r][c] === 2) {  // 冲突：覆盖了标记为错误的位置
                        coversForbidden = true;
                        break;
                    }
                    if (this.board[r][c] === 1) {  // 覆盖了必需的位置
                        coversRequired++;
                    }
                }
                return [!coversForbidden, coversRequired];
            }

            isValidSolution(board) {
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] === 1 && board[i][j] === 0) {
                            return false;
                        }
                        if (this.board[i][j] === 2 && board[i][j] !== 0) {
                            return false;
                        }
                    }
                }
                return true;
            }

            async updateSolveDisplay(board, message = "", pieceName = "", forceUpdate = false) {
                if (!this.solving) return;

                this.solveStep++;
                document.getElementById('statusSteps').textContent = `步数: ${this.solveStep}`;

                if (pieceName) {
                    document.getElementById('statusPiece').textContent = `当前方块: ${pieceName}`;
                }

                // 检查是否有方块被放置，如果有则添加为候选解决方案
                const hasPlacedPieces = board.some(row => row.some(cell => cell > 0));
                if (hasPlacedPieces && this.solveStep % 5 === 0) { // 每5步添加一次候选解决方案
                    this.addSolution(board, this.currentAlgorithm, this.solveStep);
                }

                if (this.showProcess || forceUpdate) {
                    document.getElementById('statusMain').textContent = message || `正在尝试第${this.solveStep}步...`;

                    if (this.showProcess) {
                        // 临时更新主棋盘显示（仅显示过程，不修改原始棋盘）
                        const tempBoard = this.board.map(row => [...row]);
                        for (let i = 0; i < this.boardSize; i++) {
                            for (let j = 0; j < this.boardSize; j++) {
                                if (board[i][j] > 0 && tempBoard[i][j] <= 2) {
                                    tempBoard[i][j] = board[i][j];
                                }
                            }
                        }

                        const oldBoard = this.board;
                        this.board = tempBoard;
                        this.updateBoardDisplay();
                        this.board = oldBoard;

                        await new Promise(resolve => setTimeout(resolve, 30));
                    }
                } else {
                    if (this.solveStep % 50 === 0) {
                        document.getElementById('statusMain').textContent = `快速破解中... (${this.solveStep}步)`;
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
            }

            // 计算需要覆盖的红色格子数量
            countRequiredCells() {
                let count = 0;
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] === 1) {
                            count++;
                        }
                    }
                }
                return count;
            }

            // 获取所有红色格子的位置
            getRequiredCells() {
                const cells = [];
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] === 1) {
                            cells.push([i, j]);
                        }
                    }
                }
                return cells;
            }

            // 计算当前已覆盖的红色格子数量
            countCoveredRequiredCells(board) {
                let count = 0;
                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        if (this.board[i][j] === 1 && board[i][j] > 0) {
                            count++;
                        }
                    }
                }
                return count;
            }

            // 获取红色格子周围的空位置（按距离排序）
            getPositionsAroundRequired(board, maxDistance = 3) {
                const requiredCells = this.getRequiredCells();
                const positions = new Map(); // 位置 -> 最小距离

                for (const [reqRow, reqCol] of requiredCells) {
                    for (let distance = 1; distance <= maxDistance; distance++) {
                        for (let dr = -distance; dr <= distance; dr++) {
                            for (let dc = -distance; dc <= distance; dc++) {
                                if (Math.abs(dr) + Math.abs(dc) !== distance) continue;

                                const r = reqRow + dr;
                                const c = reqCol + dc;

                                if (r >= 0 && r < this.boardSize && c >= 0 && c < this.boardSize) {
                                    if (board[r][c] === 0 && this.board[r][c] !== 2) {
                                        const key = `${r},${c}`;
                                        if (!positions.has(key) || positions.get(key) > distance) {
                                            positions.set(key, distance);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 按距离排序，距离近的优先
                return Array.from(positions.entries())
                    .map(([pos, dist]) => {
                        const [r, c] = pos.split(',').map(Number);
                        return { row: r, col: c, distance: dist };
                    })
                    .sort((a, b) => a.distance - b.distance);
            }

            // 新的智能回溯算法：优先填充红色格子周围 + 状态哈希剪枝 + 容量剪枝
            async solveBacktrack(board, piecesLeft, depth = 0) {
                if (!this.solving) return false;

                // 状态哈希剪枝
                const stateKey = this.getStateKey(board, piecesLeft);
                if (this.visitedStates.has(stateKey)) return false;
                this.visitedStates.add(stateKey);

                // 容量剪枝：剩余方块可覆盖的格子数 < 未覆盖红格 → 直接剪枝
                const totalRequired = this.countRequiredCells();
                const coveredRequired = this.countCoveredRequiredCells(board);
                const remainingRequired = totalRequired - coveredRequired;
                let capacity = 0;
                for (const [p, c] of Object.entries(piecesLeft)) {
                    if (c > 0) capacity += (this.tetrisShapes[p][0].length) * c;
                }
                if (capacity < remainingRequired) return false;

                const availablePieces = Object.fromEntries(
                    Object.entries(piecesLeft).filter(([name, count]) => count > 0)
                );

                if (Object.keys(availablePieces).length === 0) {
                    const isValid = this.isValidSolution(board);
                    if (isValid) {
                        await this.updateSolveDisplay(board, "智能回溯法找到解答！", "", true);
                        this.addSolution(board, this.currentAlgorithm, this.solveStep);
                    }
                    return isValid;
                }

                // 计算当前状态
                // 再次用于显示

                if (this.showProcess) {
                    await this.updateSolveDisplay(board,
                        `智能回溯: 还需覆盖 ${remainingRequired}/${totalRequired} 个红色格子`,
                        `剩余${Object.keys(availablePieces).length}种方块`);
                }

                // 如果还有红色格子未覆盖，优先选择能覆盖红色格子的方块
                let bestPlacements = [];

                for (const [pieceName, count] of Object.entries(availablePieces)) {
                    const shapes = this.tetrisShapes[pieceName];
                    const pieceId = Object.keys(this.tetrisShapes).indexOf(pieceName) + 3;

                    // 获取红色格子周围的位置
                    const candidatePositions = this.getPositionsAroundRequired(board, 4);

                    // 尝试在这些位置放置方块
                    for (const {row, col, distance} of candidatePositions) {
                        for (let rotationIdx = 0; rotationIdx < shapes.length; rotationIdx++) {
                            const shape = shapes[rotationIdx];

                            if (this.canPlacePiece(board, shape, row, col)) {
                                const [validPlacement, coversRequired] = this.isValidPlacement(board, shape, row, col);

                                if (validPlacement) {
                                    // 计算这个放置的优先级
                                    let priority = 0;
                                    priority += coversRequired * 100; // 覆盖红色格子得高分
                                    priority -= distance * 10; // 距离红色格子近得分
                                    priority += Math.random() * 5; // 添加随机性避免死循环

                                    bestPlacements.push({
                                        pieceName,
                                        pieceId,
                                        shape,
                                        row,
                                        col,
                                        priority,
                                        coversRequired
                                    });
                                }
                            }
                        }
                    }
                }

                // 按优先级排序
                bestPlacements.sort((a, b) => b.priority - a.priority);

                // 如果没有找到好的位置，回退到传统搜索
                if (bestPlacements.length === 0) {
                    return await this.solveBacktrackTraditional(board, piecesLeft, depth);
                }

                // 尝试最好的几个位置
                const maxTries = Math.min(bestPlacements.length, 10);
                for (let i = 0; i < maxTries; i++) {
                    const placement = bestPlacements[i];

                    if (!this.solving) return false;

                    this.placePiece(board, placement.shape, placement.row, placement.col, placement.pieceId);

                    if (this.showProcess) {
                        await this.updateSolveDisplay(board,
                            `智能回溯: 放置 ${placement.pieceName} 在(${placement.row},${placement.col}) 覆盖${placement.coversRequired}个红格`,
                            placement.pieceName);
                    }

                    const newPieces = {...piecesLeft};
                    newPieces[placement.pieceName]--;

                    if (await this.solveBacktrack(board, newPieces, depth + 1)) {
                        return true;
                    }

                    this.removePiece(board, placement.shape, placement.row, placement.col);
                    if (this.showProcess) {
                        await this.updateSolveDisplay(board, `智能回溯: 回溯移除 ${placement.pieceName}`, placement.pieceName);
                    }
                }

                return false;
            }

            // 传统回溯算法作为备用（加入状态哈希 + 容量剪枝 + MRV 选择）
            async solveBacktrackTraditional(board, piecesLeft, depth = 0) {
                if (!this.solving) return false;

                // 状态哈希剪枝
                const stateKey = this.getStateKey(board, piecesLeft);
                if (this.visitedStates.has(stateKey)) return false;
                this.visitedStates.add(stateKey);

                // 容量剪枝
                const totalRequired = this.countRequiredCells();
                const coveredRequired = this.countCoveredRequiredCells(board);
                const remainingRequired = totalRequired - coveredRequired;
                let capacity = 0;
                for (const [p, c] of Object.entries(piecesLeft)) {
                    if (c > 0) capacity += (this.tetrisShapes[p][0].length) * c;
                }
                if (capacity < remainingRequired) return false;

                const availablePieces = Object.fromEntries(
                    Object.entries(piecesLeft).filter(([name, count]) => count > 0)
                );

                if (Object.keys(availablePieces).length === 0) {
                    const isValid = this.isValidSolution(board);
                    if (isValid) {
                        await this.updateSolveDisplay(board, "传统回溯法找到解答！", "", true);
                        this.addSolution(board, this.currentAlgorithm, this.solveStep);
                    }
                    return isValid;
                }

                // MRV（最少可放置位置）选择方块
                const pieceName = Object.entries(availablePieces)
                    .map(([name]) => {
                        let ways = 0;
                        const shapes = this.tetrisShapes[name];
                        for (let i = 0; i < this.boardSize; i++) {
                            for (let j = 0; j < this.boardSize; j++) {
                                for (const shape of shapes) {
                                    if (this.canPlacePiece(board, shape, i, j) && this.isValidPlacement(board, shape, i, j)[0]) {
                                        ways++;
                                    }
                                }
                            }
                        }
                        return { name, ways };
                    })
                    .sort((a, b) => a.ways - b.ways)[0].name;

                const shapes = this.tetrisShapes[pieceName];
                const pieceId = Object.keys(this.tetrisShapes).indexOf(pieceName) + 3;

                if (this.showProcess) {
                    await this.updateSolveDisplay(board, `传统回溯尝试放置 ${pieceName}`, pieceName);
                } else {
                    await this.updateSolveDisplay(board, "", pieceName);
                }

                for (let i = 0; i < this.boardSize; i++) {
                    for (let j = 0; j < this.boardSize; j++) {
                        for (const shape of shapes) {
                            if (!this.solving) return false;
                            if (this.canPlacePiece(board, shape, i, j)) {
                                const [validPlacement] = this.isValidPlacement(board, shape, i, j);
                                if (validPlacement) {
                                    this.placePiece(board, shape, i, j, pieceId);
                                    if (this.showProcess) {
                                        await this.updateSolveDisplay(board, `传统回溯放置 ${pieceName} 在(${i},${j})`, pieceName);
                                    }
                                    const newPieces = { ...piecesLeft };
                                    newPieces[pieceName]--;
                                    if (await this.solveBacktrackTraditional(board, newPieces, depth + 1)) {
                                        return true;
                                    }
                                    this.removePiece(board, shape, i, j);
                                    if (this.showProcess) {
                                        await this.updateSolveDisplay(board, `传统回溯回溯：移除 ${pieceName}`, pieceName);
                                    }
                                }
                            }
                        }
                    }
                }

                return false;
            }

            // 贪心算法：每次选择能覆盖最多红色格子的方块放置
            async solveGreedy(board, piecesLeft) {
                if (!this.solving) return false;

                const totalRequired = this.countRequiredCells();
                let placedPieces = 0;

                while (this.solving) {
                    const availablePieces = Object.fromEntries(
                        Object.entries(piecesLeft).filter(([name, count]) => count > 0)
                    );

                    if (Object.keys(availablePieces).length === 0) {
                        break;
                    }

                    let bestPlacement = null;
                    let bestScore = -1;

                    // 寻找最佳放置位置
                    for (const [pieceName, count] of Object.entries(availablePieces)) {
                        const shapes = this.tetrisShapes[pieceName];
                        const pieceId = Object.keys(this.tetrisShapes).indexOf(pieceName) + 3;

                        // 优先考虑红色格子周围的位置
                        const candidatePositions = this.getPositionsAroundRequired(board, 3);

                        for (const {row, col, distance} of candidatePositions) {
                            for (let rotationIdx = 0; rotationIdx < shapes.length; rotationIdx++) {
                                const shape = shapes[rotationIdx];

                                if (this.canPlacePiece(board, shape, row, col)) {
                                    const [validPlacement, coversRequired] = this.isValidPlacement(board, shape, row, col);

                                    if (validPlacement) {
                                        // 贪心评分：优先覆盖红色格子
                                        let score = coversRequired * 1000; // 覆盖红色格子得高分
                                        score -= distance * 10; // 距离红色格子近得分
                                        score += shape.length * 5; // 方块大小也有影响

                                        if (score > bestScore) {
                                            bestScore = score;
                                            bestPlacement = {
                                                pieceName,
                                                pieceId,
                                                shape,
                                                row,
                                                col,
                                                coversRequired
                                            };
                                        }
                                    }
                                }
                            }
                        }
                    }

                    // 如果没有找到好位置，尝试任意位置
                    if (!bestPlacement) {
                        for (const [pieceName, count] of Object.entries(availablePieces)) {
                            const shapes = this.tetrisShapes[pieceName];
                            const pieceId = Object.keys(this.tetrisShapes).indexOf(pieceName) + 3;

                            for (let i = 0; i < this.boardSize && !bestPlacement; i++) {
                                for (let j = 0; j < this.boardSize && !bestPlacement; j++) {
                                    for (let rotationIdx = 0; rotationIdx < shapes.length; rotationIdx++) {
                                        const shape = shapes[rotationIdx];

                                        if (this.canPlacePiece(board, shape, i, j)) {
                                            const [validPlacement, coversRequired] = this.isValidPlacement(board, shape, i, j);

                                            if (validPlacement) {
                                                bestPlacement = {
                                                    pieceName,
                                                    pieceId,
                                                    shape,
                                                    row: i,
                                                    col: j,
                                                    coversRequired
                                                };
                                                break;
                                            }
                                        }
                                    }
                                }
                            }
                            if (bestPlacement) break;
                        }
                    }

                    // 如果找到了最佳位置，放置方块
                    if (bestPlacement) {
                        this.placePiece(board, bestPlacement.shape, bestPlacement.row, bestPlacement.col, bestPlacement.pieceId);
                        piecesLeft[bestPlacement.pieceName]--;
                        placedPieces++;

                        const coveredRequired = this.countCoveredRequiredCells(board);

                        if (this.showProcess) {
                            await this.updateSolveDisplay(board,
                                `贪心算法: 放置 ${bestPlacement.pieceName} 覆盖${bestPlacement.coversRequired}个红格 (${coveredRequired}/${totalRequired})`,
                                bestPlacement.pieceName);
                        }

                        // 每放置几个方块就添加一个候选解决方案
                        if (placedPieces % 2 === 0) {
                            this.addSolution(board, "贪心算法", this.solveStep);
                        }

                        // 检查是否完成
                        if (this.isValidSolution(board)) {
                            await this.updateSolveDisplay(board, "贪心算法找到解答！", "", true);
                            this.addSolution(board, "贪心算法", this.solveStep);
                            return true;
                        }
                    } else {
                        // 没有可放置的方块了
                        break;
                    }
                }

                // 添加最终状态作为候选解决方案
                this.addSolution(board, "贪心算法", this.solveStep);
                return this.isValidSolution(board);
            }

            // 其他算法的简化实现...
            async solveHeuristic(board, piecesLeft, depth = 0) {
                return await this.solveBacktrack(board, piecesLeft, depth);
            }

            async solveRandom(board, piecesLeft, maxAttempts = 1000) {
                return await this.solveBacktrack(board, piecesLeft);
            }

            async solveConstraint(board, piecesLeft) {
                return await this.solveBacktrack(board, piecesLeft);
            }

            stopSolving() {
                this.solving = false;
                document.getElementById('statusMain').textContent = "破解已停止";
                document.getElementById('statusMain').style.backgroundColor = "lightcoral";
                document.getElementById('statusPiece').textContent = "当前方块: 已停止";
            }

            async solvePuzzle(algorithm = "回溯法") {
                if (this.solving) return;

                this.currentAlgorithm = algorithm;

                // 清空之前的解决方案
                this.clearSolutions();

                // 更新方块数量
                try {
                    for (const name of Object.keys(this.pieceCounts)) {
                        const count = parseInt(document.getElementById(`count-${name}`).value);
                        if (count < 0) {
                            throw new Error("数量不能为负数");
                        }
                        this.pieceCounts[name] = count;
                    }
                } catch (e) {
                    alert(`请输入有效的数字！\n${e.message}`);
                    return;
                }

                // 检查是否有方块可以放置
                const totalPieces = Object.values(this.pieceCounts).reduce((sum, count) => sum + count, 0);
                if (totalPieces === 0) {
                    alert("请至少设置一个方块的数量大于0！");
                    return;
                }

                // 检查是否有必需的位置
                const hasRequired = this.board.some(row => row.some(cell => cell === 1));

                if (!hasRequired) {
                    if (!confirm("没有标记必需位置，是否继续破解？")) {
                        return;
                    }
                }

                // 开始破解
                this.solving = true;
                this.solveStep = 0;
                this.visitedStates.clear();

                // 创建工作棋盘
                const workBoard = Array(this.boardSize).fill(null).map(() => Array(this.boardSize).fill(0));
                const piecesLeft = {...this.pieceCounts};

                // 设置算法状态
                document.getElementById('statusAlgorithm').textContent = `算法: ${algorithm}`;

                if (this.showProcess) {
                    document.getElementById('statusMain').textContent = `开始${algorithm}破解...`;
                    document.getElementById('statusMain').style.backgroundColor = "yellow";
                } else {
                    document.getElementById('statusMain').textContent = `${algorithm}快速破解中...`;
                    document.getElementById('statusMain').style.backgroundColor = "yellow";
                }

                document.getElementById('statusPiece').textContent = "当前方块: 准备中...";

                const startTime = Date.now();

                try {
                    let success = false;

                    // 根据选择的算法调用相应的求解函数
                    switch (algorithm) {
                        case "智能回溯法":
                            success = await this.solveBacktrack(workBoard, piecesLeft);
                            break;
                        case "回溯法":
                            success = await this.solveBacktrackTraditional(workBoard, piecesLeft);
                            break;
                        case "贪心算法":
                            success = await this.solveGreedy(workBoard, piecesLeft);
                            break;
                        case "启发式搜索":
                            success = await this.solveHeuristic(workBoard, piecesLeft);
                            break;
                        case "随机搜索":
                            success = await this.solveRandom(workBoard, piecesLeft);
                            break;
                        case "约束传播":
                            success = await this.solveConstraint(workBoard, piecesLeft);
                            break;
                        default:
                            success = await this.solveBacktrack(workBoard, piecesLeft);
                    }

                    const endTime = Date.now();
                    const solveTime = ((endTime - startTime) / 1000).toFixed(2);

                    if (success) {
                        const successMsg = `${algorithm}成功找到解答！\n总共尝试了 ${this.solveStep} 步\n用时: ${solveTime} 秒\n找到 ${this.solutions.length} 个解决方案`;
                        document.getElementById('statusMain').textContent = `${algorithm}破解成功！(${this.solveStep}步, ${solveTime}秒)`;
                        document.getElementById('statusMain').style.backgroundColor = "lightgreen";
                        document.getElementById('statusPiece').textContent = "当前方块: 完成！";
                        alert(successMsg);
                    } else {
                        const failMsg = `${algorithm}无法找到有效解答！\n已尝试了 ${this.solveStep} 步\n用时: ${solveTime} 秒\n找到 ${this.solutions.length} 个候选方案\n请检查标记和方块数量设置。`;
                        document.getElementById('statusMain').textContent = `${algorithm}无解 (${this.solveStep}步, ${solveTime}秒)`;
                        document.getElementById('statusMain').style.backgroundColor = "lightcoral";
                        document.getElementById('statusPiece').textContent = "当前方块: 无解";
                        alert(failMsg);
                    }
                } catch (e) {
                    document.getElementById('statusMain').textContent = `${algorithm}破解出错`;
                    document.getElementById('statusMain').style.backgroundColor = "red";
                    document.getElementById('statusPiece').textContent = "当前方块: 错误";
                    alert(`${algorithm}求解过程中出现错误：${e.message}`);
                } finally {
                    this.solving = false;
                }
            }
        }

        // 创建游戏实例
        let game;

        // 全局函数（供HTML调用）
        function startMarking() {
            alert("开始标注模式！\n左键：标记正确位置(红色)\n右键：标记错误位置(灰色)\n双击：清除标记");
        }

        function clearAllMarks() {
            game.clearAllMarks();
        }

        function solvePuzzle(algorithm) {
            game.solvePuzzle(algorithm);
        }

        function stopSolving() {
            game.stopSolving();
        }

        function exportBoard() {
            game.exportBoard();
        }

        function importBoard() {
            game.importBoard();
        }

        // 页面加载完成后初始化游戏
        document.addEventListener('DOMContentLoaded', function() {
            game = new TetrisPuzzleGame();
        });
    </script>
</body>
</html>