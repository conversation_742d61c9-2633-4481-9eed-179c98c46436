# 安装说明 (Installation Guide)

## 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, 或 Linux (Ubuntu 20.04+)
- **Qt版本**: Qt 6.2 或更高版本
- **编译器**: 
  - Windows: MSVC 2019+ 或 MinGW 8.1+
  - macOS: Xcode 12+ (Clang)
  - Linux: GCC 9+ 或 Clang 10+
- **CMake**: 3.16 或更高版本 (可选)

## 安装Qt6

### Windows

#### 方法1: 官方安装器 (推荐)
1. 访问 [Qt官网](https://www.qt.io/download)
2. 下载 "Qt Online Installer"
3. 运行安装器，选择以下组件：
   - Qt 6.5.x (最新版本)
   - MinGW 11.2.0 64-bit (如果没有Visual Studio)
   - Qt Creator (IDE，可选)
   - CMake (如果系统没有)

#### 方法2: 使用包管理器
```powershell
# 使用 Chocolatey
choco install qt6

# 或使用 vcpkg
vcpkg install qt6[core,widgets]
```

### macOS

#### 方法1: Homebrew (推荐)
```bash
# 安装 Homebrew (如果还没有)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装 Qt6
brew install qt6

# 安装 CMake (如果需要)
brew install cmake
```

#### 方法2: 官方安装器
1. 访问 [Qt官网](https://www.qt.io/download)
2. 下载 macOS 版本的安装器
3. 按照安装向导完成安装

### Linux (Ubuntu/Debian)

```bash
# 更新包列表
sudo apt update

# 安装 Qt6 开发包
sudo apt install qt6-base-dev qt6-tools-dev

# 安装构建工具
sudo apt install build-essential cmake

# 安装额外的Qt6模块 (如果需要)
sudo apt install qt6-base-dev-tools
```

### Linux (CentOS/RHEL/Fedora)

```bash
# Fedora
sudo dnf install qt6-qtbase-devel qt6-qttools-devel cmake gcc-c++

# CentOS/RHEL (需要EPEL仓库)
sudo yum install epel-release
sudo yum install qt6-qtbase-devel qt6-qttools-devel cmake gcc-c++
```

## 验证安装

安装完成后，验证Qt6是否正确安装：

```bash
# 检查qmake版本
qmake --version

# 检查CMake版本 (如果使用CMake)
cmake --version
```

应该看到类似以下输出：
```
QMake version 3.1
Using Qt version 6.5.x in /path/to/qt6
```

## 环境变量设置

### Windows
如果使用官方安装器，通常会自动设置环境变量。如果没有，请手动添加：

1. 打开"系统属性" -> "高级" -> "环境变量"
2. 在"系统变量"中添加或修改：
   - `Qt6_DIR`: Qt6安装目录 (例如: `C:\Qt\6.5.0\mingw_64`)
   - `PATH`: 添加 `%Qt6_DIR%\bin` 和 `%Qt6_DIR%\lib`

### macOS/Linux
如果使用包管理器安装，通常会自动设置。如果使用官方安装器，可能需要：

```bash
# 添加到 ~/.bashrc 或 ~/.zshrc
export Qt6_DIR=/path/to/qt6
export PATH=$Qt6_DIR/bin:$PATH
export LD_LIBRARY_PATH=$Qt6_DIR/lib:$LD_LIBRARY_PATH  # Linux only
```

## 构建项目

安装Qt6后，可以使用以下方法构建项目：

### 方法1: 使用构建脚本 (推荐)

#### Windows
```cmd
build.bat
```

#### macOS/Linux
```bash
./build.sh
```

### 方法2: 手动使用CMake

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### 方法3: 手动使用qmake

```bash
mkdir build-qmake
cd build-qmake
qmake ../TetrisPuzzleGame.pro
make  # Linux/macOS
# 或
nmake  # Windows with MSVC
# 或
mingw32-make  # Windows with MinGW
```

## 运行程序

构建成功后，可执行文件位于：
- CMake构建: `build/` 目录
- qmake构建: `build-qmake/` 目录

### Windows
```cmd
# CMake
build\Release\TetrisPuzzleGame.exe

# qmake
build-qmake\release\TetrisPuzzleGame.exe
```

### macOS/Linux
```bash
# CMake
./build/TetrisPuzzleGame

# qmake
./build-qmake/TetrisPuzzleGame
```

## 故障排除

### 常见问题

1. **"qmake: command not found"**
   - 确保Qt6正确安装
   - 检查PATH环境变量是否包含Qt6的bin目录

2. **"Qt6 not found"**
   - 设置Qt6_DIR环境变量指向Qt6安装目录
   - 或在CMake命令中指定: `cmake -DQt6_DIR=/path/to/qt6 ..`

3. **编译错误: "C++17 not supported"**
   - 确保使用支持C++17的编译器
   - Windows: Visual Studio 2019+ 或 MinGW 8.1+
   - macOS: Xcode 12+
   - Linux: GCC 9+ 或 Clang 10+

4. **运行时错误: "Qt6 DLLs not found"**
   - Windows: 确保Qt6的bin目录在PATH中
   - 或将Qt6 DLL文件复制到可执行文件目录

### 获取帮助

如果遇到问题，可以：
1. 检查Qt6官方文档: https://doc.qt.io/qt-6/
2. 查看CMake文档: https://cmake.org/documentation/
3. 在项目仓库中提交Issue

## 开发环境推荐

### IDE选择
- **Qt Creator**: Qt官方IDE，对Qt项目支持最好
- **Visual Studio**: Windows上的强大IDE
- **CLion**: JetBrains的C++ IDE
- **VS Code**: 轻量级编辑器，配合C++插件

### 调试工具
- **Qt Creator调试器**: 内置调试支持
- **GDB**: Linux/macOS命令行调试器
- **MSVC调试器**: Windows Visual Studio调试器
