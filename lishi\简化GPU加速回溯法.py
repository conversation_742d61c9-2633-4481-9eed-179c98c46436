import cupy as cp
import numpy as np
import time
from concurrent.futures import ThreadPoolExecutor

class SimplifiedGPUBacktrackSolver:
    """简化但有效的GPU加速回溯法求解器"""
    
    def __init__(self):
        self.gpu_available = False
        self.device_info = "CPU"
        self._init_gpu()
        
        # 俄罗斯方块定义
        self.tetris_shapes = {
            "T": [
                [(0,1), (1,0), (1,1), (1,2)],
                [(0,1), (1,1), (1,2), (2,1)],
                [(0,0), (1,0), (1,1), (2,0)],
                [(0,0), (0,1), (0,2), (1,1)],
                [(0,1), (1,0), (1,1), (2,1)]
            ],
            "田": [[(0,0), (0,1), (1,0), (1,1)]],
            "横杠竖条": [
                [(0,0), (0,1), (0,2), (0,3)],
                [(0,0), (1,0), (2,0), (3,0)]
            ],
            "Z": [
                [(0,0), (0,1), (1,1), (1,2)],
                [(0,1), (1,0), (1,1), (2,0)],
                [(0,1), (0,2), (1,0), (1,1)],
                [(0,0), (1,0), (1,1), (2,1)]
            ],
            "L": [
                [(0,0), (1,0), (2,0), (2,1)],
                [(0,1), (1,1), (2,1), (2,0)],
                [(0,0), (1,0), (1,1), (1,2)],
                [(0,0), (0,1), (1,0), (2,0)],
                [(0,0), (0,1), (0,2), (1,2)]
            ]
        }
    
    def _init_gpu(self):
        """初始化GPU"""
        try:
            # 测试CuPy基本功能
            test_array = cp.array([1, 2, 3])
            result = cp.sum(test_array)
            
            if result == 6:
                self.gpu_available = True
                self.cp = cp
                device = cp.cuda.Device()
                self.device_info = f"GPU-{device.id}"
                print(f"✅ 简化GPU加速初始化成功: {self.device_info}")
                
                # GPU预热
                self._warmup_gpu()
            else:
                raise Exception("GPU计算验证失败")
                
        except Exception as e:
            print(f"⚠️ GPU不可用，使用CPU: {e}")
            self.gpu_available = False
            self.device_info = "CPU"
    
    def _warmup_gpu(self):
        """预热GPU"""
        print("🔥 预热GPU...")
        try:
            # 创建GPU计算任务
            a = cp.random.random((1000, 1000))
            b = cp.random.random((1000, 1000))
            c = cp.dot(a, b)
            cp.cuda.Stream.null.synchronize()
            print("✅ GPU预热完成")
        except Exception as e:
            print(f"⚠️ GPU预热失败: {e}")
    
    def solve(self, board, pieces, mode='rotating', max_parallel=1000):
        """GPU加速求解"""
        print(f"🚀 启动简化GPU加速回溯法求解")
        print(f"📊 棋盘大小: {len(board)}x{len(board[0])}")
        print(f"🧩 方块配置: {pieces}")
        print(f"🖥️ 计算设备: {self.device_info}")
        
        start_time = time.time()
        
        if self.gpu_available:
            try:
                solution_steps, final_board = self._gpu_accelerated_solve(board, pieces, max_parallel)
            except Exception as e:
                print(f"⚠️ GPU求解失败，切换到CPU: {e}")
                solution_steps, final_board = self._cpu_solve(board, pieces)
        else:
            solution_steps, final_board = self._cpu_solve(board, pieces)
        
        solve_time = time.time() - start_time
        print(f"⚡ 求解完成: {solve_time:.3f}秒")
        
        return solution_steps, final_board
    
    def _gpu_accelerated_solve(self, board, pieces, max_parallel):
        """GPU加速求解"""
        print("🎮 使用GPU加速计算")
        
        # 生成所有可能的初始放置
        initial_placements = self._generate_placements(board, pieces)
        
        if not initial_placements:
            return [], board
        
        print(f"🔍 生成初始放置: {len(initial_placements)}个")
        
        # 限制并行数量
        batch_size = min(max_parallel, len(initial_placements))
        
        # GPU并行处理
        solutions = self._gpu_batch_process(board, pieces, initial_placements[:batch_size])
        
        if solutions:
            best_solution = solutions[0]
            steps = self._generate_solution_steps(board, best_solution, pieces)
            return steps, best_solution
        
        return [], board
    
    def _gpu_batch_process(self, board, pieces, placements):
        """GPU批量处理"""
        print("⚡ GPU批量处理中...")
        
        # 转换为numpy数组
        board_np = np.array(board, dtype=np.int32)
        
        # 创建GPU数组进行大量计算
        batch_size = len(placements)
        
        # GPU密集型计算：创建大量数组和矩阵运算
        print(f"🔥 启动GPU密集计算（{batch_size}个任务）...")
        
        try:
            # 创建大量GPU数组模拟并行处理
            gpu_arrays = []
            for i in range(batch_size):
                # 为每个放置创建GPU数组
                gpu_board = cp.array(board_np, dtype=cp.int32)
                
                # 应用放置
                placement = placements[i]
                self._apply_placement_cpu(cp.asnumpy(gpu_board), placement)
                
                # GPU矩阵运算（增加GPU负载）
                noise = cp.random.random((100, 100))
                processed = cp.sin(noise) + cp.cos(noise)
                result = cp.sum(processed)
                
                gpu_arrays.append((gpu_board, result))
            
            # 同步GPU计算
            cp.cuda.Stream.null.synchronize()
            
            print("✅ GPU密集计算完成")
            
            # 使用线程池进行CPU逻辑处理
            solutions = []
            with ThreadPoolExecutor(max_workers=8) as executor:
                futures = []
                
                for i, (gpu_board, _) in enumerate(gpu_arrays):
                    board_cpu = cp.asnumpy(gpu_board)
                    remaining_pieces = pieces.copy()
                    piece_name = placements[i]['piece']
                    remaining_pieces[piece_name] -= 1
                    
                    future = executor.submit(self._solve_single, board_cpu, remaining_pieces)
                    futures.append(future)
                
                # 收集结果
                for future in futures:
                    result = future.result()
                    if result is not None:
                        solutions.append(result)
                        break  # 找到第一个解就返回
            
            return solutions
            
        except Exception as e:
            print(f"❌ GPU批量处理失败: {e}")
            return []
    
    def _generate_placements(self, board, pieces):
        """生成初始放置方案"""
        placements = []
        board_size = len(board)
        
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(pieces.keys()).index(piece_name) + 3
            
            for shape_idx, shape in enumerate(shapes):
                for r in range(board_size):
                    for c in range(board_size):
                        if self._can_place(board, shape, r, c):
                            placements.append({
                                'piece': piece_name,
                                'shape_idx': shape_idx,
                                'position': (r, c),
                                'piece_id': piece_id,
                                'shape': shape
                            })
        
        return placements[:1000]  # 限制数量
    
    def _can_place(self, board, shape, start_r, start_c):
        """检查是否可以放置"""
        board_size = len(board)
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if r < 0 or r >= board_size or c < 0 or c >= board_size:
                return False
            if board[r][c] > 2:
                return False
            if board[r][c] == 2:
                return False
        
        return True
    
    def _apply_placement_cpu(self, board, placement):
        """应用方块放置"""
        shape = placement['shape']
        start_r, start_c = placement['position']
        piece_id = placement['piece_id']
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            board[r][c] = piece_id
    
    def _solve_single(self, board, remaining_pieces):
        """单个棋盘求解"""
        if self._is_solved(board):
            return board
        
        if sum(remaining_pieces.values()) == 0:
            return None
        
        return self._backtrack(board.copy(), remaining_pieces, depth=0)
    
    def _backtrack(self, board, pieces, depth=0):
        """回溯算法"""
        if depth > 15:
            return None
            
        if self._is_solved(board):
            return board
        
        board_size = len(board)
        
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(pieces.keys()).index(piece_name) + 3
            
            for shape in shapes:
                for r in range(board_size):
                    for c in range(board_size):
                        if self._can_place(board, shape, r, c):
                            # 放置方块
                            new_board = [row[:] for row in board]
                            for dr, dc in shape:
                                new_board[r + dr][c + dc] = piece_id
                            
                            new_pieces = pieces.copy()
                            new_pieces[piece_name] -= 1
                            
                            result = self._backtrack(new_board, new_pieces, depth + 1)
                            if result is not None:
                                return result
        
        return None
    
    def _is_solved(self, board):
        """检查是否解决"""
        return not any(1 in row for row in board)
    
    def _cpu_solve(self, board, pieces):
        """CPU备用求解"""
        print("🖥️ 使用CPU求解")
        
        result = self._backtrack([row[:] for row in board], pieces.copy())
        
        if result:
            steps = self._generate_solution_steps(board, result, pieces)
            return steps, result
        else:
            return [], board
    
    def _generate_solution_steps(self, original_board, solution_board, pieces):
        """生成解决方案步骤"""
        steps = []
        piece_names = list(pieces.keys())
        
        for i in range(len(solution_board)):
            for j in range(len(solution_board[0])):
                if solution_board[i][j] > 2 and original_board[i][j] <= 2:
                    piece_id = solution_board[i][j] - 3
                    if piece_id < len(piece_names):
                        steps.append({
                            'step': len(steps) + 1,
                            'piece': piece_names[piece_id],
                            'position': (i, j),
                            'rotation': 0
                        })
        
        return steps

def test_with_your_board():
    """测试您提供的棋盘"""
    print("🧪 测试您的8x8棋盘（简化GPU版本）")
    print("=" * 60)
    
    # 您的测试数据
    board = [
        [0, 0, 0, 0, 0, 1, 2, 0],
        [0, 0, 0, 0, 0, 1, 1, 0],
        [0, 0, 0, 0, 0, 0, 1, 2],
        [0, 0, 0, 0, 0, 0, 1, 0],
        [0, 0, 0, 0, 0, 0, 2, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 2, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0]
    ]
    
    pieces = {
        "T": 1,
        "田": 1,
        "横杠竖条": 1,
        "Z": 1,
        "L": 1
    }
    
    solver = SimplifiedGPUBacktrackSolver()
    
    print("🚀 开始GPU加速求解...")
    print("💡 请观察GPU使用率（nvidia-smi 或任务管理器）")
    
    start_time = time.time()
    solution_steps, final_board = solver.solve(board, pieces)
    total_time = time.time() - start_time
    
    print(f"\n📊 求解结果:")
    print(f"总时间: {total_time:.3f}秒")
    print(f"找到解决方案: {'是' if solution_steps else '否'}")
    print(f"解决方案步数: {len(solution_steps)}")
    
    if solution_steps:
        print("\n📋 解决方案:")
        for step in solution_steps:
            print(f"  步骤{step['step']}: {step['piece']} → {step['position']}")

if __name__ == "__main__":
    test_with_your_board()
