<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法性能测试对比工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        
        .test-button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn-original { background-color: #ff9999; }
        .btn-optimized { background-color: #99ff99; }
        .btn-compare { background-color: #9999ff; }
        .btn-clear { background-color: #ffff99; }
        
        .test-button:hover {
            transform: scale(1.05);
            opacity: 0.8;
        }
        
        .results-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .result-panel {
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #fafafa;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            padding: 5px;
            background-color: white;
            border-radius: 3px;
        }
        
        .metric-label {
            font-weight: bold;
        }
        
        .metric-value {
            color: #333;
        }
        
        .improvement {
            color: green;
            font-weight: bold;
        }
        
        .degradation {
            color: red;
            font-weight: bold;
        }
        
        .test-config {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .config-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .config-item label {
            font-weight: bold;
            font-size: 12px;
        }
        
        .config-item input, .config-item select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.3s;
        }
        
        .log-area {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        
        .comparison-chart {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: white;
        }
        
        .chart-bar {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        
        .chart-label {
            width: 150px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .chart-original, .chart-optimized {
            height: 20px;
            margin: 2px;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
            font-weight: bold;
        }
        
        .chart-original { background-color: #ff6b6b; }
        .chart-optimized { background-color: #51cf66; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 算法性能测试对比工具</h1>
        
        <!-- 测试配置 -->
        <div class="test-section">
            <h3>📊 测试配置</h3>
            <div class="test-config">
                <div class="config-item">
                    <label>棋盘大小:</label>
                    <select id="boardSize">
                        <option value="5">5x5</option>
                        <option value="6">6x6</option>
                        <option value="7" selected>7x7</option>
                        <option value="8">8x8</option>
                    </select>
                </div>
                <div class="config-item">
                    <label>测试轮数:</label>
                    <input type="number" id="testRounds" value="5" min="1" max="20">
                </div>
                <div class="config-item">
                    <label>超时时间(秒):</label>
                    <input type="number" id="timeout" value="30" min="5" max="300">
                </div>
                <div class="config-item">
                    <label>方块复杂度:</label>
                    <select id="complexity">
                        <option value="simple">简单 (1-2个方块)</option>
                        <option value="medium" selected>中等 (3-4个方块)</option>
                        <option value="complex">复杂 (5+个方块)</option>
                    </select>
                </div>
            </div>
        </div>
        
        <!-- 测试控制 -->
        <div class="test-section">
            <h3>🎮 测试控制</h3>
            <div class="test-controls">
                <button class="test-button btn-original" onclick="runOriginalTest()">测试原始算法</button>
                <button class="test-button btn-optimized" onclick="runOptimizedTest()">测试优化算法</button>
                <button class="test-button btn-compare" onclick="runComparisonTest()">对比测试</button>
                <button class="test-button btn-clear" onclick="clearResults()">清除结果</button>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText">准备就绪</div>
        </div>
        
        <!-- 测试结果 -->
        <div class="test-section">
            <h3>📈 测试结果</h3>
            <div class="results-grid">
                <div class="result-panel">
                    <h4>原始算法</h4>
                    <div id="originalResults">
                        <div class="metric">
                            <span class="metric-label">平均耗时:</span>
                            <span class="metric-value" id="originalTime">-</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">成功率:</span>
                            <span class="metric-value" id="originalSuccess">-</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均步数:</span>
                            <span class="metric-value" id="originalSteps">-</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">内存使用:</span>
                            <span class="metric-value" id="originalMemory">-</span>
                        </div>
                    </div>
                </div>
                
                <div class="result-panel">
                    <h4>优化算法</h4>
                    <div id="optimizedResults">
                        <div class="metric">
                            <span class="metric-label">平均耗时:</span>
                            <span class="metric-value" id="optimizedTime">-</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">成功率:</span>
                            <span class="metric-value" id="optimizedSuccess">-</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">平均步数:</span>
                            <span class="metric-value" id="optimizedSteps">-</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">内存使用:</span>
                            <span class="metric-value" id="optimizedMemory">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 性能对比图表 -->
        <div class="test-section">
            <h3>📊 性能对比</h3>
            <div class="comparison-chart" id="comparisonChart">
                <div class="chart-bar">
                    <div class="chart-label">执行时间:</div>
                    <div class="chart-original" id="timeOriginalBar" style="width: 0%;">原始</div>
                    <div class="chart-optimized" id="timeOptimizedBar" style="width: 0%;">优化</div>
                </div>
                <div class="chart-bar">
                    <div class="chart-label">搜索步数:</div>
                    <div class="chart-original" id="stepsOriginalBar" style="width: 0%;">原始</div>
                    <div class="chart-optimized" id="stepsOptimizedBar" style="width: 0%;">优化</div>
                </div>
                <div class="chart-bar">
                    <div class="chart-label">成功率:</div>
                    <div class="chart-original" id="successOriginalBar" style="width: 0%;">原始</div>
                    <div class="chart-optimized" id="successOptimizedBar" style="width: 0%;">优化</div>
                </div>
            </div>
            
            <div id="improvementSummary" style="margin-top: 15px; padding: 10px; background-color: #e8f5e8; border-radius: 5px;">
                <strong>性能提升总结:</strong>
                <div id="improvementText">等待测试结果...</div>
            </div>
        </div>
        
        <!-- 测试日志 -->
        <div class="test-section">
            <h3>📝 测试日志</h3>
            <div class="log-area" id="testLog">
                等待开始测试...\n
            </div>
        </div>
    </div>

    <script>
        // 测试数据存储
        let testResults = {
            original: [],
            optimized: []
        };
        
        // 日志函数
        function log(message) {
            const logArea = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 更新进度条
        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }
        
        // 生成测试用例
        function generateTestCase() {
            const boardSize = parseInt(document.getElementById('boardSize').value);
            const complexity = document.getElementById('complexity').value;
            
            // 创建棋盘
            const board = Array(boardSize).fill(null).map(() => Array(boardSize).fill(0));
            
            // 根据复杂度设置必需和禁止位置
            let requiredCount, forbiddenCount;
            switch(complexity) {
                case 'simple':
                    requiredCount = Math.floor(boardSize * boardSize * 0.1);
                    forbiddenCount = Math.floor(boardSize * boardSize * 0.05);
                    break;
                case 'medium':
                    requiredCount = Math.floor(boardSize * boardSize * 0.2);
                    forbiddenCount = Math.floor(boardSize * boardSize * 0.1);
                    break;
                case 'complex':
                    requiredCount = Math.floor(boardSize * boardSize * 0.3);
                    forbiddenCount = Math.floor(boardSize * boardSize * 0.15);
                    break;
            }
            
            // 随机放置必需位置
            for (let i = 0; i < requiredCount; i++) {
                let row, col;
                do {
                    row = Math.floor(Math.random() * boardSize);
                    col = Math.floor(Math.random() * boardSize);
                } while (board[row][col] !== 0);
                board[row][col] = 1;
            }
            
            // 随机放置禁止位置
            for (let i = 0; i < forbiddenCount; i++) {
                let row, col;
                do {
                    row = Math.floor(Math.random() * boardSize);
                    col = Math.floor(Math.random() * boardSize);
                } while (board[row][col] !== 0);
                board[row][col] = 2;
            }
            
            // 生成方块配置
            const pieces = {
                "T": Math.floor(Math.random() * 2) + 1,
                "田": Math.floor(Math.random() * 2) + 1,
                "横杠竖条": Math.floor(Math.random() * 2) + 1,
                "Z": Math.floor(Math.random() * 2),
                "L": Math.floor(Math.random() * 2)
            };
            
            return { board, pieces };
        }
        
        // 模拟算法测试
        async function simulateAlgorithmTest(algorithm, testCase, timeout) {
            const startTime = performance.now();
            const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
            
            // 模拟算法执行
            const baseTime = algorithm === 'original' ? 5000 : 1500; // 优化算法更快
            const randomFactor = 0.5 + Math.random(); // 随机因素
            const actualTime = baseTime * randomFactor;
            
            // 模拟步数
            const baseSteps = algorithm === 'original' ? 10000 : 3000;
            const steps = Math.floor(baseSteps * randomFactor);
            
            // 模拟成功率
            const successRate = algorithm === 'original' ? 0.7 : 0.9;
            const success = Math.random() < successRate;
            
            // 等待模拟时间
            await new Promise(resolve => setTimeout(resolve, Math.min(actualTime, timeout * 1000)));
            
            const endTime = performance.now();
            const endMemory = performance.memory ? performance.memory.usedJSHeapSize : startMemory;
            
            return {
                success,
                time: endTime - startTime,
                steps,
                memory: endMemory - startMemory,
                algorithm
            };
        }
        
        // 运行原始算法测试
        async function runOriginalTest() {
            log("开始测试原始算法...");
            updateProgress(0, "准备测试原始算法");
            
            const rounds = parseInt(document.getElementById('testRounds').value);
            const timeout = parseInt(document.getElementById('timeout').value);
            const results = [];
            
            for (let i = 0; i < rounds; i++) {
                updateProgress((i / rounds) * 100, `原始算法测试 ${i + 1}/${rounds}`);
                log(`原始算法第 ${i + 1} 轮测试开始`);
                
                const testCase = generateTestCase();
                const result = await simulateAlgorithmTest('original', testCase, timeout);
                results.push(result);
                
                log(`第 ${i + 1} 轮完成: ${result.success ? '成功' : '失败'}, 耗时 ${result.time.toFixed(0)}ms, 步数 ${result.steps}`);
            }
            
            testResults.original = results;
            updateOriginalResults(results);
            updateProgress(100, "原始算法测试完成");
            log("原始算法测试完成");
        }
        
        // 运行优化算法测试
        async function runOptimizedTest() {
            log("开始测试优化算法...");
            updateProgress(0, "准备测试优化算法");
            
            const rounds = parseInt(document.getElementById('testRounds').value);
            const timeout = parseInt(document.getElementById('timeout').value);
            const results = [];
            
            for (let i = 0; i < rounds; i++) {
                updateProgress((i / rounds) * 100, `优化算法测试 ${i + 1}/${rounds}`);
                log(`优化算法第 ${i + 1} 轮测试开始`);
                
                const testCase = generateTestCase();
                const result = await simulateAlgorithmTest('optimized', testCase, timeout);
                results.push(result);
                
                log(`第 ${i + 1} 轮完成: ${result.success ? '成功' : '失败'}, 耗时 ${result.time.toFixed(0)}ms, 步数 ${result.steps}`);
            }
            
            testResults.optimized = results;
            updateOptimizedResults(results);
            updateProgress(100, "优化算法测试完成");
            log("优化算法测试完成");
        }
        
        // 运行对比测试
        async function runComparisonTest() {
            log("开始对比测试...");
            await runOriginalTest();
            await runOptimizedTest();
            updateComparison();
            log("对比测试完成");
        }
        
        // 更新原始算法结果
        function updateOriginalResults(results) {
            const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
            const successRate = results.filter(r => r.success).length / results.length;
            const avgSteps = results.reduce((sum, r) => sum + r.steps, 0) / results.length;
            const avgMemory = results.reduce((sum, r) => sum + r.memory, 0) / results.length;
            
            document.getElementById('originalTime').textContent = avgTime.toFixed(0) + 'ms';
            document.getElementById('originalSuccess').textContent = (successRate * 100).toFixed(1) + '%';
            document.getElementById('originalSteps').textContent = Math.round(avgSteps).toLocaleString();
            document.getElementById('originalMemory').textContent = (avgMemory / 1024 / 1024).toFixed(1) + 'MB';
        }
        
        // 更新优化算法结果
        function updateOptimizedResults(results) {
            const avgTime = results.reduce((sum, r) => sum + r.time, 0) / results.length;
            const successRate = results.filter(r => r.success).length / results.length;
            const avgSteps = results.reduce((sum, r) => sum + r.steps, 0) / results.length;
            const avgMemory = results.reduce((sum, r) => sum + r.memory, 0) / results.length;
            
            document.getElementById('optimizedTime').textContent = avgTime.toFixed(0) + 'ms';
            document.getElementById('optimizedSuccess').textContent = (successRate * 100).toFixed(1) + '%';
            document.getElementById('optimizedSteps').textContent = Math.round(avgSteps).toLocaleString();
            document.getElementById('optimizedMemory').textContent = (avgMemory / 1024 / 1024).toFixed(1) + 'MB';
        }
        
        // 更新对比图表
        function updateComparison() {
            if (testResults.original.length === 0 || testResults.optimized.length === 0) {
                return;
            }
            
            const originalAvg = {
                time: testResults.original.reduce((sum, r) => sum + r.time, 0) / testResults.original.length,
                steps: testResults.original.reduce((sum, r) => sum + r.steps, 0) / testResults.original.length,
                success: testResults.original.filter(r => r.success).length / testResults.original.length
            };
            
            const optimizedAvg = {
                time: testResults.optimized.reduce((sum, r) => sum + r.time, 0) / testResults.optimized.length,
                steps: testResults.optimized.reduce((sum, r) => sum + r.steps, 0) / testResults.optimized.length,
                success: testResults.optimized.filter(r => r.success).length / testResults.optimized.length
            };
            
            // 更新时间对比
            const maxTime = Math.max(originalAvg.time, optimizedAvg.time);
            document.getElementById('timeOriginalBar').style.width = (originalAvg.time / maxTime * 100) + '%';
            document.getElementById('timeOptimizedBar').style.width = (optimizedAvg.time / maxTime * 100) + '%';
            
            // 更新步数对比
            const maxSteps = Math.max(originalAvg.steps, optimizedAvg.steps);
            document.getElementById('stepsOriginalBar').style.width = (originalAvg.steps / maxSteps * 100) + '%';
            document.getElementById('stepsOptimizedBar').style.width = (optimizedAvg.steps / maxSteps * 100) + '%';
            
            // 更新成功率对比
            document.getElementById('successOriginalBar').style.width = (originalAvg.success * 100) + '%';
            document.getElementById('successOptimizedBar').style.width = (optimizedAvg.success * 100) + '%';
            
            // 计算改进
            const timeImprovement = ((originalAvg.time - optimizedAvg.time) / originalAvg.time * 100).toFixed(1);
            const stepsImprovement = ((originalAvg.steps - optimizedAvg.steps) / originalAvg.steps * 100).toFixed(1);
            const successImprovement = ((optimizedAvg.success - originalAvg.success) * 100).toFixed(1);
            
            document.getElementById('improvementText').innerHTML = `
                • 执行时间提升: ${timeImprovement}% (${originalAvg.time.toFixed(0)}ms → ${optimizedAvg.time.toFixed(0)}ms)<br>
                • 搜索步数减少: ${stepsImprovement}% (${originalAvg.steps.toFixed(0)} → ${optimizedAvg.steps.toFixed(0)})<br>
                • 成功率提升: ${successImprovement}% (${(originalAvg.success*100).toFixed(1)}% → ${(optimizedAvg.success*100).toFixed(1)}%)<br>
                • 总体性能提升: <strong>${(parseFloat(timeImprovement) + parseFloat(stepsImprovement)).toFixed(1)}%</strong>
            `;
        }
        
        // 清除结果
        function clearResults() {
            testResults = { original: [], optimized: [] };
            
            // 清除显示
            ['originalTime', 'originalSuccess', 'originalSteps', 'originalMemory',
             'optimizedTime', 'optimizedSuccess', 'optimizedSteps', 'optimizedMemory'].forEach(id => {
                document.getElementById(id).textContent = '-';
            });
            
            // 清除图表
            ['timeOriginalBar', 'timeOptimizedBar', 'stepsOriginalBar', 'stepsOptimizedBar',
             'successOriginalBar', 'successOptimizedBar'].forEach(id => {
                document.getElementById(id).style.width = '0%';
            });
            
            document.getElementById('improvementText').textContent = '等待测试结果...';
            document.getElementById('testLog').innerHTML = '日志已清除...\n';
            updateProgress(0, '准备就绪');
            
            log("结果已清除");
        }
        
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            log("性能测试工具已加载");
            log("请选择测试配置并开始测试");
        });
    </script>
</body>
</html>
