<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎲 吹牛游戏策略助手 🎲</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            overflow-x: hidden;
        }

        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
            max-width: 800px;
            width: 90%;
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .title {
            text-align: center;
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            text-align: center;
            font-size: 1.1em;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .game-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .game-section:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-2px);
        }

        .section-title {
            font-size: 1.4em;
            margin-bottom: 20px;
            color: #FFD700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dice-container {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 15px;
            margin: 20px 0;
        }

        .dice-input {
            text-align: center;
        }

        .dice-label {
            display: block;
            font-size: 2em;
            margin-bottom: 8px;
        }

        .dice-select {
            width: 100%;
            padding: 8px;
            border-radius: 10px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1.1em;
            text-align: center;
            transition: all 0.3s ease;
        }

        .dice-select:focus {
            outline: none;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
            transform: scale(1.05);
        }

        .opponent-call {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .input-group label {
            font-size: 1.1em;
            color: #FFD700;
        }

        .input-group input, .input-group select {
            padding: 12px;
            border-radius: 10px;
            border: none;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }

        .input-group input:focus, .input-group select:focus {
            outline: none;
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
        }

        .analyze-btn {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.2em;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
            margin: 20px auto;
            display: block;
        }

        .analyze-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        .analyze-btn:active {
            transform: translateY(-1px);
        }

        .result-section {
            margin-top: 30px;
            padding: 25px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 15px;
            border-left: 5px solid #FFD700;
            display: none;
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .strategy-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 20px;
            border-radius: 15px;
            margin: 15px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .strategy-card:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .strategy-title {
            font-size: 1.3em;
            color: #FFD700;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .probability {
            background: rgba(255, 215, 0, 0.2);
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin: 5px 0;
        }

        .rules-section {
            background: rgba(0, 0, 0, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
        }

        .rules-title {
            color: #FFD700;
            font-size: 1.2em;
            margin-bottom: 15px;
        }

        .rules-list {
            list-style: none;
            padding: 0;
        }

        .rules-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .rules-list li:before {
            content: "🎲 ";
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .dice-container {
                grid-template-columns: repeat(3, 1fr);
            }
            
            .opponent-call {
                grid-template-columns: 1fr;
            }
            
            .title {
                font-size: 2em;
            }
        }

        .floating-dice {
            position: fixed;
            font-size: 2em;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <!-- 浮动骰子装饰 -->
    <div class="floating-dice" style="top: 10%; left: 10%;">🎲</div>
    <div class="floating-dice" style="top: 20%; right: 15%; animation-delay: -2s;">🎲</div>
    <div class="floating-dice" style="bottom: 15%; left: 20%; animation-delay: -4s;">🎲</div>
    <div class="floating-dice" style="bottom: 25%; right: 10%; animation-delay: -1s;">🎲</div>

    <div class="container">
        <h1 class="title">🎲 吹牛游戏策略助手 🎲</h1>
        <p class="subtitle">输入你的骰子和对手叫点，获得最佳策略建议</p>

        <!-- 我的骰子 -->
        <div class="game-section">
            <h2 class="section-title">
                <span>🎯</span>
                我的骰子
            </h2>
            <div class="dice-container">
                <div class="dice-input">
                    <label class="dice-label">1️⃣</label>
                    <select class="dice-select" id="dice1">
                        <option value="0">0个</option>
                        <option value="1">1个</option>
                        <option value="2">2个</option>
                        <option value="3">3个</option>
                        <option value="4">4个</option>
                        <option value="5">5个</option>
                    </select>
                </div>
                <div class="dice-input">
                    <label class="dice-label">2️⃣</label>
                    <select class="dice-select" id="dice2">
                        <option value="0">0个</option>
                        <option value="1">1个</option>
                        <option value="2">2个</option>
                        <option value="3">3个</option>
                        <option value="4">4个</option>
                        <option value="5">5个</option>
                    </select>
                </div>
                <div class="dice-input">
                    <label class="dice-label">3️⃣</label>
                    <select class="dice-select" id="dice3">
                        <option value="0">0个</option>
                        <option value="1">1个</option>
                        <option value="2">2个</option>
                        <option value="3">3个</option>
                        <option value="4">4个</option>
                        <option value="5">5个</option>
                    </select>
                </div>
                <div class="dice-input">
                    <label class="dice-label">4️⃣</label>
                    <select class="dice-select" id="dice4">
                        <option value="0">0个</option>
                        <option value="1">1个</option>
                        <option value="2">2个</option>
                        <option value="3">3个</option>
                        <option value="4">4个</option>
                        <option value="5">5个</option>
                    </select>
                </div>
                <div class="dice-input">
                    <label class="dice-label">5️⃣</label>
                    <select class="dice-select" id="dice5">
                        <option value="0">0个</option>
                        <option value="1">1个</option>
                        <option value="2">2个</option>
                        <option value="3">3个</option>
                        <option value="4">4个</option>
                        <option value="5">5个</option>
                    </select>
                </div>
                <div class="dice-input">
                    <label class="dice-label">6️⃣</label>
                    <select class="dice-select" id="dice6">
                        <option value="0">0个</option>
                        <option value="1">1个</option>
                        <option value="2">2个</option>
                        <option value="3">3个</option>
                        <option value="4">4个</option>
                        <option value="5">5个</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- 对手叫点 -->
        <div class="game-section">
            <h2 class="section-title">
                <span>🎭</span>
                对手的叫点
            </h2>
            <div class="opponent-call">
                <div class="input-group">
                    <label>数量</label>
                    <input type="number" id="opponentCount" min="1" max="30" placeholder="例如: 3">
                </div>
                <div class="input-group">
                    <label>点数</label>
                    <select id="opponentDice">
                        <option value="">选择点数</option>
                        <option value="1">1点</option>
                        <option value="2">2点</option>
                        <option value="3">3点</option>
                        <option value="4">4点</option>
                        <option value="5">5点</option>
                        <option value="6">6点</option>
                    </select>
                </div>
            </div>
        </div>

        <button class="analyze-btn" onclick="analyzeStrategy()">
            🧠 分析最佳策略
        </button>

        <!-- 结果显示 -->
        <div id="resultSection" class="result-section">
            <h2 class="section-title">
                <span>💡</span>
                策略建议
            </h2>
            <div id="strategyResult"></div>
        </div>

        <!-- 游戏规则 -->
        <div class="rules-section">
            <h3 class="rules-title">🎲 游戏规则提醒</h3>
            <ul class="rules-list">
                <li>每人5个骰子，1点是万能牌（可以当任何点数）</li>
                <li>轮流叫点，后叫者必须比前者叫得更大</li>
                <li>可以叫更多的相同点数，或相同数量的更大点数</li>
                <li>如果怀疑对方，可以选择"开"，然后数全场骰子</li>
                <li>叫错的人失去一个骰子，最后剩一个骰子的人获胜</li>
            </ul>
        </div>
    </div>

    <script>
        // 吹牛游戏策略分析
        function analyzeStrategy() {
            // 获取我的骰子
            const myDice = {
                1: parseInt(document.getElementById('dice1').value),
                2: parseInt(document.getElementById('dice2').value),
                3: parseInt(document.getElementById('dice3').value),
                4: parseInt(document.getElementById('dice4').value),
                5: parseInt(document.getElementById('dice5').value),
                6: parseInt(document.getElementById('dice6').value)
            };

            // 获取对手叫点
            const opponentCount = parseInt(document.getElementById('opponentCount').value);
            const opponentDice = parseInt(document.getElementById('opponentDice').value);

            // 验证输入
            if (!opponentCount || !opponentDice) {
                alert('请输入对手的叫点信息！');
                return;
            }

            const totalMyDice = Object.values(myDice).reduce((a, b) => a + b, 0);
            if (totalMyDice === 0) {
                alert('请至少选择一个骰子！');
                return;
            }

            // 分析策略
            const strategy = calculateStrategy(myDice, opponentCount, opponentDice, totalMyDice);
            
            // 显示结果
            displayStrategy(strategy);
        }

        function calculateStrategy(myDice, opponentCount, opponentDice, totalMyDice) {
            // 计算我有多少个目标点数（包括1点万能牌，但1点叫1点时不算万能）
            const myTargetCount = opponentDice === 1 ? myDice[1] : (myDice[opponentDice] + myDice[1]);

            // 估算总骰子数（我的+对手的，假设对手剩余骰子数）
            const estimatedOpponentDice = 5; // 可以后续改为用户输入
            const totalDice = totalMyDice + estimatedOpponentDice;

            // 计算对手需要有多少个目标点数
            const opponentNeedCount = opponentCount - myTargetCount;

            // 计算概率和策略
            const analysis = analyzeCall(myTargetCount, opponentCount, opponentDice, totalDice, opponentNeedCount);

            // 生成策略建议
            const strategies = generateStrategies(analysis, myDice, opponentCount, opponentDice);

            return {
                strategies: strategies,
                analysis: analysis
            };
        }

        function analyzeCall(myTargetCount, opponentCount, opponentDice, totalDice, opponentNeedCount) {
            // 简化：假设对手有5个骰子
            const opponentDiceCount = 5;

            const probPerDie = opponentDice === 1 ? 1/6 : 1/3; // 1点时只有1/6，其他点数有1/3（包括万能1点）

            // 关键修正：计算对手叫点成立的概率（不是失败概率！）
            let callSuccessProbability = 0;

            if (opponentNeedCount <= 0) {
                // 我自己的骰子就已经满足了对手的叫点
                callSuccessProbability = 100;
            } else if (opponentNeedCount > opponentDiceCount) {
                // 对手不可能有这么多骰子
                callSuccessProbability = 0;
            } else {
                // 计算对手至少有opponentNeedCount个目标点数的概率
                // 使用简化的二项分布计算
                const expectedOpponentCount = opponentDiceCount * probPerDie;

                if (opponentNeedCount === 1) {
                    // 对手至少有1个的概率 = 1 - 一个都没有的概率
                    const noneProb = Math.pow(1 - probPerDie, opponentDiceCount);
                    callSuccessProbability = (1 - noneProb) * 100;
                } else if (opponentNeedCount === 2) {
                    // 对手至少有2个的概率（简化计算）
                    callSuccessProbability = expectedOpponentCount >= 2 ? 70 : 40;
                } else if (opponentNeedCount === 3) {
                    callSuccessProbability = expectedOpponentCount >= 3 ? 50 : 20;
                } else {
                    // 更多个数，概率递减
                    callSuccessProbability = Math.max(5, 60 - opponentNeedCount * 15);
                }
            }

            // 开牌成功概率 = 100 - 对手叫点成立概率
            const openSuccessProbability = 100 - callSuccessProbability;

            return {
                myTargetCount: myTargetCount,
                opponentNeedCount: opponentNeedCount,
                totalDice: totalDice,
                callSuccessProbability: Math.min(95, Math.max(5, callSuccessProbability)),
                openSuccessProbability: Math.min(95, Math.max(5, openSuccessProbability)),
                expectedOpponentCount: opponentDiceCount * probPerDie,
                riskLevel: opponentNeedCount <= 2 ? 'low' : (opponentNeedCount <= 3 ? 'medium' : 'high')
            };
        }

        function generateStrategies(analysis, myDice, opponentCount, opponentDice) {
            const strategies = [];
            const callProb = analysis.callSuccessProbability;
            const openProb = analysis.openSuccessProbability;

            // 策略1: 开牌 - 当开牌成功率高时
            if (openProb > 60 || analysis.opponentNeedCount > 3) {
                strategies.push({
                    type: '开牌 🎯',
                    icon: '🚨',
                    description: `建议开牌！对手需要${analysis.opponentNeedCount}个点数，成功率较高`,
                    probability: `开牌成功率: ${openProb.toFixed(0)}%`,
                    reason: `对手叫${opponentCount}个${opponentDice}点，你有${analysis.myTargetCount}个，对手需要${analysis.opponentNeedCount}个才能成立。根据概率，对手很可能没有足够的点数。`,
                    priority: 1
                });
            }

            // 策略2: 跟叫 - 当对手叫点可能成立，但我有机会时
            if (callProb >= 30 && callProb <= 80 && openProb < 60) {
                const nextCalls = generateSmartCalls(myDice, opponentCount, opponentDice);
                if (nextCalls.best !== '建议开牌') {
                    strategies.push({
                        type: '跟叫 📈',
                        icon: '🎲',
                        description: `可以跟叫: ${nextCalls.best}`,
                        probability: `对手叫点成立概率: ${callProb.toFixed(0)}%`,
                        reason: `${nextCalls.reason}`,
                        priority: 2,
                        alternatives: nextCalls.alternatives
                    });
                }
            }

            // 策略3: 谨慎应对 - 当对手叫点很可能成立时
            if (callProb > 80) {
                strategies.push({
                    type: '谨慎应对 🛡️',
                    icon: '⚠️',
                    description: '对手叫点很可能成立，不建议开牌',
                    probability: `对手叫点成立概率: ${callProb.toFixed(0)}%`,
                    reason: `对手只需要${analysis.opponentNeedCount}个点数，这在统计上很容易达到。建议寻找更安全的跟叫机会，或者等待更好的开牌时机。`,
                    priority: 3
                });
            }

            // 策略4: 保守开牌 - 当开牌有一定成功率但不高时
            if (openProb >= 40 && openProb <= 60 && analysis.opponentNeedCount >= 2) {
                strategies.push({
                    type: '保守开牌 🤔',
                    icon: '🎯',
                    description: `可以考虑开牌，但有风险`,
                    probability: `开牌成功率: ${openProb.toFixed(0)}%`,
                    reason: `这是一个边界情况。对手需要${analysis.opponentNeedCount}个点数，有一定概率没有，但也可能有。建议根据对手的表现和历史来判断。`,
                    priority: 4
                });
            }

            // 如果没有好策略，给出默认建议
            if (strategies.length === 0) {
                if (analysis.opponentNeedCount <= 1) {
                    strategies.push({
                        type: '非常危险 ⚠️',
                        icon: '🔴',
                        description: '对手叫点几乎肯定成立，强烈不建议开牌',
                        probability: `对手成立概率: ${callProb.toFixed(0)}%`,
                        reason: `对手只需要${analysis.opponentNeedCount}个点数，这几乎是必然的。除非你确定对手在虚张声势，否则不要开牌。`,
                        priority: 5
                    });
                } else {
                    const nextCalls = generateSmartCalls(myDice, opponentCount, opponentDice);
                    strategies.push({
                        type: '尝试跟叫 🎲',
                        icon: '🤷',
                        description: `尝试: ${nextCalls.best}`,
                        probability: `当前情况复杂`,
                        reason: nextCalls.reason,
                        priority: 6
                    });
                }
            }

            // 按优先级排序
            return strategies.sort((a, b) => a.priority - b.priority);
        }

        function generateSmartCalls(myDice, opponentCount, opponentDice) {
            const calls = [];

            // 分析我的手牌优势
            const myStrengths = [];
            for (let d = 1; d <= 6; d++) {
                const count = d === 1 ? myDice[1] : (myDice[d] + myDice[1]);
                myStrengths.push({ dice: d, count: count, total: count });
            }

            // 按我拥有的数量排序
            myStrengths.sort((a, b) => b.count - a.count);

            // 生成可能的叫点（必须比对手大）
            const possibleCalls = [];

            // 1. 叫更多的相同点数
            if (opponentCount < 15) {
                possibleCalls.push({
                    count: opponentCount + 1,
                    dice: opponentDice,
                    type: 'more_same'
                });
            }

            // 2. 叫相同数量的更大点数
            if (opponentDice < 6) {
                for (let d = opponentDice + 1; d <= 6; d++) {
                    possibleCalls.push({
                        count: opponentCount,
                        dice: d,
                        type: 'same_higher'
                    });
                }
            }

            // 3. 基于我的强势点数的叫点
            const myBest = myStrengths[0];
            if (myBest.count >= 2) {
                // 如果我有很多某个点数，可以考虑叫它
                const newCount = Math.max(opponentCount + 1, myBest.count + 1);
                if (newCount <= 12 && (newCount > opponentCount || myBest.dice > opponentDice)) {
                    possibleCalls.push({
                        count: newCount,
                        dice: myBest.dice,
                        type: 'my_strength'
                    });
                }
            }

            // 评估每个叫点的安全性
            let bestCall = null;
            let bestScore = -1;
            let reason = '';

            for (const call of possibleCalls) {
                const myCount = call.dice === 1 ? myDice[1] : (myDice[call.dice] + myDice[1]);
                const needFromOpponent = call.count - myCount;

                // 安全性评分（我有的越多越安全）
                let safety = myCount * 20 - needFromOpponent * 10;

                // 如果是我的强势点数，加分
                if (call.type === 'my_strength') safety += 15;

                // 如果叫的数量不太激进，加分
                if (call.count <= opponentCount + 2) safety += 10;

                if (safety > bestScore) {
                    bestScore = safety;
                    bestCall = call;

                    if (myCount >= call.count) {
                        reason = `你已经有${myCount}个${call.dice}点，这个叫点很安全`;
                    } else if (needFromOpponent <= 2) {
                        reason = `你有${myCount}个，只需对手有${needFromOpponent}个，比较安全`;
                    } else {
                        reason = `你有${myCount}个，需要对手有${needFromOpponent}个，有一定风险`;
                    }
                }
            }

            // 生成备选方案
            const alternatives = possibleCalls
                .filter(call => call !== bestCall)
                .slice(0, 2)
                .map(call => `${call.count}个${call.dice}点`);

            return {
                best: bestCall ? `${bestCall.count}个${bestCall.dice}点` : '建议开牌',
                reason: reason || '没有找到安全的叫点',
                alternatives: alternatives
            };
        }

        function displayStrategy(strategy) {
            const resultSection = document.getElementById('resultSection');
            const strategyResult = document.getElementById('strategyResult');

            let html = '';

            strategy.strategies.forEach((s, index) => {
                html += `
                    <div class="strategy-card" style="${index === 0 ? 'border: 2px solid #FFD700;' : ''}">
                        <div class="strategy-title">
                            <span>${s.icon}</span>
                            ${s.type} ${index === 0 ? '(推荐)' : ''}
                        </div>
                        <p style="font-size: 1.1em; margin-bottom: 10px;">${s.description}</p>
                        <div class="probability">${s.probability}</div>
                        <p style="margin-top: 10px; opacity: 0.8; font-size: 0.9em;">${s.reason}</p>
                        ${s.alternatives ? `<p style="margin-top: 8px; font-size: 0.85em; color: #FFD700;">备选: ${s.alternatives.join(', ')}</p>` : ''}
                    </div>
                `;
            });

            // 添加详细分析
            html += `
                <div class="strategy-card" style="background: rgba(0,0,0,0.2);">
                    <div class="strategy-title">
                        <span>📊</span>
                        数据分析
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 10px;">
                        <div>
                            <p><strong>你的情况:</strong></p>
                            <p>• 拥有目标点数: ${strategy.analysis.myTargetCount}个</p>
                            <p>• 总骰子数: ${strategy.analysis.totalDice}个</p>
                        </div>
                        <div>
                            <p><strong>对手需要:</strong></p>
                            <p>• 需要点数: ${strategy.analysis.opponentNeedCount}个</p>
                            <p>• 叫点成立概率: ${strategy.analysis.callSuccessProbability.toFixed(0)}%</p>
                            <p>• 开牌成功率: ${strategy.analysis.openSuccessProbability.toFixed(0)}%</p>
                            <p>• 风险等级: ${strategy.analysis.riskLevel === 'high' ? '🔴 高风险' : (strategy.analysis.riskLevel === 'medium' ? '🟡 中等风险' : '🟢 低风险')}</p>
                        </div>
                    </div>
                </div>
            `;

            strategyResult.innerHTML = html;
            resultSection.style.display = 'block';

            // 滚动到结果区域
            resultSection.scrollIntoView({ behavior: 'smooth' });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加一些示例数据
            document.getElementById('dice1').value = '2';
            document.getElementById('dice3').value = '1';
            document.getElementById('dice6').value = '2';
        });
    </script>
</body>
</html>
