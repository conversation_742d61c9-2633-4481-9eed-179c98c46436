// 🚀 优化版俄罗斯方块拼图算法实现

class OptimizedTetrisSolver {
    constructor() {
        this.cache = new Map(); // 记忆化缓存
        this.profiler = new PerformanceProfiler();
        this.bestSolution = null;
        this.bestScore = -Infinity;
    }

    // 启发式函数：评估状态质量
    heuristic(board, remainingPieces, originalBoard) {
        let score = 0;
        let requiredCovered = 0;
        let totalRequired = 0;
        let forbiddenViolated = 0;
        let placementPossibilities = 0;

        for (let i = 0; i < board.length; i++) {
            for (let j = 0; j < board[i].length; j++) {
                // 统计必需位置覆盖情况
                if (originalBoard[i][j] === 1) {
                    totalRequired++;
                    if (board[i][j] > 0) requiredCovered++;
                }
                
                // 检查禁止位置违规
                if (originalBoard[i][j] === 2 && board[i][j] > 0) {
                    forbiddenViolated++;
                }
                
                // 计算空位的放置可能性
                if (board[i][j] === 0) {
                    placementPossibilities += this.countPossiblePlacements(board, i, j, remainingPieces);
                }
            }
        }

        // 计算启发式评分
        score += requiredCovered * 100; // 覆盖必需位置得高分
        score -= forbiddenViolated * 1000; // 违规严重扣分
        score += placementPossibilities * 5; // 保持灵活性
        score += this.calculateCompactness(board) * 10; // 紧凑性奖励

        return score;
    }

    // 计算紧凑性（相邻方块奖励）
    calculateCompactness(board) {
        let compactness = 0;
        const directions = [[0,1], [1,0], [0,-1], [-1,0]];

        for (let i = 0; i < board.length; i++) {
            for (let j = 0; j < board[i].length; j++) {
                if (board[i][j] > 0) {
                    for (const [di, dj] of directions) {
                        const ni = i + di;
                        const nj = j + dj;
                        if (ni >= 0 && ni < board.length && 
                            nj >= 0 && nj < board[i].length && 
                            board[ni][nj] > 0) {
                            compactness++;
                        }
                    }
                }
            }
        }

        return compactness;
    }

    // 计算位置的可放置方块数
    countPossiblePlacements(board, row, col, remainingPieces) {
        let count = 0;

        for (const [pieceName, pieceCount] of Object.entries(remainingPieces)) {
            if (pieceCount > 0) {
                const shapes = this.tetrisShapes[pieceName];
                for (const shape of shapes) {
                    if (this.canPlacePiece(board, shape, row, col)) {
                        count++;
                    }
                }
            }
        }

        return count;
    }

    // 智能排序：按约束度排序位置
    getOrderedPositions(board, remainingPieces, originalBoard) {
        const positions = [];

        for (let i = 0; i < board.length; i++) {
            for (let j = 0; j < board[i].length; j++) {
                if (board[i][j] === 0) {
                    const constraints = this.calculateConstraints(board, i, j, remainingPieces, originalBoard);
                    const priority = this.calculatePositionPriority(i, j, originalBoard);
                    positions.push({
                        row: i, 
                        col: j, 
                        constraints, 
                        priority,
                        score: constraints * 10 + priority
                    });
                }
            }
        }

        // 按综合评分降序排序（最重要的位置优先）
        return positions.sort((a, b) => b.score - a.score);
    }

    // 计算位置约束度
    calculateConstraints(board, row, col, remainingPieces, originalBoard) {
        let constraints = 0;

        // 必需位置有更高约束
        if (originalBoard[row][col] === 1) {
            constraints += 100;
        }

        // 计算周围已占用格子数
        const directions = [[0,1], [1,0], [0,-1], [-1,0]];
        for (const [di, dj] of directions) {
            const ni = row + di;
            const nj = col + dj;
            if (ni >= 0 && ni < board.length && 
                nj >= 0 && nj < board[0].length && 
                board[ni][nj] > 0) {
                constraints += 10;
            }
        }

        // 边角位置约束更高
        if (row === 0 || row === board.length - 1) constraints += 5;
        if (col === 0 || col === board[0].length - 1) constraints += 5;

        return constraints;
    }

    // 计算位置优先级
    calculatePositionPriority(row, col, originalBoard) {
        let priority = 0;

        // 必需位置优先级最高
        if (originalBoard[row][col] === 1) {
            priority += 50;
        }

        // 中心位置优先级较高
        const centerRow = Math.floor(originalBoard.length / 2);
        const centerCol = Math.floor(originalBoard[0].length / 2);
        const distance = Math.abs(row - centerRow) + Math.abs(col - centerCol);
        priority += Math.max(0, 20 - distance);

        return priority;
    }

    // 按价值排序方块
    getOrderedPieces(remainingPieces, board, originalBoard) {
        const pieces = [];

        for (const [name, count] of Object.entries(remainingPieces)) {
            if (count > 0) {
                const value = this.calculatePieceValue(name, board, originalBoard);
                pieces.push({name, value, count});
            }
        }

        return pieces.sort((a, b) => b.value - a.value);
    }

    // 计算方块价值
    calculatePieceValue(pieceName, board, originalBoard) {
        let value = 0;
        const shapes = this.tetrisShapes[pieceName];

        // 计算该方块能覆盖多少必需位置
        let maxRequiredCoverage = 0;
        for (const shape of shapes) {
            for (let i = 0; i < board.length; i++) {
                for (let j = 0; j < board[i].length; j++) {
                    if (this.canPlacePiece(board, shape, i, j)) {
                        let requiredCoverage = 0;
                        for (const [dr, dc] of shape) {
                            const r = i + dr;
                            const c = j + dc;
                            if (originalBoard[r] && originalBoard[r][c] === 1) {
                                requiredCoverage++;
                            }
                        }
                        maxRequiredCoverage = Math.max(maxRequiredCoverage, requiredCoverage);
                    }
                }
            }
        }

        value += maxRequiredCoverage * 20;

        // 方块大小价值
        value += shapes[0].length * 5;

        // 灵活性价值（旋转数量）
        value += shapes.length * 2;

        return value;
    }

    // 约束传播
    propagateConstraints(board, remainingPieces, originalBoard) {
        let changed = true;
        let iterations = 0;
        const maxIterations = 10; // 防止无限循环

        while (changed && iterations < maxIterations) {
            changed = false;
            iterations++;

            // 检查每个必需位置
            for (let i = 0; i < board.length; i++) {
                for (let j = 0; j < board[i].length; j++) {
                    if (originalBoard[i][j] === 1 && board[i][j] === 0) {
                        const possiblePlacements = this.findPossiblePlacements(board, i, j, remainingPieces);

                        if (possiblePlacements.length === 0) {
                            return false; // 无解
                        }

                        if (possiblePlacements.length === 1) {
                            // 强制放置
                            const placement = possiblePlacements[0];
                            this.placePiece(board, placement.shape, placement.row, placement.col, placement.pieceId);
                            remainingPieces[placement.pieceName]--;
                            changed = true;
                            this.profiler.metrics.pruningCount++;
                        }
                    }
                }
            }
        }

        return true;
    }

    // 查找能覆盖指定位置的所有可能放置
    findPossiblePlacements(board, targetRow, targetCol, remainingPieces) {
        const placements = [];

        for (const [pieceName, count] of Object.entries(remainingPieces)) {
            if (count > 0) {
                const shapes = this.tetrisShapes[pieceName];
                const pieceId = Object.keys(this.tetrisShapes).indexOf(pieceName) + 3;

                for (let shapeIdx = 0; shapeIdx < shapes.length; shapeIdx++) {
                    const shape = shapes[shapeIdx];

                    // 尝试所有可能的起始位置
                    for (let startRow = 0; startRow < board.length; startRow++) {
                        for (let startCol = 0; startCol < board[0].length; startCol++) {
                            if (this.canPlacePiece(board, shape, startRow, startCol)) {
                                // 检查是否覆盖目标位置
                                let coversTarget = false;
                                for (const [dr, dc] of shape) {
                                    if (startRow + dr === targetRow && startCol + dc === targetCol) {
                                        coversTarget = true;
                                        break;
                                    }
                                }

                                if (coversTarget) {
                                    placements.push({
                                        pieceName,
                                        shape,
                                        row: startRow,
                                        col: startCol,
                                        pieceId
                                    });
                                }
                            }
                        }
                    }
                }
            }
        }

        return placements;
    }

    // 生成状态哈希
    getStateHash(board, remainingPieces) {
        let hash = '';
        for (let i = 0; i < board.length; i++) {
            hash += board[i].join('');
        }
        
        // 添加剩余方块信息
        const sortedPieces = Object.keys(remainingPieces).sort();
        for (const piece of sortedPieces) {
            hash += piece + remainingPieces[piece];
        }
        
        return hash;
    }

    // 分支限界：估算上界
    getBound(board, remainingPieces, originalBoard) {
        let bound = 0;
        let coveredRequired = 0;
        let totalRequired = 0;

        // 计算当前覆盖情况
        for (let i = 0; i < board.length; i++) {
            for (let j = 0; j < board[i].length; j++) {
                if (originalBoard[i][j] === 1) {
                    totalRequired++;
                    if (board[i][j] > 0) coveredRequired++;
                }
            }
        }

        // 估算剩余方块的最大覆盖能力
        const remainingRequired = totalRequired - coveredRequired;
        const maxCoverable = this.estimateMaxCoverage(remainingPieces, remainingRequired);

        bound = coveredRequired + Math.min(maxCoverable, remainingRequired);
        return bound;
    }

    // 估算最大覆盖能力
    estimateMaxCoverage(remainingPieces, targetCoverage) {
        let maxCoverage = 0;

        for (const [pieceName, count] of Object.entries(remainingPieces)) {
            if (count > 0) {
                const shapes = this.tetrisShapes[pieceName];
                const maxShapeSize = Math.max(...shapes.map(shape => shape.length));
                maxCoverage += count * maxShapeSize;
            }
        }

        return Math.min(maxCoverage, targetCoverage);
    }

    // 优化版回溯算法
    async solveOptimized(board, remainingPieces, originalBoard, depth = 0) {
        this.profiler.metrics.totalSteps++;

        // 检查缓存
        const stateHash = this.getStateHash(board, remainingPieces);
        if (this.cache.has(stateHash)) {
            this.profiler.metrics.cacheHits++;
            return this.cache.get(stateHash);
        }
        this.profiler.metrics.cacheMisses++;

        // 约束传播
        const boardCopy = board.map(row => [...row]);
        const piecesCopy = {...remainingPieces};
        
        if (!this.propagateConstraints(boardCopy, piecesCopy, originalBoard)) {
            this.cache.set(stateHash, false);
            return false;
        }

        // 检查是否完成
        const availablePieces = Object.fromEntries(
            Object.entries(piecesCopy).filter(([name, count]) => count > 0)
        );

        if (Object.keys(availablePieces).length === 0) {
            const isValid = this.isValidSolution(boardCopy, originalBoard);
            this.cache.set(stateHash, isValid);
            
            if (isValid) {
                const score = this.heuristic(boardCopy, {}, originalBoard);
                if (score > this.bestScore) {
                    this.bestScore = score;
                    this.bestSolution = boardCopy.map(row => [...row]);
                }
            }
            
            return isValid;
        }

        // 分支限界剪枝
        const bound = this.getBound(boardCopy, piecesCopy, originalBoard);
        if (bound < this.bestScore * 0.8) { // 如果上界太低，剪枝
            this.profiler.metrics.pruningCount++;
            this.cache.set(stateHash, false);
            return false;
        }

        // 智能排序
        const orderedPieces = this.getOrderedPieces(piecesCopy, boardCopy, originalBoard);
        const orderedPositions = this.getOrderedPositions(boardCopy, piecesCopy, originalBoard);

        // 尝试放置方块
        for (const pieceInfo of orderedPieces) {
            const pieceName = pieceInfo.name;
            const shapes = this.tetrisShapes[pieceName];
            const pieceId = Object.keys(this.tetrisShapes).indexOf(pieceName) + 3;

            for (const position of orderedPositions.slice(0, 10)) { // 只尝试前10个最有希望的位置
                const {row, col} = position;

                for (let shapeIdx = 0; shapeIdx < shapes.length; shapeIdx++) {
                    const shape = shapes[shapeIdx];

                    if (this.canPlacePiece(boardCopy, shape, row, col)) {
                        const [validPlacement] = this.isValidPlacement(boardCopy, shape, row, col, originalBoard);

                        if (validPlacement) {
                            // 放置方块
                            this.placePiece(boardCopy, shape, row, col, pieceId);
                            piecesCopy[pieceName]--;

                            // 递归求解
                            if (await this.solveOptimized(boardCopy, piecesCopy, originalBoard, depth + 1)) {
                                this.cache.set(stateHash, true);
                                return true;
                            }

                            // 回溯
                            this.removePiece(boardCopy, shape, row, col);
                            piecesCopy[pieceName]++;
                            this.profiler.metrics.backtrackCount++;
                        }
                    }
                }
            }
        }

        this.cache.set(stateHash, false);
        return false;
    }

    // 辅助方法（需要从原始类中复制）
    canPlacePiece(board, shape, startRow, startCol) {
        for (const [dr, dc] of shape) {
            const r = startRow + dr;
            const c = startCol + dc;
            if (r < 0 || r >= board.length || 
                c < 0 || c >= board[0].length || 
                board[r][c] !== 0) {
                return false;
            }
        }
        return true;
    }

    placePiece(board, shape, startRow, startCol, pieceId) {
        for (const [dr, dc] of shape) {
            const r = startRow + dr;
            const c = startCol + dc;
            board[r][c] = pieceId;
        }
    }

    removePiece(board, shape, startRow, startCol) {
        for (const [dr, dc] of shape) {
            const r = startRow + dr;
            const c = startCol + dc;
            board[r][c] = 0;
        }
    }

    isValidPlacement(board, shape, startRow, startCol, originalBoard) {
        let coversRequired = 0;
        let coversForbidden = false;

        for (const [dr, dc] of shape) {
            const r = startRow + dr;
            const c = startCol + dc;
            if (originalBoard[r][c] === 2) {
                coversForbidden = true;
                break;
            }
            if (originalBoard[r][c] === 1) {
                coversRequired++;
            }
        }
        return [!coversForbidden, coversRequired];
    }

    isValidSolution(board, originalBoard) {
        for (let i = 0; i < board.length; i++) {
            for (let j = 0; j < board[i].length; j++) {
                if (originalBoard[i][j] === 1 && board[i][j] === 0) {
                    return false;
                }
                if (originalBoard[i][j] === 2 && board[i][j] !== 0) {
                    return false;
                }
            }
        }
        return true;
    }
}

// 性能分析器
class PerformanceProfiler {
    constructor() {
        this.metrics = {
            totalSteps: 0,
            cacheHits: 0,
            cacheMisses: 0,
            pruningCount: 0,
            backtrackCount: 0,
            startTime: 0,
            endTime: 0
        };
    }

    start() {
        this.metrics.startTime = performance.now();
        // 重置计数器
        Object.keys(this.metrics).forEach(key => {
            if (key !== 'startTime') this.metrics[key] = 0;
        });
    }

    end() {
        this.metrics.endTime = performance.now();
    }

    getReport() {
        const duration = this.metrics.endTime - this.metrics.startTime;
        const cacheHitRate = this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0;

        return {
            duration: duration.toFixed(2) + 'ms',
            stepsPerSecond: Math.round(this.metrics.totalSteps / (duration / 1000)) || 0,
            cacheHitRate: (cacheHitRate * 100).toFixed(1) + '%',
            pruningEfficiency: (this.metrics.pruningCount / Math.max(this.metrics.totalSteps, 1) * 100).toFixed(1) + '%',
            backtrackCount: this.metrics.backtrackCount,
            totalSteps: this.metrics.totalSteps
        };
    }
}
