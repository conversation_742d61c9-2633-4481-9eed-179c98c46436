#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sign签名分析工具 - 分析getSmsCodeRandom等请求中的sign生成算法
"""

import hashlib
import hmac
import time
import json
import base64
import urllib.parse
import random
import string
from datetime import datetime

class SignAnalyzer:
    def __init__(self):
        self.common_keys = [
            'secret', 'key', 'appkey', 'app_key', 'api_key', 'token',
            'salt', 'nonce', 'timestamp', 'random', 'version'
        ]
        
    def analyze_sign_patterns(self, request_data):
        """分析可能的签名模式"""
        print("🔍 分析Sign签名生成模式...")
        print("=" * 60)
        
        # 常见的签名算法模式
        patterns = [
            self.pattern_md5_timestamp,
            self.pattern_md5_params_sorted,
            self.pattern_hmac_sha1,
            self.pattern_hmac_sha256,
            self.pattern_base64_encode,
            self.pattern_custom_hash
        ]
        
        for pattern in patterns:
            try:
                result = pattern(request_data)
                if result:
                    print(f"✅ 可能匹配的模式: {pattern.__name__}")
                    print(f"   生成的签名: {result}")
                    print("-" * 40)
            except Exception as e:
                print(f"❌ {pattern.__name__} 分析失败: {str(e)}")
    
    def pattern_md5_timestamp(self, data):
        """模式1: MD5(参数+时间戳+密钥)"""
        timestamp = str(int(time.time()))
        phone = data.get('phone', '13800138000')
        
        # 常见的密钥组合
        possible_keys = [
            'secret123', 'app_secret', 'api_secret', 'key123',
            '1234567890', 'abcdef123456', 'mobile_secret'
        ]
        
        results = []
        for key in possible_keys:
            # 不同的拼接方式
            combinations = [
                f"{phone}{timestamp}{key}",
                f"{key}{phone}{timestamp}",
                f"phone={phone}&timestamp={timestamp}&key={key}",
                f"getSmsCodeRandom{phone}{timestamp}{key}"
            ]
            
            for combo in combinations:
                sign = hashlib.md5(combo.encode()).hexdigest()
                results.append({
                    'pattern': f'MD5({combo})',
                    'sign': sign,
                    'timestamp': timestamp
                })
        
        return results[0] if results else None
    
    def pattern_md5_params_sorted(self, data):
        """模式2: MD5(参数按字母序排列+密钥)"""
        phone = data.get('phone', '13800138000')
        timestamp = str(int(time.time()))
        nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        
        # 构造参数字典
        params = {
            'phone': phone,
            'timestamp': timestamp,
            'nonce': nonce,
            'method': 'getSmsCodeRandom'
        }
        
        # 按key排序
        sorted_params = sorted(params.items())
        param_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
        
        # 添加密钥
        secret = 'your_secret_key'
        sign_string = param_string + '&key=' + secret
        sign = hashlib.md5(sign_string.encode()).hexdigest()
        
        return {
            'pattern': f'MD5(sorted_params + key)',
            'sign': sign,
            'params': params,
            'sign_string': sign_string
        }
    
    def pattern_hmac_sha1(self, data):
        """模式3: HMAC-SHA1签名"""
        phone = data.get('phone', '13800138000')
        timestamp = str(int(time.time()))
        
        # 构造签名字符串
        sign_string = f"phone={phone}&timestamp={timestamp}&method=getSmsCodeRandom"
        secret_key = 'your_hmac_secret'
        
        sign = hmac.new(
            secret_key.encode(),
            sign_string.encode(),
            hashlib.sha1
        ).hexdigest()
        
        return {
            'pattern': 'HMAC-SHA1',
            'sign': sign,
            'sign_string': sign_string,
            'secret_key': secret_key
        }
    
    def pattern_hmac_sha256(self, data):
        """模式4: HMAC-SHA256签名"""
        phone = data.get('phone', '13800138000')
        timestamp = str(int(time.time()))
        
        sign_string = f"getSmsCodeRandom{phone}{timestamp}"
        secret_key = 'your_secret_key_256'
        
        sign = hmac.new(
            secret_key.encode(),
            sign_string.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return {
            'pattern': 'HMAC-SHA256',
            'sign': sign,
            'sign_string': sign_string
        }
    
    def pattern_base64_encode(self, data):
        """模式5: Base64编码签名"""
        phone = data.get('phone', '13800138000')
        timestamp = str(int(time.time()))
        
        # 先MD5再Base64
        md5_string = f"{phone}{timestamp}secret"
        md5_hash = hashlib.md5(md5_string.encode()).hexdigest()
        sign = base64.b64encode(md5_hash.encode()).decode()
        
        return {
            'pattern': 'Base64(MD5(phone+timestamp+secret))',
            'sign': sign,
            'md5_string': md5_string
        }
    
    def pattern_custom_hash(self, data):
        """模式6: 自定义哈希算法"""
        phone = data.get('phone', '13800138000')
        timestamp = str(int(time.time()))
        
        # 模拟一些自定义算法
        custom_string = f"api=getSmsCodeRandom&phone={phone}&ts={timestamp}"
        
        # 自定义哈希：多次MD5
        hash1 = hashlib.md5(custom_string.encode()).hexdigest()
        hash2 = hashlib.md5((hash1 + 'salt').encode()).hexdigest()
        sign = hash2[:16]  # 取前16位
        
        return {
            'pattern': 'Custom Hash (Double MD5 + Salt)',
            'sign': sign,
            'custom_string': custom_string
        }

def analyze_request_example():
    """分析示例请求"""
    print("📋 分析getSmsCodeRandom请求示例")
    print("=" * 60)
    
    # 示例请求数据
    example_request = {
        'url': 'https://api.example.com/getSmsCodeRandom',
        'method': 'POST',
        'phone': '13800138000',
        'timestamp': str(int(time.time())),
        'nonce': ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    }
    
    print("示例请求信息:")
    for key, value in example_request.items():
        print(f"  {key}: {value}")
    
    print("\n🔧 可能的签名生成方式:")
    
    analyzer = SignAnalyzer()
    analyzer.analyze_sign_patterns(example_request)

def reverse_engineer_sign():
    """逆向工程分析已知的sign"""
    print("\n🔍 逆向分析已知Sign值")
    print("=" * 60)
    
    # 如果你有已知的请求和对应的sign，可以在这里分析
    known_cases = [
        {
            'phone': '13800138000',
            'timestamp': '1640995200',
            'sign': 'a1b2c3d4e5f6g7h8',  # 替换为实际的sign值
            'description': '示例case 1'
        },
        {
            'phone': '13900139000', 
            'timestamp': '1640995260',
            'sign': 'h8g7f6e5d4c3b2a1',  # 替换为实际的sign值
            'description': '示例case 2'
        }
    ]
    
    print("已知案例分析:")
    for i, case in enumerate(known_cases, 1):
        print(f"\n案例 {i}: {case['description']}")
        print(f"  手机号: {case['phone']}")
        print(f"  时间戳: {case['timestamp']}")
        print(f"  签名值: {case['sign']}")
        
        # 尝试各种组合来匹配这个签名
        phone = case['phone']
        timestamp = case['timestamp']
        target_sign = case['sign']
        
        # 测试不同的算法
        test_combinations = [
            f"{phone}{timestamp}",
            f"{timestamp}{phone}",
            f"phone={phone}&timestamp={timestamp}",
            f"getSmsCodeRandom{phone}{timestamp}",
            f"{phone}_{timestamp}_secret",
        ]
        
        print("  尝试匹配的组合:")
        for combo in test_combinations:
            md5_result = hashlib.md5(combo.encode()).hexdigest()
            sha1_result = hashlib.sha1(combo.encode()).hexdigest()
            
            if md5_result == target_sign:
                print(f"    ✅ MD5匹配: {combo}")
            elif sha1_result == target_sign:
                print(f"    ✅ SHA1匹配: {combo}")
            elif md5_result[:len(target_sign)] == target_sign:
                print(f"    🔸 MD5前缀匹配: {combo}")
            elif sha1_result[:len(target_sign)] == target_sign:
                print(f"    🔸 SHA1前缀匹配: {combo}")

def generate_sign_templates():
    """生成常用的签名模板代码"""
    print("\n📝 常用签名生成代码模板")
    print("=" * 60)
    
    templates = {
        'MD5时间戳': '''
def generate_md5_timestamp_sign(phone, secret_key='your_secret'):
    timestamp = str(int(time.time()))
    sign_string = f"{phone}{timestamp}{secret_key}"
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    return sign, timestamp
''',
        
        'HMAC-SHA1': '''
def generate_hmac_sha1_sign(phone, secret_key='your_secret'):
    timestamp = str(int(time.time()))
    sign_string = f"phone={phone}&timestamp={timestamp}"
    sign = hmac.new(
        secret_key.encode(),
        sign_string.encode(),
        hashlib.sha1
    ).hexdigest()
    return sign, timestamp
''',
        
        '参数排序MD5': '''
def generate_sorted_params_sign(phone, secret_key='your_secret'):
    timestamp = str(int(time.time()))
    nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    
    params = {
        'phone': phone,
        'timestamp': timestamp,
        'nonce': nonce
    }
    
    # 按key排序并拼接
    sorted_params = sorted(params.items())
    param_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
    sign_string = param_string + '&key=' + secret_key
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    
    return sign, params
'''
    }
    
    for name, code in templates.items():
        print(f"\n{name}:")
        print(code)

if __name__ == "__main__":
    print("🔐 Sign签名分析工具")
    print("=" * 60)
    print("用于分析getSmsCodeRandom等API请求中的sign生成算法")
    print()
    
    # 分析示例请求
    analyze_request_example()
    
    # 逆向分析
    reverse_engineer_sign()
    
    # 生成模板代码
    generate_sign_templates()
    
    print("\n💡 使用建议:")
    print("1. 使用Fiddler/Charles抓包获取真实的请求参数和sign值")
    print("2. 对比多个请求找出sign的变化规律")
    print("3. 查看前端JS代码寻找签名生成逻辑")
    print("4. 尝试不同的哈希算法和参数组合")
    print("5. 注意时间戳、随机数、密钥等参数的影响")
    
    print("\n🔧 下一步操作:")
    print("- 提供具体的请求URL和参数")
    print("- 提供已知的sign值进行逆向分析")
    print("- 查看目标网站的前端JS代码")
    print("- 使用浏览器开发者工具分析网络请求")
