#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI拼图破解器 - 带界面的GPU加速拼图破解器
兼容网页端的导入导出功能，支持初始棋盘展示和前10名棋盘展示
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import threading
import time
from datetime import datetime
import os
from GPU拼图破解器完整版 import GPUTetrisPuzzleSolver

class PuzzleGUI:
    """拼图破解器GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🧩 GPU拼图破解器 - 专业版")
        self.root.geometry("1200x800")
        
        # 初始化求解器
        self.solver = GPUTetrisPuzzleSolver()
        
        # 棋盘数据
        self.board_size = 7
        self.board = None
        self.piece_counts = {
            "T": 1,
            "田": 1,
            "横杠竖条": 1,
            "Z": 1,
            "L": 1
        }
        
        # GUI组件
        self.board_buttons = []
        self.piece_vars = {}
        self.solutions = []
        self.leaderboard = []
        
        # 创建界面
        self.create_widgets()
        self.load_leaderboard()
        self.create_default_board()
        
    def create_widgets(self):
        """创建GUI组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 棋盘操作
        left_frame = ttk.LabelFrame(main_frame, text="🎮 棋盘操作", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 棋盘大小控制
        size_frame = ttk.Frame(left_frame)
        size_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(size_frame, text="棋盘大小:").pack(side=tk.LEFT)
        self.size_var = tk.IntVar(value=7)
        size_spin = ttk.Spinbox(size_frame, from_=3, to=10, width=5, 
                               textvariable=self.size_var, command=self.resize_board)
        size_spin.pack(side=tk.LEFT, padx=(5, 10))
        
        ttk.Button(size_frame, text="🔄 重置棋盘", command=self.reset_board).pack(side=tk.LEFT)
        
        # 棋盘显示区域
        self.board_frame = ttk.LabelFrame(left_frame, text="📋 棋盘编辑", padding=5)
        self.board_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 操作说明
        help_frame = ttk.Frame(left_frame)
        help_frame.pack(fill=tk.X, pady=(0, 10))
        
        help_text = "🖱️ 左键: 必需位置(✓)  右键: 禁止位置(✗)  中键: 清空"
        ttk.Label(help_frame, text=help_text, font=("Arial", 9)).pack()
        
        # 导入导出按钮
        io_frame = ttk.Frame(left_frame)
        io_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(io_frame, text="📁 导入棋盘", command=self.import_board).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(io_frame, text="💾 导出棋盘", command=self.export_board).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(io_frame, text="🎲 随机棋盘", command=self.generate_random_board).pack(side=tk.LEFT)
        
        # 右侧面板 - 控制和结果
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 方块设置
        piece_frame = ttk.LabelFrame(right_frame, text="🧩 方块设置", padding=10)
        piece_frame.pack(fill=tk.X, pady=(0, 10))
        
        for i, (piece, count) in enumerate(self.piece_counts.items()):
            row = i // 3
            col = i % 3
            
            piece_subframe = ttk.Frame(piece_frame)
            piece_subframe.grid(row=row, column=col, padx=5, pady=2, sticky="w")
            
            ttk.Label(piece_subframe, text=f"{piece}:").pack(side=tk.LEFT)
            var = tk.IntVar(value=count)
            self.piece_vars[piece] = var
            ttk.Spinbox(piece_subframe, from_=0, to=5, width=5, textvariable=var).pack(side=tk.LEFT, padx=(5, 0))
        
        # 求解控制
        solve_frame = ttk.LabelFrame(right_frame, text="🚀 GPU求解", padding=10)
        solve_frame.pack(fill=tk.X, pady=(0, 10))
        
        solve_btn_frame = ttk.Frame(solve_frame)
        solve_btn_frame.pack(fill=tk.X, pady=(0, 5))
        
        self.solve_btn = ttk.Button(solve_btn_frame, text="🚀 GPU加速求解", 
                                   command=self.start_solve, style="Accent.TButton")
        self.solve_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Label(solve_btn_frame, text="最大解数:").pack(side=tk.LEFT)
        self.max_solutions_var = tk.IntVar(value=10)
        ttk.Spinbox(solve_btn_frame, from_=1, to=50, width=5, 
                   textvariable=self.max_solutions_var).pack(side=tk.LEFT, padx=(5, 0))
        
        # 进度条
        self.progress = ttk.Progressbar(solve_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(5, 0))
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(solve_frame, textvariable=self.status_var).pack(pady=(5, 0))
        
        # 结果显示
        result_frame = ttk.LabelFrame(right_frame, text="📊 求解结果", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 结果列表
        self.result_tree = ttk.Treeview(result_frame, columns=("time", "steps", "gpu"), show="tree headings", height=8)
        self.result_tree.heading("#0", text="解决方案")
        self.result_tree.heading("time", text="时间(s)")
        self.result_tree.heading("steps", text="步数")
        self.result_tree.heading("gpu", text="GPU")
        
        self.result_tree.column("#0", width=100)
        self.result_tree.column("time", width=80)
        self.result_tree.column("steps", width=60)
        self.result_tree.column("gpu", width=60)
        
        result_scroll = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=result_scroll.set)
        
        self.result_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        result_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.result_tree.bind("<Double-1>", self.show_solution_detail)
        
        # 结果操作按钮
        result_btn_frame = ttk.Frame(result_frame)
        result_btn_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(result_btn_frame, text="👁️ 查看详情", command=self.show_solution_detail).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(result_btn_frame, text="💾 保存结果", command=self.save_results).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(result_btn_frame, text="🏆 添加到排行榜", command=self.add_to_leaderboard).pack(side=tk.LEFT)
        
        # 排行榜
        leaderboard_frame = ttk.LabelFrame(right_frame, text="🏆 前10名排行榜", padding=10)
        leaderboard_frame.pack(fill=tk.X)
        
        self.leaderboard_tree = ttk.Treeview(leaderboard_frame, columns=("rank", "time", "size", "date"), show="headings", height=6)
        self.leaderboard_tree.heading("rank", text="排名")
        self.leaderboard_tree.heading("time", text="时间(s)")
        self.leaderboard_tree.heading("size", text="棋盘")
        self.leaderboard_tree.heading("date", text="日期")
        
        self.leaderboard_tree.column("rank", width=50)
        self.leaderboard_tree.column("time", width=80)
        self.leaderboard_tree.column("size", width=60)
        self.leaderboard_tree.column("date", width=100)
        
        leaderboard_scroll = ttk.Scrollbar(leaderboard_frame, orient=tk.VERTICAL, command=self.leaderboard_tree.yview)
        self.leaderboard_tree.configure(yscrollcommand=leaderboard_scroll.set)
        
        self.leaderboard_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        leaderboard_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.leaderboard_tree.bind("<Double-1>", self.load_leaderboard_board)
        
        # 排行榜操作按钮
        leaderboard_btn_frame = ttk.Frame(leaderboard_frame)
        leaderboard_btn_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(leaderboard_btn_frame, text="📋 加载棋盘", command=self.load_leaderboard_board).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(leaderboard_btn_frame, text="🗑️ 清空排行榜", command=self.clear_leaderboard).pack(side=tk.LEFT)
    
    def create_board_display(self):
        """创建棋盘显示"""
        # 清除现有棋盘
        for widget in self.board_frame.winfo_children():
            widget.destroy()
        
        self.board_buttons = []
        
        # 创建棋盘按钮
        for r in range(self.board_size):
            row_buttons = []
            for c in range(self.board_size):
                btn = tk.Button(self.board_frame, width=3, height=1, 
                               font=("Arial", 12, "bold"),
                               command=lambda r=r, c=c: self.toggle_cell(r, c))
                
                # 绑定鼠标事件
                btn.bind("<Button-1>", lambda e, r=r, c=c: self.set_cell(r, c, 1))  # 左键 - 必需
                btn.bind("<Button-3>", lambda e, r=r, c=c: self.set_cell(r, c, 2))  # 右键 - 禁止
                btn.bind("<Button-2>", lambda e, r=r, c=c: self.set_cell(r, c, 0))  # 中键 - 清空
                
                btn.grid(row=r, column=c, padx=1, pady=1)
                row_buttons.append(btn)
            
            self.board_buttons.append(row_buttons)
        
        self.update_board_display()
    
    def update_board_display(self):
        """更新棋盘显示"""
        if not self.board or not self.board_buttons:
            return
        
        for r in range(self.board_size):
            for c in range(self.board_size):
                btn = self.board_buttons[r][c]
                cell_value = self.board[r][c]
                
                if cell_value == 1:
                    # 必需位置 - 红色✓
                    btn.config(bg="red", fg="white", text="✓")
                elif cell_value == 2:
                    # 禁止位置 - 灰色✗
                    btn.config(bg="gray", fg="white", text="✗")
                elif cell_value > 2:
                    # 方块 - 根据方块类型着色
                    piece_names = list(self.piece_counts.keys())
                    piece_idx = cell_value - 3
                    if piece_idx < len(piece_names):
                        piece_name = piece_names[piece_idx]
                        colors = {"T": "purple", "田": "orange", "横杠竖条": "cyan", "Z": "red", "L": "green"}
                        color = colors.get(piece_name, "blue")
                        btn.config(bg=color, fg="white", text=piece_name)
                    else:
                        btn.config(bg="blue", fg="white", text=str(cell_value))
                else:
                    # 空位置
                    btn.config(bg="lightgray", fg="black", text="")
    
    def set_cell(self, r, c, value):
        """设置单元格值"""
        if self.board:
            self.board[r][c] = value
            self.update_board_display()
    
    def toggle_cell(self, r, c):
        """切换单元格状态"""
        if self.board:
            current = self.board[r][c]
            if current == 0:
                self.board[r][c] = 1  # 空 -> 必需
            elif current == 1:
                self.board[r][c] = 2  # 必需 -> 禁止
            else:
                self.board[r][c] = 0  # 禁止 -> 空
            self.update_board_display()
    
    def resize_board(self):
        """调整棋盘大小"""
        new_size = self.size_var.get()
        if new_size != self.board_size:
            self.board_size = new_size
            self.create_default_board()
    
    def create_default_board(self):
        """创建默认棋盘"""
        self.board = [[0 for _ in range(self.board_size)] for _ in range(self.board_size)]
        self.create_board_display()
    
    def reset_board(self):
        """重置棋盘"""
        self.create_default_board()
    
    def generate_random_board(self):
        """生成随机棋盘"""
        import random
        
        self.board = [[0 for _ in range(self.board_size)] for _ in range(self.board_size)]
        
        # 随机添加必需位置
        num_required = random.randint(2, min(6, self.board_size))
        for _ in range(num_required):
            r = random.randint(0, self.board_size - 1)
            c = random.randint(0, self.board_size - 1)
            self.board[r][c] = 1
        
        # 随机添加禁止位置
        num_forbidden = random.randint(1, 3)
        for _ in range(num_forbidden):
            r = random.randint(0, self.board_size - 1)
            c = random.randint(0, self.board_size - 1)
            if self.board[r][c] == 0:  # 不覆盖必需位置
                self.board[r][c] = 2
        
        self.update_board_display()
        messagebox.showinfo("随机棋盘", f"已生成 {self.board_size}x{self.board_size} 随机棋盘")
    
    def import_board(self):
        """导入棋盘（兼容网页端格式）"""
        file_path = filedialog.askopenfilename(
            title="导入棋盘",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 兼容网页端格式
                if 'board' in data:
                    self.board = data['board']
                    self.board_size = len(self.board)
                    self.size_var.set(self.board_size)
                    
                    if 'piece_counts' in data:
                        for piece, count in data['piece_counts'].items():
                            if piece in self.piece_vars:
                                self.piece_vars[piece].set(count)
                    
                    self.create_board_display()
                    messagebox.showinfo("导入成功", f"已导入 {self.board_size}x{self.board_size} 棋盘")
                else:
                    messagebox.showerror("导入失败", "文件格式不正确")
                    
            except Exception as e:
                messagebox.showerror("导入失败", f"读取文件失败: {e}")
    
    def export_board(self):
        """导出棋盘（兼容网页端格式）"""
        if not self.board:
            messagebox.showwarning("导出失败", "请先创建棋盘")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="导出棋盘",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 获取当前方块设置
                current_pieces = {piece: var.get() for piece, var in self.piece_vars.items()}
                
                # 兼容网页端格式
                data = {
                    "board_size": self.board_size,
                    "board": self.board,
                    "piece_counts": current_pieces,
                    "show_process": False,
                    "export_time": datetime.now().isoformat(),
                    "export_source": "GUI拼图破解器"
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("导出成功", f"棋盘已导出到: {file_path}")
                
            except Exception as e:
                messagebox.showerror("导出失败", f"保存文件失败: {e}")
    
    def start_solve(self):
        """开始求解（在后台线程中）"""
        if not self.board:
            messagebox.showwarning("求解失败", "请先创建棋盘")
            return
        
        # 获取当前方块设置
        current_pieces = {piece: var.get() for piece, var in self.piece_vars.items()}
        
        if sum(current_pieces.values()) == 0:
            messagebox.showwarning("求解失败", "请至少设置一个方块")
            return
        
        # 禁用求解按钮，显示进度
        self.solve_btn.config(state="disabled")
        self.progress.start()
        self.status_var.set("GPU求解中...")
        
        # 在后台线程中求解
        thread = threading.Thread(target=self.solve_puzzle, args=(current_pieces,))
        thread.daemon = True
        thread.start()
    
    def solve_puzzle(self, piece_counts):
        """求解拼图"""
        try:
            max_solutions = self.max_solutions_var.get()
            
            # GPU加速求解
            solutions = self.solver.gpu_accelerated_solve(
                self.board, piece_counts, 
                algorithm="GUI求解", 
                max_solutions=max_solutions
            )
            
            # 在主线程中更新UI
            self.root.after(0, self.solve_completed, solutions)
            
        except Exception as e:
            self.root.after(0, self.solve_error, str(e))
    
    def solve_completed(self, solutions):
        """求解完成"""
        self.solve_btn.config(state="normal")
        self.progress.stop()
        
        if solutions:
            self.solutions = solutions
            self.status_var.set(f"找到 {len(solutions)} 个解决方案")
            
            # 更新结果列表
            self.result_tree.delete(*self.result_tree.get_children())
            
            for i, solution in enumerate(solutions):
                gpu_status = "✅" if self.solver.gpu_available else "❌"
                self.result_tree.insert("", "end", 
                                      text=f"解决方案 {i+1}",
                                      values=(f"{self.solver.solve_time:.3f}", 
                                            len(solution.get('steps', [])),
                                            gpu_status))
            
            # 显示第一个解决方案
            if solutions:
                self.show_solution(0)
                
        else:
            self.status_var.set("未找到解决方案")
            messagebox.showinfo("求解结果", "未找到解决方案\n\n建议:\n1. 减少方块数量\n2. 调整必需位置\n3. 减少禁止位置")
    
    def solve_error(self, error_msg):
        """求解错误"""
        self.solve_btn.config(state="normal")
        self.progress.stop()
        self.status_var.set("求解失败")
        messagebox.showerror("求解错误", f"求解过程中出现错误:\n{error_msg}")
    
    def show_solution(self, index):
        """显示解决方案"""
        if 0 <= index < len(self.solutions):
            solution = self.solutions[index]
            if 'board' in solution:
                # 临时保存当前棋盘
                original_board = [row[:] for row in self.board]
                
                # 显示解决方案
                self.board = solution['board']
                self.update_board_display()
                
                # 3秒后恢复原棋盘
                self.root.after(3000, lambda: self.restore_board(original_board))
    
    def restore_board(self, original_board):
        """恢复原棋盘"""
        self.board = original_board
        self.update_board_display()
    
    def show_solution_detail(self, event=None):
        """显示解决方案详情"""
        selection = self.result_tree.selection()
        if not selection:
            return
        
        item = self.result_tree.item(selection[0])
        index = int(item['text'].split()[-1]) - 1
        
        if 0 <= index < len(self.solutions):
            solution = self.solutions[index]
            
            # 创建详情窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"解决方案 {index+1} 详情")
            detail_window.geometry("600x400")
            
            # 详情文本
            text_widget = scrolledtext.ScrolledText(detail_window, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            detail_text = f"解决方案 {index+1} 详情\n"
            detail_text += "=" * 50 + "\n\n"
            detail_text += f"求解时间: {self.solver.solve_time:.3f}秒\n"
            detail_text += f"GPU时间: {self.solver.gpu_time:.3f}秒\n"
            detail_text += f"GPU设备: {self.solver.device_info}\n"
            detail_text += f"棋盘大小: {self.board_size}x{self.board_size}\n\n"
            
            if 'steps' in solution:
                detail_text += f"解决步骤 ({len(solution['steps'])}步):\n"
                for i, step in enumerate(solution['steps'], 1):
                    detail_text += f"{i}. {step}\n"
            
            text_widget.insert(tk.END, detail_text)
            text_widget.config(state=tk.DISABLED)
            
            # 显示解决方案按钮
            btn_frame = ttk.Frame(detail_window)
            btn_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
            
            ttk.Button(btn_frame, text="👁️ 显示解决方案", 
                      command=lambda: self.show_solution(index)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="💾 保存图片", 
                      command=lambda: self.save_solution_image(index)).pack(side=tk.LEFT)
    
    def save_solution_image(self, index):
        """保存解决方案图片"""
        if 0 <= index < len(self.solutions):
            try:
                filename = f"solution_{index+1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                self.solver.visualize_solution(index, filename)
                messagebox.showinfo("保存成功", f"解决方案图片已保存: {filename}")
            except Exception as e:
                messagebox.showerror("保存失败", f"保存图片失败: {e}")
    
    def save_results(self):
        """保存求解结果"""
        if not self.solutions:
            messagebox.showwarning("保存失败", "没有可保存的结果")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="保存求解结果",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                current_pieces = {piece: var.get() for piece, var in self.piece_vars.items()}
                
                results = {
                    "solve_time": self.solver.solve_time,
                    "gpu_time": self.solver.gpu_time,
                    "gpu_available": self.solver.gpu_available,
                    "device_info": self.solver.device_info,
                    "board_size": self.board_size,
                    "board": self.board,
                    "piece_counts": current_pieces,
                    "solutions_count": len(self.solutions),
                    "solutions": self.solutions,
                    "save_time": datetime.now().isoformat()
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("保存成功", f"求解结果已保存: {file_path}")
                
            except Exception as e:
                messagebox.showerror("保存失败", f"保存文件失败: {e}")
    
    def add_to_leaderboard(self):
        """添加到排行榜"""
        if not self.solutions:
            messagebox.showwarning("添加失败", "没有可添加的结果")
            return
        
        # 添加到排行榜
        entry = {
            "time": self.solver.solve_time,
            "gpu_time": self.solver.gpu_time,
            "board_size": self.board_size,
            "solutions_count": len(self.solutions),
            "date": datetime.now().strftime("%Y-%m-%d %H:%M"),
            "board": self.board,
            "piece_counts": {piece: var.get() for piece, var in self.piece_vars.items()}
        }
        
        self.leaderboard.append(entry)
        
        # 按时间排序，保留前10名
        self.leaderboard.sort(key=lambda x: x["time"])
        self.leaderboard = self.leaderboard[:10]
        
        self.update_leaderboard_display()
        self.save_leaderboard()
        
        messagebox.showinfo("添加成功", f"已添加到排行榜 (第{len([x for x in self.leaderboard if x['time'] <= self.solver.solve_time])}名)")
    
    def update_leaderboard_display(self):
        """更新排行榜显示"""
        self.leaderboard_tree.delete(*self.leaderboard_tree.get_children())
        
        for i, entry in enumerate(self.leaderboard, 1):
            self.leaderboard_tree.insert("", "end", values=(
                f"#{i}",
                f"{entry['time']:.3f}",
                f"{entry['board_size']}x{entry['board_size']}",
                entry['date']
            ))
    
    def load_leaderboard_board(self, event=None):
        """加载排行榜中的棋盘"""
        selection = self.leaderboard_tree.selection()
        if not selection:
            return
        
        item = self.leaderboard_tree.item(selection[0])
        rank = int(item['values'][0].replace('#', '')) - 1
        
        if 0 <= rank < len(self.leaderboard):
            entry = self.leaderboard[rank]
            
            # 加载棋盘
            self.board = entry['board']
            self.board_size = entry['board_size']
            self.size_var.set(self.board_size)
            
            # 加载方块设置
            for piece, count in entry['piece_counts'].items():
                if piece in self.piece_vars:
                    self.piece_vars[piece].set(count)
            
            self.create_board_display()
            messagebox.showinfo("加载成功", f"已加载排行榜第{rank+1}名的棋盘")
    
    def clear_leaderboard(self):
        """清空排行榜"""
        if messagebox.askyesno("确认清空", "确定要清空排行榜吗？"):
            self.leaderboard = []
            self.update_leaderboard_display()
            self.save_leaderboard()
            messagebox.showinfo("清空成功", "排行榜已清空")
    
    def load_leaderboard(self):
        """加载排行榜"""
        try:
            if os.path.exists("leaderboard.json"):
                with open("leaderboard.json", 'r', encoding='utf-8') as f:
                    self.leaderboard = json.load(f)
                self.update_leaderboard_display()
        except Exception as e:
            print(f"加载排行榜失败: {e}")
            self.leaderboard = []
    
    def save_leaderboard(self):
        """保存排行榜"""
        try:
            with open("leaderboard.json", 'w', encoding='utf-8') as f:
                json.dump(self.leaderboard, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存排行榜失败: {e}")
    
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = PuzzleGUI()
        app.run()
    except Exception as e:
        print(f"启动GUI失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
