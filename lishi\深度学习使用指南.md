# 🧠 深度学习拼图破解器使用指南

## 📁 生成的文件

### 模型文件
- `best_puzzle_model.pth` - 训练好的最佳模型
- `training_history.json` - 训练历史数据
- `training_curves.png` - 训练曲线图

### 使用方法

#### 1. Python中使用
```python
from 深度学习拼图破解器 import DeepPuzzleSolver

# 创建求解器
solver = DeepPuzzleSolver("best_puzzle_model.pth")

# 求解拼图
board = [[1, 0, 0], [0, 0, 0], [0, 0, 2]]
pieces = {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}

solution_steps, final_board = solver.solve(board, pieces)

# 获取提示
hint = solver.get_hint(board, pieces)
print(hint['message'])
```

#### 2. 网页中集成
将模型集成到您的网页拼图游戏中，提供AI智能求解功能。

## 🎯 性能指标
- 支持棋盘大小: 3x3 到 8x8
- 目标成功率: ≥90%
- 平均求解时间: <200ms (GPU) / <1s (CPU)
- 支持所有方块类型: T, 田, 横杠竖条, Z, L

## 🔧 优化建议
1. 使用GPU可显著提升训练和推理速度
2. 增加训练数据可提高模型泛化能力
3. 调整网络结构可适应特定拼图类型
