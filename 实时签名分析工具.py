#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时签名分析工具
配合Fiddler抓包分析getSmsCodeRandom等请求的sign生成算法
"""

import hashlib
import hmac
import time
import json
import re
import urllib.parse
from datetime import datetime

def parse_request_from_fiddler(fiddler_text):
    """解析从Fiddler复制的请求文本"""
    print("📋 解析Fiddler请求数据...")
    
    # 提取URL
    url_match = re.search(r'(GET|POST)\s+(.*?)\s+HTTP', fiddler_text)
    if url_match:
        method = url_match.group(1)
        url = url_match.group(2)
        print(f"请求方法: {method}")
        print(f"请求URL: {url}")
    
    # 提取请求头
    headers = {}
    header_lines = re.findall(r'^([^:]+):\s*(.*)$', fiddler_text, re.MULTILINE)
    for name, value in header_lines:
        headers[name.strip()] = value.strip()
    
    # 提取POST数据
    post_data = ""
    if "Content-Length:" in fiddler_text:
        # 找到空行后的内容作为POST数据
        parts = fiddler_text.split('\n\n')
        if len(parts) > 1:
            post_data = parts[-1].strip()
    
    return {
        'method': method if 'method' in locals() else 'GET',
        'url': url if 'url' in locals() else '',
        'headers': headers,
        'post_data': post_data
    }

def extract_parameters(url, post_data):
    """提取请求参数"""
    params = {}
    
    # 从URL查询参数提取
    if '?' in url:
        query_string = url.split('?')[1]
        url_params = urllib.parse.parse_qs(query_string)
        for key, values in url_params.items():
            params[key] = values[0] if values else ''
    
    # 从POST数据提取
    if post_data:
        try:
            # 尝试JSON格式
            json_data = json.loads(post_data)
            params.update(json_data)
        except:
            # 尝试表单格式
            try:
                form_params = urllib.parse.parse_qs(post_data)
                for key, values in form_params.items():
                    params[key] = values[0] if values else ''
            except:
                pass
    
    return params

def analyze_sign_algorithm(params):
    """分析签名算法"""
    print("\n🔍 分析签名算法...")
    print("=" * 50)
    
    # 查找可能的签名字段
    sign_fields = ['sign', 'signature', 'sig', 'hash', 'token']
    sign_value = None
    sign_field = None
    
    for field in sign_fields:
        if field in params:
            sign_value = params[field]
            sign_field = field
            break
    
    if not sign_value:
        print("❌ 未找到签名字段")
        return
    
    print(f"找到签名字段: {sign_field} = {sign_value}")
    
    # 提取其他参数
    other_params = {k: v for k, v in params.items() if k != sign_field}
    print(f"其他参数: {other_params}")
    
    # 尝试各种签名算法
    test_sign_algorithms(other_params, sign_value)

def test_sign_algorithms(params, target_sign):
    """测试各种签名算法"""
    print("\n🧪 测试签名算法...")

    # 常见的密钥 - 针对联通活动的可能密钥
    common_secrets = [
        'secret', 'key', 'app_secret', 'api_secret', 'sms_secret',
        '123456', 'abcdef123456', 'mobile_key', 'default_key',
        'your_secret_key', 'api_key_123', 'sms_key_2024',
        'unicom', 'wostore', 'activity', 'huodong', 'wo_activity',
        'bac27baa773c5bc2d3060edeb9881c64', 'channel_secret',
        'mobile_secret', 'activity_secret', '10155', 'huodong10155'
    ]

    # 获取参数
    mobile = params.get('mobile', params.get('phone', params.get('phoneNo', '')))
    activity_id = params.get('activityId', '')
    channel = params.get('channel', '')
    region_flag = params.get('regionLimitFlag', '')

    print(f"手机号: {mobile}")
    print(f"活动ID: {activity_id}")
    print(f"渠道: {channel}")
    print(f"区域标志: {region_flag}")
    print(f"目标签名: {target_sign}")
    print()

    found_match = False
    
    for secret in common_secrets:
        # 算法1: MD5(mobile + activityId + channel + secret)
        test_string = f"{mobile}{activity_id}{channel}{secret}"
        test_sign = hashlib.md5(test_string.encode()).hexdigest()
        if test_sign == target_sign or test_sign.upper() == target_sign.upper():
            print(f"✅ 匹配算法: MD5(mobile + activityId + channel + secret)")
            print(f"   密钥: {secret}")
            print(f"   签名字符串: {test_string}")
            print(f"   生成签名: {test_sign}")
            found_match = True
            break

        # 算法2: MD5(secret + mobile + activityId + channel)
        test_string = f"{secret}{mobile}{activity_id}{channel}"
        test_sign = hashlib.md5(test_string.encode()).hexdigest()
        if test_sign == target_sign or test_sign.upper() == target_sign.upper():
            print(f"✅ 匹配算法: MD5(secret + mobile + activityId + channel)")
            print(f"   密钥: {secret}")
            print(f"   签名字符串: {test_string}")
            print(f"   生成签名: {test_sign}")
            found_match = True
            break

        # 算法3: MD5(mobile + activityId + secret)
        test_string = f"{mobile}{activity_id}{secret}"
        test_sign = hashlib.md5(test_string.encode()).hexdigest()
        if test_sign == target_sign or test_sign.upper() == target_sign.upper():
            print(f"✅ 匹配算法: MD5(mobile + activityId + secret)")
            print(f"   密钥: {secret}")
            print(f"   签名字符串: {test_string}")
            print(f"   生成签名: {test_sign}")
            found_match = True
            break
        
        # 算法4: 参数排序后MD5 (不包含sign字段)
        params_without_sign = {k: v for k, v in params.items() if k != 'sign'}
        sorted_params = sorted(params_without_sign.items())
        param_string = '&'.join([f'{k}={v}' for k, v in sorted_params])
        test_string = param_string + '&key=' + secret
        test_sign = hashlib.md5(test_string.encode()).hexdigest()
        if test_sign == target_sign or test_sign.upper() == target_sign.upper():
            print(f"✅ 匹配算法: MD5(sorted_params + key)")
            print(f"   密钥: {secret}")
            print(f"   签名字符串: {test_string}")
            print(f"   生成签名: {test_sign}")
            found_match = True
            break

        # 算法5: 参数值直接拼接 + secret
        param_values = [str(v) for k, v in sorted(params_without_sign.items())]
        test_string = ''.join(param_values) + secret
        test_sign = hashlib.md5(test_string.encode()).hexdigest()
        if test_sign == target_sign or test_sign.upper() == target_sign.upper():
            print(f"✅ 匹配算法: MD5(sorted_param_values + secret)")
            print(f"   密钥: {secret}")
            print(f"   签名字符串: {test_string}")
            print(f"   生成签名: {test_sign}")
            found_match = True
            break

        # 算法6: HMAC-SHA1
        param_string = '&'.join([f'{k}={v}' for k, v in sorted(params_without_sign.items())])
        test_sign = hmac.new(secret.encode(), param_string.encode(), hashlib.sha1).hexdigest()
        if test_sign == target_sign or test_sign.upper() == target_sign.upper():
            print(f"✅ 匹配算法: HMAC-SHA1")
            print(f"   密钥: {secret}")
            print(f"   签名字符串: {param_string}")
            print(f"   生成签名: {test_sign}")
            found_match = True
            break
    
    if not found_match:
        print("❌ 未找到匹配的签名算法")
        print("💡 建议:")
        print("   1. 检查是否有其他隐藏参数")
        print("   2. 查看前端JS代码")
        print("   3. 尝试其他密钥组合")
        print("   4. 检查签名字段的大小写")

def generate_python_code(algorithm, secret, params):
    """生成Python代码"""
    print(f"\n📝 生成Python实现代码:")
    print("=" * 40)
    
    if "MD5(phone + timestamp + secret)" in algorithm:
        code = f'''
def generate_sign(phone):
    import hashlib
    import time
    
    timestamp = str(int(time.time()))
    secret = "{secret}"
    sign_string = f"{{phone}}{{timestamp}}{{secret}}"
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    
    return {{
        'phone': phone,
        'timestamp': timestamp,
        'sign': sign
    }}

# 使用示例
result = generate_sign("13800138000")
print(result)
'''
    elif "HMAC-SHA1" in algorithm:
        code = f'''
def generate_sign(phone):
    import hmac
    import hashlib
    import time
    
    timestamp = str(int(time.time()))
    secret = "{secret}"
    
    params = {{'phone': phone, 'timestamp': timestamp}}
    param_string = '&'.join([f'{{k}}={{v}}' for k, v in sorted(params.items())])
    sign = hmac.new(secret.encode(), param_string.encode(), hashlib.sha1).hexdigest()
    
    return {{
        'phone': phone,
        'timestamp': timestamp,
        'sign': sign
    }}
'''
    else:
        code = "# 请根据具体算法实现"
    
    print(code)

def main():
    """主函数"""
    print("🔐 实时签名分析工具")
    print("=" * 60)
    print("请将Fiddler中的请求内容粘贴到下面 (输入END结束):")
    print()
    
    # 读取Fiddler请求内容
    fiddler_text = ""
    while True:
        line = input()
        if line.strip().upper() == 'END':
            break
        fiddler_text += line + '\n'
    
    if not fiddler_text.strip():
        print("❌ 未输入请求内容")
        return
    
    # 解析请求
    request_info = parse_request_from_fiddler(fiddler_text)
    print(f"\n解析结果:")
    print(f"方法: {request_info['method']}")
    print(f"URL: {request_info['url']}")
    print(f"POST数据: {request_info['post_data']}")
    
    # 提取参数
    params = extract_parameters(request_info['url'], request_info['post_data'])
    print(f"\n提取的参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # 分析签名
    analyze_sign_algorithm(params)

if __name__ == "__main__":
    # 可以直接运行主函数进行交互式分析
    # main()
    
    # 或者使用示例数据测试
    print("🔐 实时签名分析工具 - 示例模式")
    print("=" * 60)
    
    # 真实请求参数
    example_params = {
        'mobile': '18699133341',
        'activityId': '625',
        'channel': 'bac27baa773c5bc2d3060edeb9881c64',
        'sign': '0aa2427fbb22fe6308893b59926b700e',
        'regionLimitFlag': '1'
    }
    
    print("示例参数:")
    for key, value in example_params.items():
        print(f"  {key}: {value}")
    
    if example_params['sign'] != 'your_actual_sign_here':
        analyze_sign_algorithm(example_params)
    else:
        print("\n💡 使用说明:")
        print("1. 使用Fiddler抓包获取getSmsCodeRandom请求")
        print("2. 复制完整的请求内容")
        print("3. 运行 main() 函数进行交互式分析")
        print("4. 或者修改example_params中的实际签名值进行测试")
