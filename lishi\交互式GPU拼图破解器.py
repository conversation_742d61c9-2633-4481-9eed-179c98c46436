#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交互式GPU拼图破解器
允许用户自定义棋盘、设置方块数量，然后使用GPU加速求解
"""

import json
from GPU拼图破解器完整版 import GPUTetrisPuzzleSolver

class InteractiveGPUPuzzleSolver:
    """交互式GPU拼图破解器"""
    
    def __init__(self):
        self.solver = GPUTetrisPuzzleSolver()
        self.board = None
        self.piece_counts = {
            "T": 1,
            "田": 1,
            "横杠竖条": 1,
            "Z": 1,
            "L": 1
        }
    
    def show_menu(self):
        """显示主菜单"""
        print("\n🧩 交互式GPU拼图破解器")
        print("=" * 50)
        print("1. 创建新棋盘")
        print("2. 加载预设棋盘")
        print("3. 设置方块数量")
        print("4. 显示当前棋盘")
        print("5. 🚀 GPU加速求解")
        print("6. 可视化解决方案")
        print("7. 保存/加载配置")
        print("8. 性能测试")
        print("0. 退出")
        print("=" * 50)
    
    def create_board(self):
        """创建新棋盘"""
        print("\n📋 创建新棋盘")
        
        try:
            size = int(input("请输入棋盘大小 (3-10): "))
            if size < 3 or size > 10:
                print("❌ 棋盘大小必须在3-10之间")
                return
            
            self.board = self.solver.create_board(size)
            print(f"✅ 创建了 {size}x{size} 的空棋盘")
            
            # 设置必需位置
            print("\n设置必需位置（红色✓）")
            print("格式: 行,列 (例如: 1,2) 或 'done' 完成")
            
            while True:
                pos_input = input("必需位置: ").strip()
                if pos_input.lower() == 'done':
                    break
                
                try:
                    r, c = map(int, pos_input.split(','))
                    if 0 <= r < size and 0 <= c < size:
                        self.board[r][c] = 1
                        print(f"✅ 设置必需位置: ({r},{c})")
                    else:
                        print("❌ 位置超出棋盘范围")
                except:
                    print("❌ 格式错误，请使用 '行,列' 格式")
            
            # 设置禁止位置
            print("\n设置禁止位置（灰色✗）")
            print("格式: 行,列 (例如: 0,6) 或 'done' 完成")
            
            while True:
                pos_input = input("禁止位置: ").strip()
                if pos_input.lower() == 'done':
                    break
                
                try:
                    r, c = map(int, pos_input.split(','))
                    if 0 <= r < size and 0 <= c < size:
                        if self.board[r][c] != 1:  # 不覆盖必需位置
                            self.board[r][c] = 2
                            print(f"✅ 设置禁止位置: ({r},{c})")
                        else:
                            print("❌ 该位置已是必需位置")
                    else:
                        print("❌ 位置超出棋盘范围")
                except:
                    print("❌ 格式错误，请使用 '行,列' 格式")
            
            print("✅ 棋盘创建完成")
            
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def load_preset_board(self):
        """加载预设棋盘"""
        print("\n📁 预设棋盘")
        print("1. 简单 3x3")
        print("2. 中等 5x5") 
        print("3. 复杂 7x7")
        print("4. 您的8x8测试棋盘")
        
        try:
            choice = int(input("选择预设 (1-4): "))
            
            if choice == 1:
                # 简单3x3
                self.board = self.solver.create_board(3)
                self.solver.set_required_positions(self.board, [(1, 1)])
                self.solver.set_forbidden_positions(self.board, [(0, 2)])
                self.piece_counts = {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
                
            elif choice == 2:
                # 中等5x5
                self.board = self.solver.create_board(5)
                self.solver.set_required_positions(self.board, [(1, 1), (3, 3)])
                self.solver.set_forbidden_positions(self.board, [(0, 4), (4, 0)])
                self.piece_counts = {"T": 1, "田": 1, "横杠竖条": 0, "Z": 1, "L": 0}
                
            elif choice == 3:
                # 复杂7x7
                self.board = self.solver.create_board(7)
                self.solver.set_required_positions(self.board, [(1, 1), (2, 3), (4, 5), (5, 2)])
                self.solver.set_forbidden_positions(self.board, [(0, 6), (6, 0), (3, 3)])
                self.piece_counts = {"T": 1, "田": 1, "横杠竖条": 1, "Z": 1, "L": 1}
                
            elif choice == 4:
                # 您的8x8测试棋盘
                self.board = [
                    [0, 0, 0, 0, 0, 1, 2, 0],
                    [0, 0, 0, 0, 0, 1, 1, 0],
                    [0, 0, 0, 0, 0, 0, 1, 2],
                    [0, 0, 0, 0, 0, 0, 1, 0],
                    [0, 0, 0, 0, 0, 0, 2, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0],
                    [0, 2, 0, 0, 0, 0, 0, 0],
                    [0, 0, 0, 0, 0, 0, 0, 0]
                ]
                self.piece_counts = {"T": 1, "田": 1, "横杠竖条": 1, "Z": 1, "L": 1}
            
            else:
                print("❌ 无效选择")
                return
            
            print("✅ 预设棋盘加载完成")
            
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def set_piece_counts(self):
        """设置方块数量"""
        print("\n🧩 设置方块数量")
        print("当前配置:")
        for piece, count in self.piece_counts.items():
            print(f"  {piece}: {count}")
        
        print("\n输入新的数量 (直接回车保持不变):")
        
        for piece in self.piece_counts.keys():
            try:
                new_count = input(f"{piece} (当前: {self.piece_counts[piece]}): ").strip()
                if new_count:
                    self.piece_counts[piece] = max(0, int(new_count))
            except ValueError:
                print(f"❌ {piece} 数量无效，保持原值")
        
        print("✅ 方块数量设置完成")
        print("新配置:")
        for piece, count in self.piece_counts.items():
            print(f"  {piece}: {count}")
    
    def display_board(self):
        """显示当前棋盘"""
        if self.board is None:
            print("❌ 请先创建或加载棋盘")
            return
        
        print(f"\n📋 当前棋盘 ({len(self.board)}x{len(self.board[0])})")
        print("图例: 0=空 1=必需(✓) 2=禁止(✗)")
        
        # 显示列号
        print("   ", end="")
        for c in range(len(self.board[0])):
            print(f"{c:2}", end=" ")
        print()
        
        # 显示棋盘
        for r, row in enumerate(self.board):
            print(f"{r:2} ", end="")
            for cell in row:
                if cell == 1:
                    print(" ✓", end=" ")
                elif cell == 2:
                    print(" ✗", end=" ")
                else:
                    print(f"{cell:2}", end=" ")
            print()
        
        print(f"\n🧩 方块配置: {self.piece_counts}")
    
    def solve_puzzle(self):
        """GPU加速求解拼图"""
        if self.board is None:
            print("❌ 请先创建或加载棋盘")
            return
        
        print("\n🚀 启动GPU加速求解")
        print("💡 请观察GPU使用率（任务管理器 -> 性能 -> GPU）")
        
        try:
            max_solutions = int(input("最大解决方案数量 (1-20): ") or "5")
            max_solutions = max(1, min(20, max_solutions))
        except ValueError:
            max_solutions = 5
        
        # 开始求解
        solutions = self.solver.gpu_accelerated_solve(
            self.board, self.piece_counts, 
            algorithm="交互式GPU求解", 
            max_solutions=max_solutions
        )
        
        if solutions:
            print(f"\n🎉 找到 {len(solutions)} 个解决方案！")
            print(f"⚡ GPU计算时间: {self.solver.gpu_time:.3f}秒")
            print(f"📊 总求解时间: {self.solver.solve_time:.3f}秒")
            
            if self.solver.gpu_available:
                speedup = self.solver.solve_time / max(self.solver.gpu_time, 0.001)
                print(f"🚀 GPU加速效果: {speedup:.1f}x")
        else:
            print("❌ 未找到解决方案")
            print("💡 建议:")
            print("  1. 减少方块数量")
            print("  2. 调整必需位置")
            print("  3. 减少禁止位置")
    
    def visualize_solutions(self):
        """可视化解决方案"""
        if not self.solver.solutions:
            print("❌ 没有可视化的解决方案，请先求解")
            return
        
        print(f"\n📊 可视化解决方案 (共{len(self.solver.solutions)}个)")
        
        try:
            idx = int(input(f"选择解决方案 (0-{len(self.solver.solutions)-1}): ") or "0")
            if 0 <= idx < len(self.solver.solutions):
                filename = f"solution_{idx+1}.png"
                self.solver.visualize_solution(idx, filename)
            else:
                print("❌ 无效的解决方案编号")
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def save_load_config(self):
        """保存/加载配置"""
        print("\n💾 配置管理")
        print("1. 保存当前配置")
        print("2. 加载配置")
        
        try:
            choice = int(input("选择操作 (1-2): "))
            
            if choice == 1:
                filename = input("配置文件名 (默认: puzzle_config.json): ").strip()
                if not filename:
                    filename = "puzzle_config.json"
                
                config = {
                    'board': self.board,
                    'piece_counts': self.piece_counts
                }
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                print(f"✅ 配置已保存: {filename}")
                
            elif choice == 2:
                filename = input("配置文件名: ").strip()
                
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    self.board = config['board']
                    self.piece_counts = config['piece_counts']
                    
                    print(f"✅ 配置已加载: {filename}")
                    
                except FileNotFoundError:
                    print("❌ 配置文件不存在")
                except json.JSONDecodeError:
                    print("❌ 配置文件格式错误")
            
            else:
                print("❌ 无效选择")
                
        except ValueError:
            print("❌ 请输入有效的数字")
    
    def performance_test(self):
        """性能测试"""
        print("\n⚡ GPU性能测试")
        
        test_cases = [
            {
                'name': '小型 3x3',
                'board': [[1, 0, 0], [0, 0, 0], [0, 0, 2]],
                'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
            },
            {
                'name': '中型 5x5',
                'board': [[0, 1, 0, 0, 0], [0, 0, 0, 1, 0], [2, 0, 0, 0, 0], [0, 0, 1, 0, 2], [0, 0, 0, 0, 0]],
                'pieces': {"T": 1, "田": 1, "横杠竖条": 1, "Z": 0, "L": 0}
            }
        ]
        
        print("开始性能测试...")
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n测试 {i}: {case['name']}")
            
            solutions = self.solver.gpu_accelerated_solve(
                case['board'], case['pieces'], 
                algorithm=f"性能测试{i}", max_solutions=3
            )
            
            print(f"  结果: {len(solutions)}个解决方案")
            print(f"  GPU时间: {self.solver.gpu_time:.3f}秒")
            print(f"  总时间: {self.solver.solve_time:.3f}秒")
        
        print("\n✅ 性能测试完成")
    
    def run(self):
        """运行交互式界面"""
        print("🎮 欢迎使用交互式GPU拼图破解器！")
        print("基于您的HTML游戏重写的Python版本")
        
        while True:
            self.show_menu()
            
            try:
                choice = int(input("\n请选择操作: "))
                
                if choice == 0:
                    print("👋 再见！")
                    break
                elif choice == 1:
                    self.create_board()
                elif choice == 2:
                    self.load_preset_board()
                elif choice == 3:
                    self.set_piece_counts()
                elif choice == 4:
                    self.display_board()
                elif choice == 5:
                    self.solve_puzzle()
                elif choice == 6:
                    self.visualize_solutions()
                elif choice == 7:
                    self.save_load_config()
                elif choice == 8:
                    self.performance_test()
                else:
                    print("❌ 无效选择，请重试")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出程序")
                break

if __name__ == "__main__":
    app = InteractiveGPUPuzzleSolver()
    app.run()
