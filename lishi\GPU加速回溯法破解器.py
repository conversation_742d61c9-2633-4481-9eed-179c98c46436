import numpy as np
import numba
from numba import cuda, types
import time
import math

class GPUBacktrackSolver:
    """GPU加速回溯法拼图求解器"""
    
    def __init__(self):
        try:
            self.device = cuda.get_current_device()
            print(f"🎮 使用GPU: {self.device.name}")

            # 安全获取显存信息
            try:
                memory_info = cuda.current_context().get_memory_info()
                total_memory = memory_info[1] / 1024**3
                print(f"📊 显存: {total_memory:.1f}GB")
            except:
                print("📊 显存: 信息获取失败，但GPU可用")

        except Exception as e:
            print(f"❌ GPU初始化失败: {e}")
            raise
        
        # 俄罗斯方块定义 (编码为数字数组以便GPU处理)
        self.tetris_shapes = self._encode_shapes()
        
    def _encode_shapes(self):
        """将方块形状编码为GPU友好的格式"""
        shapes = {
            "T": [
                [(0,1), (1,0), (1,1), (1,2)],  # T形状 - 上
                [(0,1), (1,1), (1,2), (2,1)],  # T形状 - 右
                [(0,0), (1,0), (1,1), (2,0)],  # T形状 - 右
                [(0,0), (0,1), (0,2), (1,1)],  # T形状 - 下
                [(0,1), (1,0), (1,1), (2,1)]   # T形状 - 左
            ],
            "田": [[(0,0), (0,1), (1,0), (1,1)]],
            "横杠竖条": [
                [(0,0), (0,1), (0,2), (0,3)],  # 水平
                [(0,0), (1,0), (2,0), (3,0)]   # 垂直
            ],
            "Z": [
                [(0,0), (0,1), (1,1), (1,2)],  # Z形状
                [(0,1), (1,0), (1,1), (2,0)],  # Z形状 - 90度
                [(0,1), (0,2), (1,0), (1,1)],  # S形状
                [(0,0), (1,0), (1,1), (2,1)]   # S形状 - 90度
            ],
            "L": [
                [(0,0), (1,0), (2,0), (2,1)],  # L形状
                [(0,1), (1,1), (2,1), (2,0)],  # J形状
                [(0,0), (1,0), (1,1), (1,2)],  # J形状 - 90度
                [(0,0), (0,1), (1,0), (2,0)],  # J形状 - 180度
                [(0,0), (0,1), (0,2), (1,2)]   # J形状 - 270度
            ]
        }
        
        # 转换为numpy数组格式
        encoded_shapes = {}
        shape_id = 0
        
        for piece_name, rotations in shapes.items():
            piece_shapes = []
            for rotation in rotations:
                # 每个形状最多4个方块，用-1填充
                shape_array = np.full((4, 2), -1, dtype=np.int32)
                for i, (r, c) in enumerate(rotation):
                    shape_array[i] = [r, c]
                piece_shapes.append(shape_array)
            encoded_shapes[piece_name] = np.array(piece_shapes, dtype=np.int32)
            shape_id += 1
            
        return encoded_shapes
    
    def solve(self, board, pieces, mode='rotating', max_solutions=1000):
        """GPU加速求解"""
        print(f"🚀 启动GPU加速回溯法求解")
        print(f"📊 棋盘大小: {len(board)}x{len(board[0])}")
        print(f"🧩 方块配置: {pieces}")
        
        # 准备数据
        board_array = np.array(board, dtype=np.int32)
        board_size = len(board)
        
        # 编码方块信息
        piece_info = self._encode_pieces(pieces)
        
        # 准备GPU内存
        solutions = self._gpu_backtrack_solve(
            board_array, piece_info, board_size, max_solutions
        )
        
        return self._decode_solutions(solutions, board_array)
    
    def _encode_pieces(self, pieces):
        """编码方块信息为GPU格式"""
        piece_names = ["T", "田", "横杠竖条", "Z", "L"]
        piece_counts = np.zeros(5, dtype=np.int32)
        
        for i, name in enumerate(piece_names):
            piece_counts[i] = pieces.get(name, 0)
            
        return piece_counts
    
    def _gpu_backtrack_solve(self, board, piece_counts, board_size, max_solutions):
        """GPU并行回溯求解"""
        # 计算网格大小
        threads_per_block = 256
        blocks_per_grid = min(65535, (max_solutions + threads_per_block - 1) // threads_per_block)
        
        # 准备GPU数据
        d_board = cuda.to_device(board)
        d_piece_counts = cuda.to_device(piece_counts)
        
        # 准备形状数据
        all_shapes = self._prepare_shapes_for_gpu()
        d_shapes = cuda.to_device(all_shapes)
        
        # 结果数组
        solutions = np.zeros((max_solutions, board_size, board_size), dtype=np.int32)
        solution_found = np.zeros(max_solutions, dtype=np.int32)
        
        d_solutions = cuda.to_device(solutions)
        d_solution_found = cuda.to_device(solution_found)
        
        print(f"🔥 启动GPU内核: {blocks_per_grid} blocks × {threads_per_block} threads")
        
        start_time = time.time()
        
        # 启动GPU内核
        gpu_backtrack_kernel[blocks_per_grid, threads_per_block](
            d_board, d_piece_counts, d_shapes, board_size,
            d_solutions, d_solution_found, max_solutions
        )
        
        cuda.synchronize()
        
        # 获取结果
        solutions = d_solutions.copy_to_host()
        solution_found = d_solution_found.copy_to_host()
        
        solve_time = time.time() - start_time
        found_count = np.sum(solution_found)
        
        print(f"⚡ GPU求解完成: {solve_time:.3f}秒")
        print(f"🎯 找到解决方案: {found_count}个")
        
        return solutions[solution_found == 1]
    
    def _prepare_shapes_for_gpu(self):
        """为GPU准备形状数据"""
        # 创建一个大的数组包含所有形状
        max_shapes_per_piece = 5
        max_blocks_per_shape = 4
        
        all_shapes = np.full((5, max_shapes_per_piece, max_blocks_per_shape, 2), -1, dtype=np.int32)
        
        piece_names = ["T", "田", "横杠竖条", "Z", "L"]
        for i, name in enumerate(piece_names):
            shapes = self.tetris_shapes[name]
            for j, shape in enumerate(shapes):
                all_shapes[i, j] = shape
                
        return all_shapes
    
    def _decode_solutions(self, solutions, original_board):
        """解码GPU解决方案"""
        if len(solutions) == 0:
            return [], original_board.tolist()
        
        # 返回第一个解决方案
        best_solution = solutions[0]
        
        # 生成步骤序列
        steps = self._generate_steps(original_board, best_solution)
        
        return steps, best_solution.tolist()
    
    def _generate_steps(self, original_board, solution_board):
        """从解决方案生成步骤序列"""
        steps = []
        piece_names = ["T", "田", "横杠竖条", "Z", "L"]
        
        # 简化版本：只返回放置的方块信息
        for i in range(len(solution_board)):
            for j in range(len(solution_board[0])):
                if solution_board[i][j] > 2:
                    piece_id = solution_board[i][j] - 3
                    if piece_id < len(piece_names):
                        steps.append({
                            'step': len(steps) + 1,
                            'piece': piece_names[piece_id],
                            'position': (i, j),
                            'rotation': 0
                        })
                        break
        
        return steps

# GPU内核函数
@cuda.jit
def gpu_backtrack_kernel(board, piece_counts, shapes, board_size, solutions, solution_found, max_solutions):
    """GPU回溯法内核"""
    idx = cuda.grid(1)
    
    if idx >= max_solutions:
        return
    
    # 每个线程尝试不同的起始策略
    thread_board = cuda.local.array((8, 8), numba.int32)
    thread_pieces = cuda.local.array(5, numba.int32)
    
    # 复制初始状态
    for i in range(board_size):
        for j in range(board_size):
            thread_board[i][j] = board[i][j]
    
    for i in range(5):
        thread_pieces[i] = piece_counts[i]
    
    # 尝试求解
    if gpu_solve_recursive(thread_board, thread_pieces, shapes, board_size, 0, idx):
        # 找到解决方案
        solution_found[idx] = 1
        for i in range(board_size):
            for j in range(board_size):
                solutions[idx][i][j] = thread_board[i][j]

@cuda.jit(device=True)
def gpu_solve_recursive(board, pieces, shapes, board_size, depth, thread_id):
    """GPU递归回溯函数"""
    if depth > 20:  # 防止过深递归
        return False
    
    # 检查是否完成
    if gpu_is_solved(board, board_size):
        return True
    
    # 检查是否还有方块
    has_pieces = False
    for i in range(5):
        if pieces[i] > 0:
            has_pieces = True
            break
    
    if not has_pieces:
        return False
    
    # 尝试放置每种方块
    for piece_type in range(5):
        if pieces[piece_type] <= 0:
            continue
            
        # 尝试该方块的每种旋转
        for rotation in range(5):  # 最多5种旋转
            if shapes[piece_type][rotation][0][0] == -1:  # 无效旋转
                continue
                
            # 尝试每个位置
            for start_r in range(board_size):
                for start_c in range(board_size):
                    if gpu_can_place(board, shapes[piece_type][rotation], start_r, start_c, board_size):
                        # 放置方块
                        piece_id = piece_type + 3
                        gpu_place_piece(board, shapes[piece_type][rotation], start_r, start_c, piece_id)
                        pieces[piece_type] -= 1
                        
                        # 递归求解
                        if gpu_solve_recursive(board, pieces, shapes, board_size, depth + 1, thread_id):
                            return True
                        
                        # 回溯
                        gpu_remove_piece(board, shapes[piece_type][rotation], start_r, start_c)
                        pieces[piece_type] += 1
    
    return False

@cuda.jit(device=True)
def gpu_can_place(board, shape, start_r, start_c, board_size):
    """检查是否可以放置方块"""
    for i in range(4):
        if shape[i][0] == -1:  # 无效位置
            break
            
        r = start_r + shape[i][0]
        c = start_c + shape[i][1]
        
        if r < 0 or r >= board_size or c < 0 or c >= board_size:
            return False
        if board[r][c] > 2:  # 已被占用
            return False
        if board[r][c] == 2:  # 禁止位置
            return False
    
    return True

@cuda.jit(device=True)
def gpu_place_piece(board, shape, start_r, start_c, piece_id):
    """在GPU上放置方块"""
    for i in range(4):
        if shape[i][0] == -1:
            break
        r = start_r + shape[i][0]
        c = start_c + shape[i][1]
        board[r][c] = piece_id

@cuda.jit(device=True)
def gpu_remove_piece(board, shape, start_r, start_c):
    """在GPU上移除方块"""
    for i in range(4):
        if shape[i][0] == -1:
            break
        r = start_r + shape[i][0]
        c = start_c + shape[i][1]
        board[r][c] = 0

@cuda.jit(device=True)
def gpu_is_solved(board, board_size):
    """检查是否解决"""
    for i in range(board_size):
        for j in range(board_size):
            if board[i][j] == 1:  # 必需位置未被覆盖
                return False
    return True

def main():
    """测试GPU加速回溯法"""
    print("🚀 GPU加速回溯法拼图破解器")
    print("=" * 60)
    
    # 检查CUDA
    if not cuda.is_available():
        print("❌ CUDA不可用，请安装CUDA支持")
        return
    
    solver = GPUBacktrackSolver()
    
    # 测试用例
    test_cases = [
        {
            'name': '4x4 测试',
            'board': [
                [1, 0, 0, 1],
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [2, 0, 0, 2]
            ],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '5x5 测试',
            'board': [
                [0, 1, 0, 0, 0],
                [0, 0, 0, 1, 0],
                [2, 0, 0, 0, 0],
                [0, 0, 1, 0, 2],
                [0, 0, 0, 0, 0]
            ],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 1, "Z": 0, "L": 0}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        solution_steps, final_board = solver.solve(
            test_case['board'], 
            test_case['pieces']
        )
        solve_time = time.time() - start_time
        
        if solution_steps:
            print(f"✅ 求解成功! {len(solution_steps)}步, {solve_time:.3f}秒")
            print("📋 解决方案:")
            for step in solution_steps:
                print(f"  步骤{step['step']}: {step['piece']} → {step['position']}")
        else:
            print(f"❌ 求解失败, {solve_time:.3f}秒")

if __name__ == "__main__":
    main()
