#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QFontDatabase>
#include "TetrisPuzzleGame.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("俄罗斯方块拼图游戏");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("TetrisPuzzle");
    
    // Set application style
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // Set application stylesheet for better appearance
    QString styleSheet = R"(
        QMainWindow {
            background-color: #f0f0f0;
        }
        
        QWidget {
            font-family: Arial, sans-serif;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            border: 1px solid #cccccc;
            border-radius: 4px;
            padding: 4px 8px;
            background-color: #f9f9f9;
            min-height: 20px;
        }
        
        QPushButton:hover {
            background-color: #e9e9e9;
            border-color: #999999;
        }
        
        QPushButton:pressed {
            background-color: #d9d9d9;
        }
        
        QPushButton:checked {
            background-color: #4CAF50;
            color: white;
            border-color: #45a049;
        }
        
        QComboBox {
            border: 1px solid #cccccc;
            border-radius: 3px;
            padding: 2px 5px;
            background-color: white;
            min-height: 20px;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #666666;
            margin-right: 5px;
        }
        
        QTextEdit {
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: white;
            font-family: monospace;
        }
        
        QCheckBox {
            spacing: 5px;
        }
        
        QCheckBox::indicator {
            width: 13px;
            height: 13px;
            border: 1px solid #cccccc;
            border-radius: 2px;
            background-color: white;
        }
        
        QCheckBox::indicator:checked {
            background-color: #4CAF50;
            border-color: #45a049;
        }
        
        QCheckBox::indicator:checked::after {
            content: "✓";
            color: white;
            font-weight: bold;
        }
        
        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 10px;
            background-color: #f0f0f0;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 9px;
        }
        
        QScrollArea {
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: white;
        }
        
        QScrollBar:vertical {
            border: none;
            background-color: #f0f0f0;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #cccccc;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #999999;
        }
        
        QScrollBar::add-line:vertical,
        QScrollBar::sub-line:vertical {
            border: none;
            background: none;
        }
        
        QLabel {
            color: #333333;
        }
        
        QToolTip {
            background-color: #ffffcc;
            border: 1px solid #cccccc;
            border-radius: 3px;
            padding: 3px;
            color: #333333;
        }
    )";
    
    app.setStyleSheet(styleSheet);
    
    // Create and show the main window
    TetrisPuzzleGame game;
    game.show();
    
    return app.exec();
}
