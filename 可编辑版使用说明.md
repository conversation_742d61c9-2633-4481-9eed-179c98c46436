# 短信发送工具 - 可编辑表单版使用说明

## 概述
这是一个从易语言代码转换而来的Python GUI短信发送工具的可编辑版本。与之前版本不同，这个版本允许您完全自定义每个请求的所有参数。

## 主要特点

### ✨ 全新的可编辑表单设计
- **标签0（名称）**: 为每个请求设置自定义名称，便于识别
- **编辑框1（网址）**: 完全可编辑的请求URL
- **选择框2（方式）**: GET或POST请求方式选择
- **编辑框3（数据）**: 自定义POST请求数据
- **编辑框4（协议头）**: 自定义HTTP请求头

### 🔧 强大的表单管理功能
- **添加空白表单**: 创建新的自定义请求
- **删除表单**: 移除不需要的请求
- **保存配置**: 将所有表单保存到文件
- **加载配置**: 从文件恢复表单设置
- **重置为预设**: 恢复到默认的9个平台

### 📱 智能手机号替换
- 在网址和数据中使用 `{phone}` 占位符
- 程序会自动替换为您输入的手机号
- 支持在任意位置使用占位符

## 界面布局

### 1. 基本设置区域
- **手机号输入框**: 输入目标手机号码

### 2. 操作控制区域
- **一键发送所有**: 自动发送所有有效的请求表单
- **添加空白表单**: 创建新的空白请求表单
- **保存所有配置**: 保存当前所有表单到配置文件
- **加载配置**: 从配置文件加载表单
- **清空所有表单**: 删除所有表单
- **重置为预设**: 恢复到默认的9个预设表单

### 3. 请求表单列表区域
每个表单包含：
- **名称**: 请求的自定义名称
- **网址**: 完整的请求URL
- **方式**: GET或POST选择
- **数据**: POST请求的数据内容
- **协议头**: HTTP请求头（每行一个，格式：键: 值）
- **发送此请求**: 单独发送这个请求
- **删除**: 删除这个表单

### 4. 执行日志区域
- 实时显示请求过程和结果
- 包含时间戳和详细信息
- 支持清空日志功能

## 使用步骤

### 快速开始
1. **启动程序**: 双击 `启动工具.bat` 或运行 `python 短信工具可编辑版.py`
2. **输入密码**: 默认密码是 `1995`
3. **输入手机号**: 在基本设置区域输入目标手机号
4. **选择操作**:
   - 使用预设表单：直接点击"一键发送所有"
   - 自定义表单：编辑现有表单或添加新表单

### 编辑表单
1. **修改现有表单**:
   - 直接在表单中编辑名称、网址、方式、数据、协议头
   - 使用 `{phone}` 占位符代表手机号
   - 点击"发送此请求"测试单个表单

2. **添加新表单**:
   - 点击"添加空白表单"
   - 填写所有必要信息
   - 保存配置以便下次使用

3. **协议头格式**:
   ```
   Content-Type: application/json
   User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)
   Accept: application/json, text/plain, */*
   ```

### 配置管理
- **保存配置**: 将当前所有表单和手机号保存到 `sms_tool_config.json`
- **加载配置**: 从配置文件恢复之前的设置
- **自动加载**: 程序启动时会自动尝试加载上次的配置

## 预设表单说明

程序包含9个预设的短信平台表单：

1. **大众工商网** - GET请求，简单参数
2. **索尼音乐** - POST请求，需要验证码
3. **人大金仓** - POST请求，JSON格式
4. **水果批发** - POST请求，JSON数据
5. **水利部** - GET请求，特殊协议头
6. **云标科技** - POST请求，表单数据
7. **温柔怪物** - POST请求，JSON格式
8. **7net平台** - POST请求，需要验证码
9. **天启教育** - POST请求，表单数据

## 高级功能

### 占位符系统
- `{phone}` - 自动替换为输入的手机号
- 可在网址和数据的任意位置使用
- 支持多次使用

### 批量发送
- "一键发送所有"会依次发送所有有名称和网址的表单
- 每个请求间隔2秒，避免频率过高
- 实时显示发送进度和结果

### 日志系统
- 详细记录每个请求的过程
- 包含请求URL、方法、响应状态码
- 显示响应内容的前300字符
- 支持清空日志功能

## 注意事项

### 安全提醒
1. **合法使用**: 仅用于测试自己的手机号或经过授权的号码
2. **频率控制**: 避免过于频繁的请求，遵守网站规则
3. **隐私保护**: 不要泄露他人手机号信息
4. **密码保护**: 默认密码1995，建议修改源码中的密码

### 技术限制
1. **验证码**: 部分平台需要图形验证码，目前使用默认值
2. **网络环境**: 需要稳定的网络连接
3. **SSL证书**: 程序忽略SSL证书验证，适用于测试环境
4. **请求限制**: 某些平台可能有IP或频率限制

### 故障排除
1. **程序无法启动**: 检查Python环境和依赖库
2. **请求失败**: 检查网络连接和URL正确性
3. **配置丢失**: 检查 `sms_tool_config.json` 文件是否存在
4. **界面卡顿**: 所有网络请求都在后台线程执行，不应卡顿

## 文件说明

- `短信工具可编辑版.py` - 主程序文件
- `启动工具.bat` - Windows启动脚本
- `启动工具.sh` - Linux/Mac启动脚本
- `sms_tool_config.json` - 配置文件（运行后生成）
- `可编辑版使用说明.md` - 本说明文档

## 依赖要求

- Python 3.6+
- tkinter（通常随Python安装）
- requests库
- urllib3库

安装依赖：
```bash
pip install requests urllib3
```

## 版本信息

- **版本**: v2.0 可编辑表单版
- **基于**: 易语言短信发送工具转换
- **开发语言**: Python 3.x
- **GUI框架**: tkinter
- **特色**: 完全可编辑的表单界面

## 更新日志

- **v2.0**: 可编辑表单版本
  - 重新设计界面，支持完全自定义表单
  - 添加表单管理功能（添加、删除、保存、加载）
  - 改进滚动界面，支持大量表单
  - 优化日志显示和错误处理
  - 增强配置管理功能

- **v1.0**: 初始版本
  - 基本的短信发送功能
  - 固定的快捷按钮界面

## 技术支持

如有问题或建议，请：
1. 查看本使用说明文档
2. 检查日志中的错误信息
3. 确认网络连接和配置正确性

**请合理使用本工具，遵守相关法律法规！**
