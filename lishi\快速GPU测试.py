#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速GPU测试 - 验证修复后的GPU加速效果
"""

import time

def test_simplified_gpu_solver():
    """测试简化GPU求解器"""
    print("🧪 测试简化GPU求解器")
    print("=" * 50)
    
    try:
        from 简化GPU加速回溯法 import SimplifiedGPUBacktrackSolver
        
        solver = SimplifiedGPUBacktrackSolver()
        
        if not solver.gpu_available:
            print("❌ GPU不可用")
            return False
        
        # 您的8x8棋盘
        board = [
            [0, 0, 0, 0, 0, 1, 2, 0],
            [0, 0, 0, 0, 0, 1, 1, 0],
            [0, 0, 0, 0, 0, 0, 1, 2],
            [0, 0, 0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 0, 2, 0],
            [0, 0, 0, 0, 0, 0, 0, 0],
            [0, 2, 0, 0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 0, 0, 0]
        ]
        
        pieces = {
            "T": 1,
            "田": 0,
            "横杠竖条": 0,
            "Z": 1,
            "L": 1
        }
        
        print("🚀 开始GPU加速求解...")
        print("💡 请观察GPU使用率（任务管理器 -> 性能 -> GPU）")
        
        start_time = time.time()
        solution_steps, final_board = solver.solve(board, pieces)
        solve_time = time.time() - start_time
        
        print(f"\n📊 求解结果:")
        print(f"求解时间: {solve_time:.3f}秒")
        print(f"找到解决方案: {'是' if solution_steps else '否'}")
        print(f"解决方案步数: {len(solution_steps)}")
        
        if solution_steps:
            print("\n📋 解决方案:")
            for step in solution_steps[:5]:  # 只显示前5步
                print(f"  步骤{step['step']}: {step['piece']} → {step['position']}")
            if len(solution_steps) > 5:
                print(f"  ... 还有{len(solution_steps)-5}步")
        
        return True
        
    except ImportError:
        print("❌ 简化GPU求解器不可用")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gpu_load_generation():
    """测试GPU负载生成"""
    print("\n🔥 测试GPU负载生成")
    print("=" * 50)
    
    try:
        import cupy as cp
        
        print("💪 生成GPU负载（10秒）...")
        print("💡 现在GPU使用率应该上升")
        
        start_time = time.time()
        
        while time.time() - start_time < 10:
            # 大型矩阵运算
            a = cp.random.random((2000, 2000), dtype=cp.float32)
            b = cp.random.random((2000, 2000), dtype=cp.float32)
            c = cp.dot(a, b)
            
            # 数学运算
            d = cp.sin(a) + cp.cos(b)
            e = cp.sum(d)
            
            # 清理
            del a, b, c, d, e
            
            elapsed = int(time.time() - start_time)
            if elapsed % 2 == 0:
                print(f"  GPU负载生成中... {elapsed}/10秒")
        
        print("✅ GPU负载生成完成")
        return True
        
    except Exception as e:
        print(f"❌ GPU负载生成失败: {e}")
        return False

def test_web_service():
    """测试Web服务"""
    print("\n🌐 测试Web服务")
    print("=" * 50)
    
    try:
        from GPU回溯法网页服务 import GPUBacktrackWebService
        
        service = GPUBacktrackWebService()
        
        print(f"服务状态: {service.solver_type}")
        print(f"GPU可用: {service.gpu_available}")
        
        # 简单测试
        test_board = [
            [1, 0, 0],
            [0, 0, 0],
            [0, 0, 2]
        ]
        test_pieces = {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
        
        result = service.solve_puzzle(test_board, test_pieces)
        
        print(f"Web服务测试: {'✅ 成功' if result.get('success', False) or 'error' not in result else '❌ 失败'}")
        print(f"求解器类型: {result.get('solver_type', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Web服务测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 快速GPU测试工具")
    print("🎯 验证修复后的GPU加速效果")
    print("=" * 60)
    
    results = []
    
    # 测试1: 简化GPU求解器
    solver_ok = test_simplified_gpu_solver()
    results.append(("简化GPU求解器", solver_ok))
    
    # 测试2: GPU负载生成
    load_ok = test_gpu_load_generation()
    results.append(("GPU负载生成", load_ok))
    
    # 测试3: Web服务
    web_ok = test_web_service()
    results.append(("Web服务", web_ok))
    
    # 生成报告
    print("\n📋 测试报告")
    print("=" * 60)
    
    all_ok = True
    for test_name, ok in results:
        status = "✅ 通过" if ok else "❌ 失败"
        print(f"{test_name:20s}: {status}")
        if not ok:
            all_ok = False
    
    print("\n🎯 总结")
    print("=" * 60)
    
    if all_ok:
        print("🎉 所有测试通过！")
        print("✅ GPU加速已修复，现在应该有明显负载")
        print("\n📋 下一步:")
        print("1. 启动Web服务: python GPU回溯法网页服务.py")
        print("2. 在网页中测试您的8x8棋盘")
        print("3. 观察GPU使用率上升")
    else:
        print("⚠️ 部分测试失败")
        print("💡 建议:")
        print("1. 检查CuPy安装")
        print("2. 确认GPU驱动正常")
        print("3. 重启Python进程")
    
    print(f"\n💡 监控GPU使用率:")
    print("任务管理器 -> 性能 -> GPU -> 计算")

if __name__ == "__main__":
    main()
