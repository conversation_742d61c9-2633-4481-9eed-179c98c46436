import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random
import time
import json
from collections import deque
import matplotlib.pyplot as plt

class PuzzleNet(nn.Module):
    """深度学习拼图破解网络"""
    def __init__(self, max_board_size=8, num_pieces=5):
        super(PuzzleNet, self).__init__()
        self.max_board_size = max_board_size
        self.num_pieces = num_pieces
        
        # 棋盘特征提取网络 - 使用ResNet风格
        self.board_conv = nn.Sequential(
            # 第一层：基础特征提取
            nn.Conv2d(1, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            
            # 残差块1
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 64, kernel_size=3, padding=1),
            nn.BatchNorm2d(64),
        )
        
        # 深层特征提取
        self.deep_conv = nn.Sequential(
            nn.Conv2d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, kernel_size=3, padding=1),
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            
            # 注意力机制
            nn.Conv2d(128, 256, kernel_size=3, padding=1),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
        )
        
        # 全局特征池化
        self.global_pool = nn.AdaptiveAvgPool2d((4, 4))
        
        # 方块信息处理网络
        self.piece_net = nn.Sequential(
            nn.Linear(num_pieces, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(64, 128),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(128, 256),
            nn.ReLU(inplace=True)
        )
        
        # 融合决策网络
        self.decision_net = nn.Sequential(
            nn.Linear(256 * 4 * 4 + 256, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.4),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Linear(256, max_board_size * max_board_size)
        )
        
        # 价值评估网络
        self.value_net = nn.Sequential(
            nn.Linear(256 * 4 * 4 + 256, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 1),
            nn.Tanh()
        )
        
    def forward(self, board, pieces):
        batch_size = board.size(0)
        
        # 棋盘特征提取
        board_features = self.board_conv(board)
        # 残差连接
        board_features = board_features + board
        
        # 深层特征
        deep_features = self.deep_conv(board_features)
        
        # 全局池化
        global_features = self.global_pool(deep_features)
        global_features = global_features.view(batch_size, -1)
        
        # 方块特征
        piece_features = self.piece_net(pieces)
        
        # 特征融合
        combined_features = torch.cat([global_features, piece_features], dim=1)
        
        # 位置预测
        position_scores = self.decision_net(combined_features)
        
        # 价值评估
        value = self.value_net(combined_features)
        
        return position_scores, value

class PuzzleEnvironment:
    """拼图环境"""
    def __init__(self, board_size=5):
        self.board_size = board_size
        self.reset()
        
        # 俄罗斯方块定义
        self.tetris_shapes = {
            "T": [
                [[0,1], [1,0], [1,1], [1,2]],  # T形状 - 上
                [[0,1], [1,1], [1,2], [2,1]],  # T形状 - 右
                [[0,0], [1,0], [1,1], [2,0]],  # T形状 - 右
                [[0,0], [0,1], [0,2], [1,1]],  # T形状 - 下
                [[0,1], [1,0], [1,1], [2,1]]   # T形状 - 左
            ],
            "田": [[[0,0], [0,1], [1,0], [1,1]]],
            "横杠竖条": [
                [[0,0], [0,1], [0,2], [0,3]],  # 水平
                [[0,0], [1,0], [2,0], [3,0]]   # 垂直
            ],
            "Z": [
                [[0,0], [0,1], [1,1], [1,2]],  # Z形状
                [[0,1], [1,0], [1,1], [2,0]],  # Z形状 - 90度
                [[0,1], [0,2], [1,0], [1,1]],  # S形状
                [[0,0], [1,0], [1,1], [2,1]]   # S形状 - 90度
            ],
            "L": [
                [[0,0], [1,0], [2,0], [2,1]],  # L形状
                [[0,1], [1,1], [2,1], [2,0]],  # J形状
                [[0,0], [1,0], [1,1], [1,2]],  # J形状 - 90度
                [[0,0], [0,1], [1,0], [2,0]],  # J形状 - 180度
                [[0,0], [0,1], [0,2], [1,2]]   # J形状 - 270度
            ]
        }
        
    def reset(self):
        """重置环境"""
        self.board = np.zeros((self.board_size, self.board_size), dtype=np.int32)
        self.required_positions = set()
        self.forbidden_positions = set()
        self.pieces_left = {"T": 1, "田": 1, "横杠竖条": 1, "Z": 1, "L": 1}
        return self.get_state()
    
    def generate_random_puzzle(self):
        """生成随机拼图"""
        self.reset()
        
        # 随机生成必需位置 (1-5个)
        num_required = random.randint(1, min(5, self.board_size * self.board_size // 3))
        positions = [(i, j) for i in range(self.board_size) for j in range(self.board_size)]
        required_pos = random.sample(positions, num_required)
        
        for r, c in required_pos:
            self.board[r, c] = 1
            self.required_positions.add((r, c))
        
        # 随机生成禁止位置 (0-3个)
        remaining_pos = [(i, j) for i in range(self.board_size) for j in range(self.board_size) 
                        if (i, j) not in required_pos]
        num_forbidden = random.randint(0, min(3, len(remaining_pos)))
        if num_forbidden > 0:
            forbidden_pos = random.sample(remaining_pos, num_forbidden)
            for r, c in forbidden_pos:
                self.board[r, c] = 2
                self.forbidden_positions.add((r, c))
        
        # 随机设置方块数量
        for piece in self.pieces_left:
            self.pieces_left[piece] = random.randint(0, 2)
        
        # 确保至少有一个方块
        if sum(self.pieces_left.values()) == 0:
            piece = random.choice(list(self.pieces_left.keys()))
            self.pieces_left[piece] = 1
            
        return self.get_state()
    
    def get_state(self):
        """获取当前状态"""
        # 棋盘状态 (8x8, 填充到最大尺寸)
        board_state = np.zeros((8, 8), dtype=np.float32)
        board_state[:self.board_size, :self.board_size] = self.board
        
        # 方块状态
        piece_state = np.array([
            self.pieces_left["T"],
            self.pieces_left["田"], 
            self.pieces_left["横杠竖条"],
            self.pieces_left["Z"],
            self.pieces_left["L"]
        ], dtype=np.float32)
        
        return board_state, piece_state
    
    def is_valid_placement(self, piece_name, shape_idx, start_r, start_c):
        """检查放置是否有效"""
        if piece_name not in self.tetris_shapes:
            return False
        if shape_idx >= len(self.tetris_shapes[piece_name]):
            return False
        if self.pieces_left[piece_name] <= 0:
            return False
            
        shape = self.tetris_shapes[piece_name][shape_idx]
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if r < 0 or r >= self.board_size or c < 0 or c >= self.board_size:
                return False
            if self.board[r, c] > 2:  # 已被其他方块占用
                return False
            if (r, c) in self.forbidden_positions:
                return False
                
        return True
    
    def place_piece(self, piece_name, shape_idx, start_r, start_c):
        """放置方块"""
        if not self.is_valid_placement(piece_name, shape_idx, start_r, start_c):
            return False, -10  # 无效放置，负奖励
        
        shape = self.tetris_shapes[piece_name][shape_idx]
        piece_id = 3 + list(self.tetris_shapes.keys()).index(piece_name)
        
        # 计算奖励
        reward = 0
        covered_required = 0
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if (r, c) in self.required_positions:
                covered_required += 1
                reward += 20  # 覆盖必需位置高奖励
            else:
                reward += 1   # 普通位置小奖励
            self.board[r, c] = piece_id
        
        self.pieces_left[piece_name] -= 1
        
        # 检查是否完成拼图
        if self.is_solved():
            reward += 100  # 完成拼图大奖励
            
        return True, reward
    
    def is_solved(self):
        """检查是否解决"""
        # 所有必需位置都被覆盖
        for r, c in self.required_positions:
            if self.board[r, c] <= 2:
                return False
        return True
    
    def get_valid_actions(self):
        """获取所有有效动作"""
        actions = []
        for piece_name, count in self.pieces_left.items():
            if count > 0:
                for shape_idx in range(len(self.tetris_shapes[piece_name])):
                    for r in range(self.board_size):
                        for c in range(self.board_size):
                            if self.is_valid_placement(piece_name, shape_idx, r, c):
                                actions.append((piece_name, shape_idx, r, c))
        return actions

class DataGenerator:
    """训练数据生成器"""
    def __init__(self):
        self.env = PuzzleEnvironment()
    
    def generate_training_batch(self, batch_size=32, board_sizes=[3,4,5,6,7,8]):
        """生成训练批次"""
        boards = []
        pieces = []
        targets = []
        values = []
        
        for _ in range(batch_size):
            # 随机选择棋盘大小
            board_size = random.choice(board_sizes)
            self.env.board_size = board_size
            
            # 生成随机拼图
            board_state, piece_state = self.env.generate_random_puzzle()
            
            # 获取有效动作
            valid_actions = self.env.get_valid_actions()
            
            if valid_actions:
                # 随机选择一个动作作为目标
                action = random.choice(valid_actions)
                piece_name, shape_idx, r, c = action
                
                # 计算目标位置
                target_pos = r * 8 + c  # 转换为一维索引
                
                # 评估这个状态的价值
                value = self.evaluate_state()
                
                boards.append(board_state)
                pieces.append(piece_state)
                targets.append(target_pos)
                values.append(value)
        
        return (torch.FloatTensor(boards).unsqueeze(1),  # 添加通道维度
                torch.FloatTensor(pieces),
                torch.LongTensor(targets),
                torch.FloatTensor(values))
    
    def evaluate_state(self):
        """评估当前状态的价值"""
        # 简单的启发式评估
        covered_required = 0
        total_required = len(self.env.required_positions)
        
        for r, c in self.env.required_positions:
            if self.env.board[r, c] > 2:
                covered_required += 1
        
        if total_required == 0:
            return 0.0
        
        coverage_ratio = covered_required / total_required
        
        # 考虑剩余方块数量
        remaining_pieces = sum(self.env.pieces_left.values())
        piece_penalty = remaining_pieces * 0.1
        
        value = coverage_ratio - piece_penalty
        return np.clip(value, -1.0, 1.0)

class DeepPuzzleSolver:
    """深度学习拼图求解器"""
    def __init__(self, model_path="best_puzzle_model.pth"):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = PuzzleNet().to(self.device)
        self.load_model(model_path)

    def load_model(self, model_path):
        """加载训练好的模型"""
        try:
            checkpoint = torch.load(model_path, map_location=self.device)
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ 成功加载模型: {model_path}")
                print(f"📊 模型准确率: {checkpoint.get('accuracy', 0)*100:.1f}%")
            else:
                self.model.load_state_dict(checkpoint)
                print(f"✅ 成功加载模型: {model_path}")
            self.model.eval()
        except FileNotFoundError:
            print(f"❌ 模型文件不存在: {model_path}")
            print("💡 请先运行训练脚本")
        except Exception as e:
            print(f"❌ 加载模型失败: {e}")

    def solve(self, board, pieces, mode='rotating', max_steps=50, beam_width=3):
        """求解拼图"""
        # 设置环境
        env = PuzzleEnvironment(len(board))
        env.reset()

        # 复制棋盘状态
        for i in range(len(board)):
            for j in range(len(board[0])):
                env.board[i, j] = board[i][j]
                if board[i][j] == 1:
                    env.required_positions.add((i, j))
                elif board[i][j] == 2:
                    env.forbidden_positions.add((i, j))

        env.pieces_left = pieces.copy()

        # 使用束搜索求解
        solution_steps = []

        with torch.no_grad():
            for step in range(max_steps):
                if env.is_solved():
                    break

                # 获取当前状态
                board_state, piece_state = env.get_state()

                # 转换为tensor
                board_tensor = torch.FloatTensor(board_state).unsqueeze(0).unsqueeze(0).to(self.device)
                piece_tensor = torch.FloatTensor(piece_state).unsqueeze(0).to(self.device)

                # 预测
                position_scores, value = self.model(board_tensor, piece_tensor)
                position_probs = torch.softmax(position_scores, dim=1)

                # 获取有效动作
                valid_actions = env.get_valid_actions()
                if not valid_actions:
                    break

                # 计算每个动作的得分
                action_scores = []
                for action in valid_actions:
                    piece_name, shape_idx, r, c = action
                    pos_idx = r * 8 + c
                    score = position_probs[0, pos_idx].item()
                    action_scores.append((score, action))

                # 选择最佳动作
                action_scores.sort(reverse=True)
                best_score, best_action = action_scores[0]

                # 执行动作
                piece_name, shape_idx, r, c = best_action
                success, reward = env.place_piece(piece_name, shape_idx, r, c)

                if success:
                    solution_steps.append({
                        'step': step + 1,
                        'piece': piece_name,
                        'position': (r, c),
                        'rotation': shape_idx,
                        'confidence': best_score
                    })
                else:
                    break

        return solution_steps, env.board.tolist()

    def get_hint(self, board, pieces, mode='rotating'):
        """获取下一步提示"""
        solution_steps, _ = self.solve(board, pieces, mode, max_steps=1)

        if solution_steps:
            step = solution_steps[0]
            return {
                'success': True,
                'piece': step['piece'],
                'position': step['position'],
                'rotation': step['rotation'],
                'confidence': step['confidence'],
                'message': f"建议放置 {step['piece']} 到位置 {step['position']}"
            }
        else:
            return {
                'success': False,
                'message': '暂时无法找到有效的下一步',
                'confidence': 0.0
            }

    def batch_solve(self, puzzles):
        """批量求解拼图"""
        results = []

        for i, puzzle in enumerate(puzzles):
            start_time = time.time()

            solution_steps, final_board = self.solve(
                puzzle['board'],
                puzzle['pieces'],
                puzzle.get('mode', 'rotating')
            )

            solve_time = time.time() - start_time

            results.append({
                'index': i,
                'success': len(solution_steps) > 0,
                'steps': len(solution_steps),
                'time_ms': solve_time * 1000,
                'solution': solution_steps,
                'final_board': final_board
            })

        return results

def main():
    """测试深度学习求解器"""
    print("🧠 深度学习拼图求解器测试")
    print("=" * 50)

    # 创建求解器
    solver = DeepPuzzleSolver()

    # 测试用例
    test_cases = [
        {
            'name': '3x3 简单',
            'board': [
                [1, 0, 0],
                [0, 0, 0],
                [0, 0, 2]
            ],
            'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '4x4 中等',
            'board': [
                [1, 0, 0, 1],
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [2, 0, 0, 2]
            ],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '5x5 复杂',
            'board': [
                [0, 1, 0, 0, 0],
                [0, 0, 0, 1, 0],
                [2, 0, 0, 0, 0],
                [0, 0, 1, 0, 2],
                [0, 0, 0, 0, 0]
            ],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 1, "Z": 0, "L": 0}
        }
    ]

    success_count = 0
    total_time = 0

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")

        start_time = time.time()
        solution_steps, final_board = solver.solve(
            test_case['board'],
            test_case['pieces']
        )
        solve_time = time.time() - start_time
        total_time += solve_time

        if solution_steps:
            success_count += 1
            print(f"✅ 成功求解! {len(solution_steps)}步, {solve_time*1000:.1f}ms")

            for step in solution_steps:
                print(f"  步骤{step['step']}: {step['piece']} → {step['position']} "
                      f"(置信度: {step['confidence']:.3f})")
        else:
            print(f"❌ 求解失败, {solve_time*1000:.1f}ms")

    print(f"\n📊 总结:")
    print(f"成功率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    print(f"平均时间: {total_time/len(test_cases)*1000:.1f}ms")

if __name__ == "__main__":
    main()
