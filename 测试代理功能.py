#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试安徽联通话费领取工具的代理功能
"""

import requests
import urllib3
import json

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_proxy_settings():
    """测试代理设置"""
    print("🔍 测试代理功能...")
    print("=" * 50)
    
    # 测试配置
    proxy_configs = [
        {
            "name": "直连（无代理）",
            "use_proxy": False,
            "verify_ssl": True
        },
        {
            "name": "Fiddler代理（SSL验证关闭）",
            "use_proxy": True,
            "proxy_host": "127.0.0.1",
            "proxy_port": "8888",
            "verify_ssl": False
        },
        {
            "name": "Proxifier代理（SSL验证关闭）",
            "use_proxy": True,
            "proxy_host": "127.0.0.1",
            "proxy_port": "8080",
            "verify_ssl": False
        }
    ]
    
    for config in proxy_configs:
        print(f"\n📡 测试配置: {config['name']}")
        print("-" * 30)
        
        try:
            # 创建会话
            session = requests.Session()
            
            # 设置代理
            if config.get('use_proxy', False):
                proxy_url = f"http://{config['proxy_host']}:{config['proxy_port']}"
                session.proxies = {
                    'http': proxy_url,
                    'https': proxy_url
                }
                print(f"代理设置: {proxy_url}")
            else:
                print("代理设置: 无")
            
            # 设置SSL验证
            session.verify = config.get('verify_ssl', True)
            print(f"SSL验证: {'启用' if session.verify else '禁用'}")
            
            # 测试连接
            print("正在测试连接...")
            response = session.get("https://httpbin.org/ip", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                ip = data.get('origin', '未知')
                print(f"✅ 连接成功！当前IP: {ip}")
            else:
                print(f"❌ 连接失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 连接失败: {str(e)}")
            if config.get('use_proxy', False):
                print("💡 可能的原因:")
                print("   - 代理软件未启动")
                print("   - 端口号不正确")
                print("   - 防火墙阻止连接")

def test_target_api():
    """测试目标API"""
    print("\n\n🎯 测试目标API...")
    print("=" * 50)
    
    url = "https://huodong.10155.com/wo_activity/hy/t-biz-hy-right-receive-order/receiveRightHY"
    
    # 请求头
    headers = {
        "Host": "huodong.10155.com",
        "Connection": "keep-alive",
        "Content-Length": "119",
        "Accept": "application/json, text/plain, */*",
        "Origin": "https://huodong.10155.com",
        "accessToken": "Bearer 49e05361-d03e-4d00-b324-8ebe7cabe582",
        "Sec-Fetch-Dest": "empty",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/95.0.4638.69 Safari/537.36",
        "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
        "Cookie": "shadow_only_cookie_1017=b8d5f1a352c755da9cd2edc0321f80c2",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-Mode": "cors",
        "Referer": "https://huodong.10155.com/h5/hdact4/bojin20220509_lottery/",
        "Accept-Language": "zh-CN,zh;q=0.9"
    }
    
    # POST数据
    post_data = "productId=11269449&activityId=625&channel=bac27baa773c5bc2d3060edeb9881c64&rightsSkuId=95787a32c8bab4e76215ba66d25386d5"
    
    print(f"目标URL: {url}")
    print(f"请求方法: POST")
    print(f"数据长度: {len(post_data)} 字节")
    
    try:
        # 创建会话（可以根据需要启用代理）
        session = requests.Session()
        session.verify = False  # 禁用SSL验证以便抓包
        
        # 如果需要使用代理，取消下面的注释
        # session.proxies = {
        #     'http': 'http://127.0.0.1:8888',
        #     'https': 'http://127.0.0.1:8888'
        # }
        
        print("正在发送请求...")
        response = session.post(url, data=post_data, headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print(f"响应内容: {response.text}")
        
        # 检查是否包含true
        if "true" in response.text.lower():
            print("✅ 响应包含'true'，可能表示成功")
        else:
            print("❌ 响应不包含'true'")
            
    except Exception as e:
        print(f"❌ 请求失败: {str(e)}")

def show_fiddler_setup_guide():
    """显示Fiddler设置指南"""
    print("\n\n📖 Fiddler设置指南")
    print("=" * 50)
    print("1. 启动Fiddler Classic")
    print("2. 菜单 Tools -> Options -> HTTPS")
    print("3. 勾选 'Capture HTTPS CONNECTs'")
    print("4. 勾选 'Decrypt HTTPS traffic'")
    print("5. 点击 'Actions' -> 'Trust Root Certificate'")
    print("6. 菜单 Tools -> Options -> Connections")
    print("7. 确认端口为 8888")
    print("8. 勾选 'Allow remote computers to connect'")
    print("9. 重启Fiddler")
    print("\n💡 使用提示:")
    print("- 启用代理后，所有HTTPS请求都会被Fiddler拦截")
    print("- 可以在Fiddler中查看完整的请求和响应")
    print("- 可以修改请求内容进行调试")
    print("- 记得在调试完成后关闭代理")

if __name__ == "__main__":
    print("🚀 安徽联通话费领取工具 - 代理功能测试")
    print("=" * 60)
    
    # 测试代理设置
    test_proxy_settings()
    
    # 测试目标API
    test_target_api()
    
    # 显示设置指南
    show_fiddler_setup_guide()
    
    print("\n✨ 测试完成！")
    print("现在可以启动主程序并使用代理功能进行抓包调试了。")
