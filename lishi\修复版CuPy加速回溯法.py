import numpy as np
import time
from concurrent.futures import ThreadPoolExecutor

class FixedCuPyBacktrackSolver:
    """修复版CuPy加速回溯法求解器"""
    
    def __init__(self):
        self.gpu_available = False
        self.device_info = "CPU"
        self._init_gpu()
        
        # 俄罗斯方块定义
        self.tetris_shapes = {
            "T": [
                [(0,1), (1,0), (1,1), (1,2)],
                [(0,1), (1,1), (1,2), (2,1)],
                [(0,0), (1,0), (1,1), (2,0)],
                [(0,0), (0,1), (0,2), (1,1)],
                [(0,1), (1,0), (1,1), (2,1)]
            ],
            "田": [[(0,0), (0,1), (1,0), (1,1)]],
            "横杠竖条": [
                [(0,0), (0,1), (0,2), (0,3)],
                [(0,0), (1,0), (2,0), (3,0)]
            ],
            "Z": [
                [(0,0), (0,1), (1,1), (1,2)],
                [(0,1), (1,0), (1,1), (2,0)],
                [(0,1), (0,2), (1,0), (1,1)],
                [(0,0), (1,0), (1,1), (2,1)]
            ],
            "L": [
                [(0,0), (1,0), (2,0), (2,1)],
                [(0,1), (1,1), (2,1), (2,0)],
                [(0,0), (1,0), (1,1), (1,2)],
                [(0,0), (0,1), (1,0), (2,0)],
                [(0,0), (0,1), (0,2), (1,2)]
            ]
        }
    
    def _init_gpu(self):
        """安全初始化GPU"""
        try:
            import cupy as cp
            
            # 测试基本GPU操作
            test_array = cp.array([1, 2, 3])
            result = cp.sum(test_array)
            
            if result == 6:  # 验证计算正确
                self.gpu_available = True
                self.cp = cp
                
                # 获取设备信息
                try:
                    device = cp.cuda.Device()
                    self.device_info = f"GPU-{device.id}"
                    print(f"✅ GPU初始化成功: {self.device_info}")
                except:
                    self.device_info = "GPU-Unknown"
                    print("✅ GPU初始化成功: 设备信息未知")
                
                # 测试内存分配
                try:
                    test_large = cp.zeros((1000, 1000))
                    del test_large
                    print("✅ GPU内存分配测试通过")
                except Exception as e:
                    print(f"⚠️ GPU内存测试失败: {e}")
                    self.gpu_available = False
                    
            else:
                print("❌ GPU计算验证失败")
                self.gpu_available = False
                
        except ImportError:
            print("⚠️ CuPy未安装，使用CPU")
            self.gpu_available = False
        except Exception as e:
            print(f"⚠️ GPU初始化失败: {e}")
            self.gpu_available = False
        
        if not self.gpu_available:
            self.device_info = "CPU"
            print("🖥️ 将使用CPU计算")
    
    def solve(self, board, pieces, mode='rotating', max_parallel=500):
        """求解拼图"""
        print(f"🚀 启动{'GPU' if self.gpu_available else 'CPU'}加速回溯法求解")
        print(f"📊 棋盘大小: {len(board)}x{len(board[0])}")
        print(f"🧩 方块配置: {pieces}")
        print(f"🖥️ 计算设备: {self.device_info}")
        
        start_time = time.time()
        
        if self.gpu_available:
            try:
                solution_steps, final_board = self._gpu_solve(board, pieces, max_parallel)
            except Exception as e:
                print(f"⚠️ GPU求解失败，切换到CPU: {e}")
                solution_steps, final_board = self._cpu_solve(board, pieces)
        else:
            solution_steps, final_board = self._cpu_solve(board, pieces)
        
        solve_time = time.time() - start_time
        print(f"⚡ 求解完成: {solve_time:.3f}秒")
        
        return solution_steps, final_board
    
    def _gpu_solve(self, board, pieces, max_parallel):
        """GPU加速求解"""
        print("🎮 使用GPU加速计算")
        
        # 转换为CuPy数组
        board_gpu = self.cp.array(board, dtype=self.cp.int32)
        
        # 生成初始放置方案
        initial_placements = self._generate_initial_placements(board, pieces)
        
        if not initial_placements:
            return [], board
        
        print(f"🔍 生成初始方案: {len(initial_placements)}个")
        
        # 限制并行数量避免内存溢出
        batch_size = min(max_parallel, len(initial_placements))
        
        # 批量处理
        for i in range(0, len(initial_placements), batch_size):
            batch = initial_placements[i:i+batch_size]
            
            # GPU批量处理
            solutions = self._gpu_batch_process(board_gpu, pieces, batch)
            
            if solutions:
                best_solution = solutions[0]
                steps = self._generate_solution_steps(board, best_solution, pieces)
                return steps, best_solution.tolist()
        
        return [], board
    
    def _gpu_batch_process(self, board_gpu, pieces, batch_placements):
        """GPU批量处理"""
        batch_size = len(batch_placements)
        board_size = board_gpu.shape[0]
        
        # 创建批量棋盘
        batch_boards = self.cp.tile(board_gpu[None, :, :], (batch_size, 1, 1))
        
        # 应用初始放置
        for i, placement in enumerate(batch_placements):
            self._apply_placement_gpu(batch_boards[i], placement)
        
        # 转回CPU进行复杂逻辑处理
        batch_boards_cpu = self.cp.asnumpy(batch_boards)
        
        # 并行CPU处理
        solutions = []
        with ThreadPoolExecutor(max_workers=min(8, batch_size)) as executor:
            futures = []
            for i in range(batch_size):
                remaining_pieces = pieces.copy()
                piece_name = batch_placements[i]['piece']
                remaining_pieces[piece_name] -= 1
                
                future = executor.submit(
                    self._solve_single_cpu, 
                    batch_boards_cpu[i], 
                    remaining_pieces
                )
                futures.append(future)
            
            # 收集结果
            for future in futures:
                result = future.result()
                if result is not None:
                    solutions.append(result)
        
        return solutions
    
    def _apply_placement_gpu(self, board, placement):
        """在GPU上应用方块放置"""
        shape = placement['shape']
        start_r, start_c = placement['position']
        piece_id = placement['piece_id']
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            board[r, c] = piece_id
    
    def _cpu_solve(self, board, pieces):
        """CPU求解"""
        print("🖥️ 使用CPU计算")
        
        import copy
        board_copy = copy.deepcopy(board)
        pieces_copy = copy.deepcopy(pieces)
        
        solution = self._backtrack_cpu(board_copy, pieces_copy)
        
        if solution:
            steps = self._generate_solution_steps(board, solution, pieces)
            return steps, solution
        else:
            return [], board
    
    def _generate_initial_placements(self, board, pieces):
        """生成初始放置方案"""
        placements = []
        board_size = len(board)
        
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(pieces.keys()).index(piece_name) + 3
            
            for shape_idx, shape in enumerate(shapes):
                for r in range(board_size):
                    for c in range(board_size):
                        if self._can_place_cpu(board, shape, r, c):
                            placements.append({
                                'piece': piece_name,
                                'shape_idx': shape_idx,
                                'position': (r, c),
                                'piece_id': piece_id,
                                'shape': shape
                            })
        
        return placements[:500]  # 限制数量
    
    def _solve_single_cpu(self, board, remaining_pieces):
        """单个棋盘的CPU求解"""
        if self._is_solved(board):
            return board
        
        if sum(remaining_pieces.values()) == 0:
            return None
        
        return self._backtrack_cpu(board.copy(), remaining_pieces, depth=0)
    
    def _backtrack_cpu(self, board, pieces, depth=0):
        """CPU回溯求解"""
        if depth > 15:
            return None
            
        if self._is_solved(board):
            return board
        
        board_size = len(board)
        
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(pieces.keys()).index(piece_name) + 3
            
            for shape in shapes:
                for r in range(board_size):
                    for c in range(board_size):
                        if self._can_place_cpu(board, shape, r, c):
                            # 放置方块
                            new_board = board.copy() if hasattr(board, 'copy') else [row[:] for row in board]
                            for dr, dc in shape:
                                new_board[r + dr][c + dc] = piece_id
                            
                            # 更新方块数量
                            new_pieces = pieces.copy()
                            new_pieces[piece_name] -= 1
                            
                            # 递归求解
                            result = self._backtrack_cpu(new_board, new_pieces, depth + 1)
                            if result is not None:
                                return result
        
        return None
    
    def _can_place_cpu(self, board, shape, start_r, start_c):
        """检查是否可以放置方块"""
        board_size = len(board)
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if r < 0 or r >= board_size or c < 0 or c >= board_size:
                return False
            if board[r][c] > 2:
                return False
            if board[r][c] == 2:
                return False
        
        return True
    
    def _is_solved(self, board):
        """检查是否解决"""
        if hasattr(board, 'any'):  # numpy array
            return not board.any() if hasattr(board, 'any') else not any(1 in row for row in board)
        else:  # regular list
            return not any(1 in row for row in board)
    
    def _generate_solution_steps(self, original_board, solution_board, pieces):
        """生成解决方案步骤"""
        steps = []
        piece_names = list(pieces.keys())
        
        for i in range(len(solution_board)):
            for j in range(len(solution_board[0])):
                if solution_board[i][j] > 2 and original_board[i][j] <= 2:
                    piece_id = solution_board[i][j] - 3
                    if piece_id < len(piece_names):
                        steps.append({
                            'step': len(steps) + 1,
                            'piece': piece_names[piece_id],
                            'position': (i, j),
                            'rotation': 0
                        })
        
        return steps

def main():
    """测试修复版求解器"""
    print("🧪 测试修复版CuPy加速回溯法求解器")
    print("=" * 60)
    
    solver = FixedCuPyBacktrackSolver()
    
    # 测试用例
    test_cases = [
        {
            'name': '3x3 简单',
            'board': [
                [1, 0, 0],
                [0, 0, 0],
                [0, 0, 2]
            ],
            'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '4x4 中等',
            'board': [
                [1, 0, 0, 1],
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [2, 0, 0, 2]
            ],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        solution_steps, final_board = solver.solve(
            test_case['board'], 
            test_case['pieces']
        )
        total_time = time.time() - start_time
        
        if solution_steps:
            print(f"✅ 求解成功! {len(solution_steps)}步, 总时间: {total_time:.3f}秒")
            print("📋 解决方案:")
            for step in solution_steps:
                print(f"  步骤{step['step']}: {step['piece']} → {step['position']}")
        else:
            print(f"❌ 求解失败, 总时间: {total_time:.3f}秒")

if __name__ == "__main__":
    main()
