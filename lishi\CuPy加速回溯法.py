import cupy as cp
import numpy as np
import time
from concurrent.futures import ThreadPoolExecutor
import threading

class CuPyBacktrackSolver:
    """CuPy加速回溯法求解器 - 更易用的GPU加速方案"""
    
    def __init__(self):
        # 检查GPU
        try:
            import cupy as cp
            # 测试基本GPU操作
            test_array = cp.array([1, 2, 3])
            _ = cp.sum(test_array)

            self.device = cp.cuda.Device()
            print(f"🎮 使用GPU: {self.device}")

            # 安全获取显存信息
            try:
                meminfo = cp.cuda.MemoryInfo()
                print(f"📊 显存: {meminfo.total / 1024**3:.1f}GB")
            except:
                print("📊 显存: 信息获取失败，但GPU可用")

            self.gpu_available = True
            print("✅ CuPy GPU初始化成功")

        except ImportError:
            print("❌ CuPy未安装，将使用CPU")
            self.gpu_available = False
        except Exception as e:
            print(f"❌ GPU不可用: {e}")
            print("🖥️ 将使用CPU")
            self.gpu_available = False
        
        # 俄罗斯方块定义
        self.tetris_shapes = {
            "T": [
                [(0,1), (1,0), (1,1), (1,2)],
                [(0,1), (1,1), (1,2), (2,1)],
                [(0,0), (1,0), (1,1), (2,0)],
                [(0,0), (0,1), (0,2), (1,1)],
                [(0,1), (1,0), (1,1), (2,1)]
            ],
            "田": [[(0,0), (0,1), (1,0), (1,1)]],
            "横杠竖条": [
                [(0,0), (0,1), (0,2), (0,3)],
                [(0,0), (1,0), (2,0), (3,0)]
            ],
            "Z": [
                [(0,0), (0,1), (1,1), (1,2)],
                [(0,1), (1,0), (1,1), (2,0)],
                [(0,1), (0,2), (1,0), (1,1)],
                [(0,0), (1,0), (1,1), (2,1)]
            ],
            "L": [
                [(0,0), (1,0), (2,0), (2,1)],
                [(0,1), (1,1), (2,1), (2,0)],
                [(0,0), (1,0), (1,1), (1,2)],
                [(0,0), (0,1), (1,0), (2,0)],
                [(0,0), (0,1), (0,2), (1,2)]
            ]
        }
    
    def solve(self, board, pieces, mode='rotating', max_parallel=1000):
        """GPU加速并行回溯求解"""
        print(f"🚀 启动CuPy加速回溯法求解")
        print(f"📊 棋盘大小: {len(board)}x{len(board[0])}")
        print(f"🧩 方块配置: {pieces}")
        print(f"⚡ 并行度: {max_parallel}")
        
        if not self.gpu_available:
            return self._cpu_fallback(board, pieces)
        
        start_time = time.time()
        
        # 转换为CuPy数组
        board_gpu = cp.array(board, dtype=cp.int32)
        
        # 生成所有可能的初始放置
        initial_placements = self._generate_initial_placements(board, pieces)
        
        if not initial_placements:
            return [], board
        
        print(f"🔍 生成初始放置方案: {len(initial_placements)}个")
        
        # 批量GPU并行求解
        solutions = self._parallel_gpu_solve(board_gpu, pieces, initial_placements, max_parallel)
        
        solve_time = time.time() - start_time
        print(f"⚡ GPU求解完成: {solve_time:.3f}秒")
        
        if solutions:
            best_solution = solutions[0]
            steps = self._generate_solution_steps(board, best_solution, pieces)
            return steps, best_solution.tolist()
        else:
            return [], board
    
    def _generate_initial_placements(self, board, pieces):
        """生成初始放置方案"""
        placements = []
        board_size = len(board)
        
        # 为每种有数量的方块生成放置方案
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(pieces.keys()).index(piece_name) + 3
            
            for shape_idx, shape in enumerate(shapes):
                for r in range(board_size):
                    for c in range(board_size):
                        if self._can_place_cpu(board, shape, r, c):
                            placements.append({
                                'piece': piece_name,
                                'shape_idx': shape_idx,
                                'position': (r, c),
                                'piece_id': piece_id,
                                'shape': shape
                            })
        
        return placements[:1000]  # 限制数量避免内存溢出
    
    def _parallel_gpu_solve(self, board_gpu, pieces, initial_placements, max_parallel):
        """并行GPU求解"""
        batch_size = min(max_parallel, len(initial_placements))
        solutions = []
        
        # 分批处理
        for i in range(0, len(initial_placements), batch_size):
            batch = initial_placements[i:i+batch_size]
            batch_solutions = self._gpu_batch_solve(board_gpu, pieces, batch)
            solutions.extend(batch_solutions)
            
            if solutions:  # 找到解决方案就停止
                break
        
        return solutions
    
    def _gpu_batch_solve(self, board_gpu, pieces, batch_placements):
        """GPU批量求解"""
        batch_size = len(batch_placements)
        board_size = board_gpu.shape[0]
        
        # 创建批量棋盘
        batch_boards = cp.tile(board_gpu[None, :, :], (batch_size, 1, 1))
        
        # 应用初始放置
        for i, placement in enumerate(batch_placements):
            self._apply_placement_gpu(batch_boards[i], placement)
        
        # 并行求解每个棋盘
        solutions = []
        
        # 转回CPU进行复杂逻辑处理（CuPy在复杂递归上有限制）
        batch_boards_cpu = cp.asnumpy(batch_boards)
        
        # 使用线程池并行处理
        with ThreadPoolExecutor(max_workers=min(8, batch_size)) as executor:
            futures = []
            for i in range(batch_size):
                remaining_pieces = pieces.copy()
                piece_name = batch_placements[i]['piece']
                remaining_pieces[piece_name] -= 1
                
                future = executor.submit(
                    self._solve_single_cpu, 
                    batch_boards_cpu[i], 
                    remaining_pieces
                )
                futures.append(future)
            
            # 收集结果
            for future in futures:
                result = future.result()
                if result is not None:
                    solutions.append(result)
        
        return solutions
    
    def _apply_placement_gpu(self, board, placement):
        """在GPU上应用方块放置"""
        shape = placement['shape']
        start_r, start_c = placement['position']
        piece_id = placement['piece_id']
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            board[r, c] = piece_id
    
    def _solve_single_cpu(self, board, remaining_pieces):
        """单个棋盘的CPU求解"""
        if self._is_solved(board):
            return board
        
        # 检查是否还有方块
        if sum(remaining_pieces.values()) == 0:
            return None
        
        # 继续回溯求解
        return self._backtrack_cpu(board.copy(), remaining_pieces, depth=0)
    
    def _backtrack_cpu(self, board, pieces, depth=0):
        """CPU回溯求解"""
        if depth > 15:  # 限制递归深度
            return None
            
        if self._is_solved(board):
            return board
        
        board_size = len(board)
        
        # 尝试放置每种方块
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(pieces.keys()).index(piece_name) + 3
            
            for shape in shapes:
                for r in range(board_size):
                    for c in range(board_size):
                        if self._can_place_cpu(board, shape, r, c):
                            # 放置方块
                            new_board = board.copy()
                            for dr, dc in shape:
                                new_board[r + dr, c + dc] = piece_id
                            
                            # 更新方块数量
                            new_pieces = pieces.copy()
                            new_pieces[piece_name] -= 1
                            
                            # 递归求解
                            result = self._backtrack_cpu(new_board, new_pieces, depth + 1)
                            if result is not None:
                                return result
        
        return None
    
    def _can_place_cpu(self, board, shape, start_r, start_c):
        """检查是否可以放置方块"""
        board_size = len(board)
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if r < 0 or r >= board_size or c < 0 or c >= board_size:
                return False
            if board[r, c] > 2:  # 已被占用
                return False
            if board[r, c] == 2:  # 禁止位置
                return False
        
        return True
    
    def _is_solved(self, board):
        """检查是否解决"""
        return not cp.any(board == 1) if isinstance(board, cp.ndarray) else not np.any(board == 1)
    
    def _generate_solution_steps(self, original_board, solution_board, pieces):
        """生成解决方案步骤"""
        steps = []
        piece_names = list(pieces.keys())
        
        # 简化版本：识别放置的方块
        for i in range(len(solution_board)):
            for j in range(len(solution_board[0])):
                if solution_board[i][j] > 2 and original_board[i][j] <= 2:
                    piece_id = solution_board[i][j] - 3
                    if piece_id < len(piece_names):
                        steps.append({
                            'step': len(steps) + 1,
                            'piece': piece_names[piece_id],
                            'position': (i, j),
                            'rotation': 0
                        })
        
        return steps
    
    def _cpu_fallback(self, board, pieces):
        """CPU备用方案"""
        print("🖥️ 使用CPU备用求解")
        
        start_time = time.time()
        result = self._backtrack_cpu(np.array(board), pieces)
        solve_time = time.time() - start_time
        
        print(f"⏱️ CPU求解时间: {solve_time:.3f}秒")
        
        if result is not None:
            steps = self._generate_solution_steps(board, result, pieces)
            return steps, result.tolist()
        else:
            return [], board

def main():
    """测试CuPy加速回溯法"""
    print("🚀 CuPy加速回溯法拼图破解器")
    print("=" * 60)
    
    try:
        import cupy as cp
        print("✅ CuPy可用")
    except ImportError:
        print("❌ CuPy未安装，请运行: pip install cupy")
        return
    
    solver = CuPyBacktrackSolver()
    
    # 测试用例
    test_cases = [
        {
            'name': '3x3 简单',
            'board': [
                [1, 0, 0],
                [0, 0, 0],
                [0, 0, 2]
            ],
            'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '4x4 中等',
            'board': [
                [1, 0, 0, 1],
                [0, 0, 0, 0],
                [0, 0, 0, 0],
                [2, 0, 0, 2]
            ],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '5x5 复杂',
            'board': [
                [0, 1, 0, 0, 0],
                [0, 0, 0, 1, 0],
                [2, 0, 0, 0, 0],
                [0, 0, 1, 0, 2],
                [0, 0, 0, 0, 0]
            ],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 1, "Z": 0, "L": 0}
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 测试 {i}: {test_case['name']}")
        print("-" * 40)
        
        start_time = time.time()
        solution_steps, final_board = solver.solve(
            test_case['board'], 
            test_case['pieces']
        )
        total_time = time.time() - start_time
        
        if solution_steps:
            print(f"✅ 求解成功! {len(solution_steps)}步, 总时间: {total_time:.3f}秒")
            print("📋 解决方案:")
            for step in solution_steps:
                print(f"  步骤{step['step']}: {step['piece']} → {step['position']}")
        else:
            print(f"❌ 求解失败, 总时间: {total_time:.3f}秒")

if __name__ == "__main__":
    main()
