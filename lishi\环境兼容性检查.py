#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境兼容性检查和修复脚本
检查PyTorch版本兼容性并自动修复常见问题
"""

import sys
import torch
import subprocess
import pkg_resources

def check_pytorch_version():
    """检查PyTorch版本"""
    print("🔍 检查PyTorch版本兼容性")
    print("=" * 50)
    
    torch_version = torch.__version__
    print(f"📦 PyTorch版本: {torch_version}")
    
    # 解析版本号
    version_parts = torch_version.split('.')
    major = int(version_parts[0])
    minor = int(version_parts[1])
    
    print(f"🔢 版本号: {major}.{minor}")
    
    # 检查兼容性
    compatibility_issues = []
    
    # PyTorch 2.0+ 的变化
    if major >= 2:
        print("✅ PyTorch 2.0+ 检测到")
        print("💡 注意: 某些API可能有变化")
    elif major == 1 and minor >= 12:
        print("✅ PyTorch 1.12+ 兼容")
    else:
        compatibility_issues.append("PyTorch版本过低，建议升级到1.12+")
    
    # 检查CUDA兼容性
    if torch.cuda.is_available():
        cuda_version = torch.version.cuda
        print(f"🎮 CUDA版本: {cuda_version}")
        
        # 检查cuDNN
        if torch.backends.cudnn.is_available():
            print(f"✅ cuDNN可用: {torch.backends.cudnn.version()}")
        else:
            compatibility_issues.append("cuDNN不可用")
    else:
        print("⚠️ CUDA不可用，将使用CPU")
    
    return compatibility_issues

def check_scheduler_compatibility():
    """检查学习率调度器兼容性"""
    print("\n🔧 检查学习率调度器兼容性")
    print("=" * 50)
    
    try:
        # 测试ReduceLROnPlateau的verbose参数
        from torch.optim.lr_scheduler import ReduceLROnPlateau
        import torch.optim as optim
        
        # 创建一个简单的模型和优化器用于测试
        model = torch.nn.Linear(1, 1)
        optimizer = optim.Adam(model.parameters())
        
        # 测试verbose参数
        try:
            scheduler = ReduceLROnPlateau(optimizer, verbose=True)
            print("✅ ReduceLROnPlateau支持verbose参数")
            return True
        except TypeError as e:
            if "verbose" in str(e):
                print("❌ ReduceLROnPlateau不支持verbose参数")
                print("💡 需要移除verbose参数")
                return False
            else:
                raise e
                
    except Exception as e:
        print(f"❌ 学习率调度器测试失败: {e}")
        return False

def fix_scheduler_issue():
    """修复学习率调度器问题"""
    print("\n🔧 修复学习率调度器兼容性问题")
    print("=" * 50)
    
    files_to_fix = [
        "GPU深度学习训练.py",
        "深度学习拼图破解器.py"
    ]
    
    for filename in files_to_fix:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否包含verbose=True
            if "verbose=True" in content:
                print(f"🔧 修复文件: {filename}")
                
                # 替换verbose=True
                fixed_content = content.replace(
                    "ReduceLROnPlateau(\n            self.optimizer, mode='max', factor=0.8, patience=20, verbose=True)",
                    "ReduceLROnPlateau(\n            self.optimizer, mode='max', factor=0.8, patience=20)"
                )
                
                # 如果还有其他verbose=True的情况
                fixed_content = fixed_content.replace(", verbose=True", "")
                fixed_content = fixed_content.replace("verbose=True,", "")
                fixed_content = fixed_content.replace("verbose=True", "")
                
                # 写回文件
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                print(f"✅ {filename} 修复完成")
            else:
                print(f"✅ {filename} 无需修复")
                
        except FileNotFoundError:
            print(f"⚠️ 文件不存在: {filename}")
        except Exception as e:
            print(f"❌ 修复 {filename} 失败: {e}")

def check_other_dependencies():
    """检查其他依赖"""
    print("\n📦 检查其他依赖包")
    print("=" * 50)
    
    required_packages = {
        'numpy': '1.19.0',
        'matplotlib': '3.3.0',
        'flask': '2.0.0',
        'flask-cors': '3.0.0'
    }
    
    issues = []
    
    for package, min_version in required_packages.items():
        try:
            if package == 'flask-cors':
                import flask_cors
                current_version = flask_cors.__version__
            else:
                current_version = pkg_resources.get_distribution(package).version
            
            print(f"✅ {package}: {current_version}")
            
            # 简单版本比较
            if pkg_resources.parse_version(current_version) < pkg_resources.parse_version(min_version):
                issues.append(f"{package} 版本过低 (当前: {current_version}, 需要: {min_version}+)")
                
        except ImportError:
            issues.append(f"缺少包: {package}")
            print(f"❌ {package}: 未安装")
        except Exception as e:
            print(f"⚠️ {package}: 检查失败 ({e})")
    
    return issues

def install_missing_packages(packages):
    """安装缺失的包"""
    if not packages:
        return True
    
    print(f"\n📥 安装缺失的包")
    print("=" * 50)
    
    for package in packages:
        try:
            print(f"正在安装 {package}...")
            if package == 'flask-cors':
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'flask-cors'])
            else:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    return True

def create_compatibility_report():
    """创建兼容性报告"""
    print("\n📋 生成兼容性报告")
    print("=" * 50)
    
    report = f"""# 环境兼容性报告

## 系统信息
- Python版本: {sys.version}
- PyTorch版本: {torch.__version__}
- CUDA可用: {torch.cuda.is_available()}
"""
    
    if torch.cuda.is_available():
        report += f"- CUDA版本: {torch.version.cuda}\n"
        report += f"- GPU设备: {torch.cuda.get_device_name(0)}\n"
        report += f"- 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB\n"
    
    report += f"""
## 依赖包状态
"""
    
    packages = ['numpy', 'matplotlib', 'flask', 'flask-cors']
    for package in packages:
        try:
            if package == 'flask-cors':
                import flask_cors
                version = flask_cors.__version__
            else:
                version = pkg_resources.get_distribution(package).version
            report += f"- {package}: {version} ✅\n"
        except:
            report += f"- {package}: 未安装 ❌\n"
    
    report += f"""
## 兼容性检查结果
- 学习率调度器: {'兼容' if check_scheduler_compatibility() else '需要修复'}
- PyTorch版本: {'兼容' if len(check_pytorch_version()) == 0 else '有问题'}

## 建议
1. 如果遇到verbose参数错误，运行此脚本自动修复
2. 确保PyTorch版本 >= 1.12
3. 如果使用GPU，确保CUDA和cuDNN正确安装
"""
    
    with open('compatibility_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    
    print("📄 兼容性报告已保存: compatibility_report.md")

def main():
    """主函数"""
    print("🔍 环境兼容性检查和修复工具")
    print("=" * 60)
    
    # 1. 检查PyTorch版本
    pytorch_issues = check_pytorch_version()
    
    # 2. 检查学习率调度器
    scheduler_ok = check_scheduler_compatibility()
    
    # 3. 检查其他依赖
    dependency_issues = check_other_dependencies()
    
    # 4. 修复问题
    if not scheduler_ok:
        print("\n🔧 检测到学习率调度器兼容性问题，开始修复...")
        fix_scheduler_issue()
        print("✅ 学习率调度器问题修复完成")
    
    if dependency_issues:
        print(f"\n⚠️ 发现依赖问题: {len(dependency_issues)}个")
        for issue in dependency_issues:
            print(f"  - {issue}")
        
        choice = input("\n是否自动安装缺失的包? (y/n): ").lower().strip()
        if choice == 'y':
            missing_packages = [issue.split(':')[0].replace('缺少包: ', '') 
                              for issue in dependency_issues if '缺少包:' in issue]
            if missing_packages:
                install_missing_packages(missing_packages)
    
    # 5. 生成报告
    create_compatibility_report()
    
    # 6. 总结
    print(f"\n📋 兼容性检查完成")
    print("=" * 60)
    
    if not pytorch_issues and scheduler_ok and not dependency_issues:
        print("✅ 环境完全兼容，可以开始训练！")
    else:
        print("⚠️ 发现一些问题，但大部分已修复")
        print("💡 建议查看 compatibility_report.md 了解详情")
    
    print("\n🚀 现在可以运行训练脚本:")
    print("   python 一键深度学习训练.py")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 兼容性检查出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")
