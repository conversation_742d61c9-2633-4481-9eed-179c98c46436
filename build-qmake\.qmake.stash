QMAKE_CXX.QT_COMPILER_STDCXX = 201703L
QMAKE_CXX.QMAKE_GCC_MAJOR_VERSION = 15
QMAKE_CXX.QMAKE_GCC_MINOR_VERSION = 1
QMAKE_CXX.QMAKE_GCC_PATCH_VERSION = 0
QMAKE_CXX.COMPILER_MACROS = \
    QT_COMPILER_STDCXX \
    QMAKE_GCC_MAJOR_VERSION \
    QMAKE_GCC_MINOR_VERSION \
    QMAKE_GCC_PATCH_VERSION
QMAKE_CXX.INCDIRS = \
    C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++ \
    C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32 \
    C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward \
    C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include \
    C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed \
    C:/mingw64/x86_64-w64-mingw32/include
QMAKE_CXX.LIBDIRS = \
    C:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0 \
    C:/mingw64/lib/gcc \
    C:/mingw64/x86_64-w64-mingw32/lib \
    C:/mingw64/lib
