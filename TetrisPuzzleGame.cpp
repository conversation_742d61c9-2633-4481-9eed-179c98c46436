#include "TetrisPuzzleGame.h"
#include <QApplication>
#include <QMouseEvent>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QClipboard>
#include <QMessageBox>
#include <QTimer>
#include <QDateTime>
#include <QVariant>
#include <algorithm>
#include <random>
#include <sstream>

// Custom type registration is done in header file

// BoardCell implementation
BoardCell::BoardCell(int row, int col, QWidget* parent)
    : QPushButton(parent), m_row(row), m_col(col) {
    setFixedSize(35, 35);
    setStyleSheet("QPushButton { border: 1px solid #ddd; font-size: 10px; font-weight: bold; }");
}

void BoardCell::updateDisplay(int value, const std::map<std::string, QColor>& pieceColors) {
    setText("");
    setStyleSheet("QPushButton { border: 1px solid #ddd; font-size: 10px; font-weight: bold; }");
    
    if (value == 0) {
        // Empty cell - light blue
        setStyleSheet(styleSheet() + "background-color: lightblue;");
    } else if (value == 1) {
        // Required cell - red
        setStyleSheet(styleSheet() + "background-color: red; color: white;");
        setText("✓");
    } else if (value == 2) {
        // Forbidden cell - gray
        setStyleSheet(styleSheet() + "background-color: gray; color: white;");
        setText("✗");
    } else {
        // Piece cell
        std::vector<std::string> pieceNames = {"T", "田", "横杠竖条", "Z", "L"};
        int pieceIdx = value - 3;
        if (pieceIdx >= 0 && pieceIdx < pieceNames.size()) {
            std::string pieceName = pieceNames[pieceIdx];
            auto it = pieceColors.find(pieceName);
            if (it != pieceColors.end()) {
                QColor color = it->second;
                setStyleSheet(styleSheet() + QString("background-color: %1; color: white;").arg(color.name()));
                setText(QString::fromStdString(pieceName).left(1));
            }
        }
    }
}

void BoardCell::mousePressEvent(QMouseEvent* event) {
    if (event->button() == Qt::LeftButton) {
        emit leftClicked(m_row, m_col);
    } else if (event->button() == Qt::RightButton) {
        emit rightClicked(m_row, m_col);
    }
    QPushButton::mousePressEvent(event);
}

void BoardCell::mouseDoubleClickEvent(QMouseEvent* event) {
    emit doubleClicked(m_row, m_col);
    QPushButton::mouseDoubleClickEvent(event);
}

// SolverThread implementation
SolverThread::SolverThread(QObject* parent) : QThread(parent), m_stopRequested(false) {}

void SolverThread::setGameData(const std::vector<std::vector<int>>& board,
                              const std::map<std::string, int>& pieceCounts,
                              const std::map<std::string, TetrisShape>& shapes,
                              bool showProcess) {
    QMutexLocker locker(&m_mutex);
    m_originalBoard = board;
    m_pieceCounts = pieceCounts;
    m_tetrisShapes = shapes;
    m_showProcess = showProcess;
    m_boardSize = board.size();
    m_stopRequested = false;
}

void SolverThread::stopSolving() {
    QMutexLocker locker(&m_mutex);
    m_stopRequested = true;
}

void SolverThread::run() {
    std::vector<std::vector<int>> board = m_originalBoard;
    std::vector<std::string> pieces;
    std::map<std::string, int> usedCounts;

    // Prepare pieces list
    for (const auto& pair : m_pieceCounts) {
        for (int i = 0; i < pair.second; ++i) {
            pieces.push_back(pair.first);
        }
    }

    // Initialize used counts
    for (const auto& pair : m_pieceCounts) {
        usedCounts[pair.first] = 0;
    }

    // Debug output
    emit statusUpdate(QString("开始求解，方块数量: %1").arg(pieces.size()), 0, QString(""), QString("回溯法"));

    int step = 0;
    backtrackSolve(board, pieces, usedCounts, step);

    emit solvingFinished();
}

bool SolverThread::canPlacePiece(const std::vector<std::vector<int>>& board,
                                const std::vector<std::pair<int, int>>& shape,
                                int startRow, int startCol, int /*pieceId*/) {
    for (const auto& offset : shape) {
        int row = startRow + offset.first;
        int col = startCol + offset.second;
        
        if (row < 0 || row >= m_boardSize || col < 0 || col >= m_boardSize) {
            return false; // Out of bounds
        }
        
        if (board[row][col] == 2) {
            return false; // Forbidden cell
        }
        
        if (board[row][col] > 2) {
            return false; // Already occupied by another piece
        }
    }
    return true;
}

void SolverThread::placePiece(std::vector<std::vector<int>>& board,
                             const std::vector<std::pair<int, int>>& shape,
                             int startRow, int startCol, int pieceId) {
    for (const auto& offset : shape) {
        int row = startRow + offset.first;
        int col = startCol + offset.second;
        board[row][col] = pieceId;
    }
}

void SolverThread::removePiece(std::vector<std::vector<int>>& board,
                              const std::vector<std::pair<int, int>>& shape,
                              int startRow, int startCol) {
    for (const auto& offset : shape) {
        int row = startRow + offset.first;
        int col = startCol + offset.second;
        if (m_originalBoard[row][col] <= 2) {
            board[row][col] = m_originalBoard[row][col];
        } else {
            board[row][col] = 0;
        }
    }
}

void SolverThread::backtrackSolve(std::vector<std::vector<int>>& board,
                                 std::vector<std::string>& pieces,
                                 std::map<std::string, int>& usedCounts,
                                 int& step) {
    {
        QMutexLocker locker(&m_mutex);
        if (m_stopRequested) return;
    }
    
    step++;
    
    if (m_showProcess && step % 100 == 0) {
        emit statusUpdate(QString("搜索中..."), step, QString(""), QString("回溯法"));
    }
    
    // Check if all required cells are covered
    bool allRequiredCovered = true;
    for (int i = 0; i < m_boardSize && allRequiredCovered; ++i) {
        for (int j = 0; j < m_boardSize && allRequiredCovered; ++j) {
            if (m_originalBoard[i][j] == 1 && board[i][j] <= 2) {
                allRequiredCovered = false;
            }
        }
    }

    // If all required cells are covered, we found a solution
    if (allRequiredCovered) {
        emit solutionFound(board, "回溯法", step, usedCounts);
        // Don't return here, continue searching for more solutions
    }

    // If no more pieces to place, stop this branch
    if (pieces.empty()) {
        return;
    }
    
    // Try placing each remaining piece
    for (size_t pieceIdx = 0; pieceIdx < pieces.size(); ++pieceIdx) {
        {
            QMutexLocker locker(&m_mutex);
            if (m_stopRequested) return;
        }
        
        std::string pieceName = pieces[pieceIdx];
        auto shapeIt = m_tetrisShapes.find(pieceName);
        if (shapeIt == m_tetrisShapes.end()) continue;

        // Fixed piece ID mapping
        int pieceId = 3; // Default
        if (pieceName == "T") pieceId = 3;
        else if (pieceName == "田") pieceId = 4;
        else if (pieceName == "横杠竖条") pieceId = 5;
        else if (pieceName == "Z") pieceId = 6;
        else if (pieceName == "L") pieceId = 7;
        
        // Try each rotation of the piece
        for (const auto& rotation : shapeIt->second.rotations) {
            // Try each position on the board
            for (int row = 0; row < m_boardSize; ++row) {
                for (int col = 0; col < m_boardSize; ++col) {
                    if (canPlacePiece(board, rotation, row, col, pieceId)) {
                        // Place the piece
                        placePiece(board, rotation, row, col, pieceId);
                        usedCounts[pieceName]++;
                        
                        // Remove this piece from the list
                        std::vector<std::string> remainingPieces = pieces;
                        remainingPieces.erase(remainingPieces.begin() + pieceIdx);
                        
                        // Recurse
                        backtrackSolve(board, remainingPieces, usedCounts, step);
                        
                        // Backtrack
                        removePiece(board, rotation, row, col);
                        usedCounts[pieceName]--;
                    }
                }
            }
        }
    }
}

std::string SolverThread::generateBoardHash(const std::vector<std::vector<int>>& board) {
    std::stringstream ss;
    for (const auto& row : board) {
        for (int cell : row) {
            ss << cell << ",";
        }
    }
    return ss.str();
}

// TetrisPuzzleGame implementation
TetrisPuzzleGame::TetrisPuzzleGame(QWidget* parent)
    : QMainWindow(parent), m_boardSize(7), m_solving(false), m_showProcess(false),
      m_currentAlgorithm("回溯法"), m_duplicatesFound(0), m_solverThread(nullptr) {

    // Register custom types for QVariant
    qRegisterMetaType<std::vector<std::vector<int>>>("std::vector<std::vector<int>>");
    
    // Initialize tetris shapes
    m_tetrisShapes["T"] = {{{
        {{0,1}, {1,0}, {1,1}, {1,2}},  // T shape - up
        {{0,1}, {1,1}, {1,2}, {2,1}},  // T shape - right
        {{0,0}, {1,0}, {1,1}, {2,0}},  // T shape - right
        {{0,0}, {0,1}, {0,2}, {1,1}},  // T shape - down
        {{0,1}, {1,0}, {1,1}, {2,1}}   // T shape - left
    }}};
    
    m_tetrisShapes["田"] = {{{
        {{0,0}, {0,1}, {1,0}, {1,1}}   // O shape (square)
    }}};
    
    m_tetrisShapes["横杠竖条"] = {{{
        {{0,0}, {0,1}, {0,2}, {0,3}},  // I shape - horizontal
        {{0,0}, {1,0}, {2,0}, {3,0}}   // I shape - vertical
    }}};
    
    m_tetrisShapes["Z"] = {{{
        {{0,0}, {0,1}, {1,1}, {1,2}},  // Z shape - 0 degrees
        {{0,1}, {1,0}, {1,1}, {2,0}},  // Z shape - 90 degrees
        {{0,1}, {0,2}, {1,0}, {1,1}},  // S shape - 0 degrees
        {{0,0}, {1,0}, {1,1}, {2,1}}   // S shape - 90 degrees
    }}};
    
    m_tetrisShapes["L"] = {{{
        {{0,0}, {1,0}, {2,0}, {2,1}},  // L shape - 0 degrees
        {{0,1}, {1,1}, {2,1}, {2,0}},  // J shape - 0 degrees
        {{0,0}, {1,0}, {1,1}, {1,2}},  // J shape - 90 degrees
        {{0,0}, {0,1}, {1,0}, {2,0}},  // J shape - 180 degrees
        {{0,0}, {0,1}, {0,2}, {1,2}}   // J shape - 270 degrees
    }}};
    
    // Initialize piece counts
    m_pieceCounts["T"] = 1;
    m_pieceCounts["田"] = 1;
    m_pieceCounts["横杠竖条"] = 1;
    m_pieceCounts["Z"] = 1;
    m_pieceCounts["L"] = 1;
    
    // Initialize piece colors
    m_pieceColors["T"] = QColor("purple");
    m_pieceColors["田"] = QColor("orange");
    m_pieceColors["横杠竖条"] = QColor("cyan");
    m_pieceColors["Z"] = QColor("red");
    m_pieceColors["L"] = QColor("blue");
    
    initBoard();
    setupUI();
    createBoard();
}

TetrisPuzzleGame::~TetrisPuzzleGame() {
    if (m_solverThread) {
        m_solverThread->stopSolving();
        m_solverThread->wait();
        delete m_solverThread;
    }
}

void TetrisPuzzleGame::initBoard() {
    m_board = std::vector<std::vector<int>>(m_boardSize, std::vector<int>(m_boardSize, 0));
    m_solutionBoard = std::vector<std::vector<int>>(m_boardSize, std::vector<int>(m_boardSize, 0));
    m_cellCoverage = std::vector<std::vector<int>>(m_boardSize, std::vector<int>(m_boardSize, 0));
}

void TetrisPuzzleGame::setupUI() {
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);

    m_mainLayout = new QHBoxLayout(m_centralWidget);

    setupFloatingControls();
    setupControlPanel();
    setupMainGameArea();
    setupSolutionsArea();

    setWindowTitle("随机旋转 -概率版");
    setMinimumSize(1400, 800);
}

void TetrisPuzzleGame::setupFloatingControls() {
    m_floatingControls = new QWidget;
    m_floatingControls->setFixedWidth(280);
    m_floatingControls->setStyleSheet(
        "QWidget { background-color: white; border-radius: 8px; padding: 15px; }"
        "QLabel { font-size: 12px; font-weight: bold; }"
        "QComboBox, QPushButton { font-size: 10px; padding: 2px; }"
    );

    QVBoxLayout* layout = new QVBoxLayout(m_floatingControls);

    // Board size selection
    QLabel* boardSizeLabel = new QLabel("棋盘大小:");
    m_boardSizeCombo = new QComboBox;
    m_boardSizeCombo->addItems({"3x3", "4x4", "5x5", "6x6", "7x7", "8x8", "9x9"});
    m_boardSizeCombo->setCurrentText("7x7");
    connect(m_boardSizeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &TetrisPuzzleGame::onBoardSizeChanged);

    layout->addWidget(boardSizeLabel);
    layout->addWidget(m_boardSizeCombo);

    // Piece count controls
    QLabel* pieceCountLabel = new QLabel("方块数量设置:");
    layout->addWidget(pieceCountLabel);

    std::vector<std::string> pieceNames = {"T", "田", "横杠竖条", "Z", "L"};
    for (const std::string& pieceName : pieceNames) {
        QLabel* label = new QLabel(QString::fromStdString(pieceName) + ":");
        layout->addWidget(label);

        QHBoxLayout* buttonLayout = new QHBoxLayout;
        std::vector<QPushButton*> buttons;

        for (int count = 0; count <= 2; ++count) {
            QPushButton* btn = new QPushButton(QString::number(count));
            btn->setFixedSize(30, 20);
            btn->setCheckable(true);
            btn->setProperty("piece", QString::fromStdString(pieceName));
            btn->setProperty("count", count);

            if (count == 1) {
                btn->setChecked(true);
                btn->setStyleSheet("QPushButton:checked { background-color: #4CAF50; color: white; }");
            }

            connect(btn, &QPushButton::clicked, this, &TetrisPuzzleGame::onPieceCountChanged);
            buttons.push_back(btn);
            buttonLayout->addWidget(btn);
        }

        m_pieceCountButtons[pieceName] = buttons;
        layout->addLayout(buttonLayout);
    }

    layout->addStretch();
    m_mainLayout->addWidget(m_floatingControls);
}

void TetrisPuzzleGame::setupControlPanel() {
    m_controlPanel = new QWidget;
    m_controlPanel->setFixedWidth(280);
    m_controlPanel->setStyleSheet(
        "QWidget { background-color: white; border-radius: 8px; padding: 15px; }"
        "QLabel { font-size: 12px; }"
        "QPushButton { font-size: 10px; padding: 6px; margin: 2px; }"
        "QTextEdit { font-size: 9px; font-family: monospace; }"
    );

    QVBoxLayout* layout = new QVBoxLayout(m_controlPanel);

    // Title
    QLabel* titleLabel = new QLabel("随机旋转 -概率版");
    titleLabel->setStyleSheet("font-size: 14px; font-weight: bold;");
    layout->addWidget(titleLabel);

    // Show process checkbox
    m_showProcessCheck = new QCheckBox("显示破解过程");
    connect(m_showProcessCheck, &QCheckBox::toggled, this, &TetrisPuzzleGame::onShowProcessToggled);
    layout->addWidget(m_showProcessCheck);

    // Basic operations
    QLabel* basicOpsLabel = new QLabel("基本操作:");
    basicOpsLabel->setStyleSheet("font-weight: bold; margin-top: 10px;");
    layout->addWidget(basicOpsLabel);

    QHBoxLayout* basicOpsLayout = new QHBoxLayout;
    QPushButton* startBtn = new QPushButton("开始标注");
    QPushButton* clearBtn = new QPushButton("清除标记");
    startBtn->setStyleSheet("background-color: lightgreen;");
    clearBtn->setStyleSheet("background-color: orange;");
    connect(startBtn, &QPushButton::clicked, this, &TetrisPuzzleGame::startSolving);
    connect(clearBtn, &QPushButton::clicked, this, &TetrisPuzzleGame::clearAllMarks);
    basicOpsLayout->addWidget(startBtn);
    basicOpsLayout->addWidget(clearBtn);
    layout->addLayout(basicOpsLayout);

    // Import/Export
    QLabel* importLabel = new QLabel("导入棋盘:");
    importLabel->setStyleSheet("font-weight: bold; margin-top: 10px;");
    layout->addWidget(importLabel);

    m_importTextEdit = new QTextEdit;
    m_importTextEdit->setFixedHeight(80);
    m_importTextEdit->setPlaceholderText("在此粘贴棋盘数据...");
    layout->addWidget(m_importTextEdit);

    QHBoxLayout* importLayout = new QHBoxLayout;
    QPushButton* importBtn = new QPushButton("导入棋盘");
    QPushButton* exportBtn = new QPushButton("复制棋盘");
    importBtn->setStyleSheet("background-color: yellow;");
    exportBtn->setStyleSheet("background-color: pink;");
    connect(importBtn, &QPushButton::clicked, this, &TetrisPuzzleGame::importBoard);
    connect(exportBtn, &QPushButton::clicked, this, &TetrisPuzzleGame::exportBoard);
    importLayout->addWidget(importBtn);
    importLayout->addWidget(exportBtn);
    layout->addLayout(importLayout);

    // Solving algorithms
    QLabel* algoLabel = new QLabel("破解算法:");
    algoLabel->setStyleSheet("font-weight: bold; margin-top: 10px;");
    layout->addWidget(algoLabel);

    QHBoxLayout* algoLayout = new QHBoxLayout;
    QPushButton* backtrackBtn = new QPushButton("回溯法");
    QPushButton* stopBtn = new QPushButton("停止破解");
    backtrackBtn->setStyleSheet("background-color: lightblue;");
    stopBtn->setStyleSheet("background-color: lightcoral;");
    connect(backtrackBtn, &QPushButton::clicked, this, &TetrisPuzzleGame::startSolving);
    connect(stopBtn, &QPushButton::clicked, this, &TetrisPuzzleGame::stopSolving);
    algoLayout->addWidget(backtrackBtn);
    algoLayout->addWidget(stopBtn);
    layout->addLayout(algoLayout);

    // Status panel
    QLabel* statusLabel = new QLabel("破解状态:");
    statusLabel->setStyleSheet("font-weight: bold; margin-top: 10px;");
    layout->addWidget(statusLabel);

    m_statusMain = new QLabel("等待中...");
    m_statusMain->setStyleSheet("background-color: lightyellow; padding: 5px; border-radius: 3px; text-align: center;");
    layout->addWidget(m_statusMain);

    m_statusSteps = new QLabel("步数: 0");
    m_statusSteps->setStyleSheet("background-color: lightgray; padding: 5px; border-radius: 3px; text-align: center;");
    layout->addWidget(m_statusSteps);

    m_statusPiece = new QLabel("当前方块: 无");
    m_statusPiece->setStyleSheet("background-color: lightsteelblue; padding: 5px; border-radius: 3px; text-align: center;");
    layout->addWidget(m_statusPiece);

    m_statusAlgorithm = new QLabel("算法: 未选择");
    m_statusAlgorithm->setStyleSheet("background-color: lavender; padding: 5px; border-radius: 3px; text-align: center;");
    layout->addWidget(m_statusAlgorithm);

    // Progress bar
    m_progressBar = new QProgressBar;
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);
    layout->addWidget(m_progressBar);

    m_progressText = new QLabel("等待开始...");
    m_progressText->setStyleSheet("font-size: 10px; text-align: center;");
    layout->addWidget(m_progressText);

    // Info text
    QLabel* infoLabel = new QLabel("左键: 必需位置\n右键: 禁止位置\n双击: 清除");
    infoLabel->setStyleSheet("background-color: lightyellow; padding: 8px; border-radius: 4px; font-size: 8px;");
    layout->addWidget(infoLabel);

    layout->addStretch();
    m_mainLayout->addWidget(m_controlPanel);
}

void TetrisPuzzleGame::setupMainGameArea() {
    m_mainGameArea = new QWidget;
    m_mainGameArea->setFixedWidth(420);
    m_mainGameArea->setStyleSheet(
        "QWidget { background-color: white; border-radius: 8px; padding: 20px; }"
        "QLabel { font-size: 14px; text-align: center; }"
    );

    QVBoxLayout* layout = new QVBoxLayout(m_mainGameArea);

    // Title
    QLabel* titleLabel = new QLabel("随机旋转 -概率版");
    titleLabel->setStyleSheet("font-size: 18px; font-weight: bold;");
    layout->addWidget(titleLabel);

    // Mode indicator
    m_modeIndicator = new QLabel("模式: 显示破解过程");
    m_modeIndicator->setStyleSheet(
        "background-color: #e3f2fd; padding: 5px; border-radius: 4px; "
        "font-size: 12px; color: blue; margin: 10px 0;"
    );
    layout->addWidget(m_modeIndicator);

    // Game board
    m_gameBoardWidget = new QWidget;
    m_gameBoardWidget->setStyleSheet("border: 2px solid #333; background-color: #f9f9f9;");
    m_boardLayout = new QGridLayout(m_gameBoardWidget);
    m_boardLayout->setSpacing(0);
    m_boardLayout->setContentsMargins(0, 0, 0, 0);

    layout->addWidget(m_gameBoardWidget, 0, Qt::AlignCenter);
    layout->addStretch();

    m_mainLayout->addWidget(m_mainGameArea);
}

void TetrisPuzzleGame::setupSolutionsArea() {
    m_solutionsArea = new QWidget;
    m_solutionsArea->setStyleSheet(
        "QWidget { background-color: white; border-radius: 8px; padding: 15px; }"
        "QLabel { font-size: 12px; }"
    );

    QVBoxLayout* layout = new QVBoxLayout(m_solutionsArea);

    // Statistics panel
    QGroupBox* statsGroup = new QGroupBox("📊 解决方案统计");
    statsGroup->setStyleSheet(
        "QGroupBox { font-size: 14px; font-weight: bold; border: 2px solid #dee2e6; "
        "border-radius: 8px; padding: 10px; margin: 5px; }"
        "QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; }"
    );

    QGridLayout* statsLayout = new QGridLayout(statsGroup);

    // Statistics labels
    QLabel* totalLabel = new QLabel("原始方案数:");
    m_totalSolutionsLabel = new QLabel("0");
    m_totalSolutionsLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #007bff;");

    QLabel* validLabel = new QLabel("完整解决方案:");
    m_validSolutionsLabel = new QLabel("0");
    m_validSolutionsLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #007bff;");

    QLabel* duplicatesLabel = new QLabel("去重方案数:");
    m_duplicatesRemovedLabel = new QLabel("0");
    m_duplicatesRemovedLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #007bff;");

    QLabel* coverageLabel = new QLabel("平均覆盖率:");
    m_averageCoverageLabel = new QLabel("0%");
    m_averageCoverageLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: #007bff;");

    statsLayout->addWidget(totalLabel, 0, 0);
    statsLayout->addWidget(m_totalSolutionsLabel, 0, 1);
    statsLayout->addWidget(validLabel, 0, 2);
    statsLayout->addWidget(m_validSolutionsLabel, 0, 3);
    statsLayout->addWidget(duplicatesLabel, 1, 0);
    statsLayout->addWidget(m_duplicatesRemovedLabel, 1, 1);
    statsLayout->addWidget(coverageLabel, 1, 2);
    statsLayout->addWidget(m_averageCoverageLabel, 1, 3);

    layout->addWidget(statsGroup);

    // Probability heatmap
    QGroupBox* heatmapGroup = new QGroupBox("🔥 位置填充概率热力图");
    heatmapGroup->setStyleSheet(statsGroup->styleSheet());
    QVBoxLayout* heatmapLayout = new QVBoxLayout(heatmapGroup);

    m_probabilityHeatmap = new QWidget;
    heatmapLayout->addWidget(m_probabilityHeatmap, 0, Qt::AlignCenter);
    layout->addWidget(heatmapGroup);

    // Solutions display
    QGroupBox* solutionsGroup = new QGroupBox("✅ 完整解决方案展示");
    solutionsGroup->setStyleSheet(statsGroup->styleSheet());
    QVBoxLayout* solutionsLayout = new QVBoxLayout(solutionsGroup);

    m_solutionsStats = new QLabel("找到: 0 个完整解决方案");
    m_solutionsStats->setStyleSheet("font-size: 11px; color: #666; margin-bottom: 10px;");
    solutionsLayout->addWidget(m_solutionsStats);

    m_solutionsScrollArea = new QScrollArea;
    m_solutionsScrollArea->setWidgetResizable(true);
    m_solutionsScrollArea->setMaximumHeight(400);

    m_solutionsGrid = new QWidget;
    m_solutionsScrollArea->setWidget(m_solutionsGrid);
    solutionsLayout->addWidget(m_solutionsScrollArea);

    layout->addWidget(solutionsGroup);
    layout->addStretch();

    m_mainLayout->addWidget(m_solutionsArea);
}

void TetrisPuzzleGame::createBoard() {
    // Clear existing board
    if (m_boardCells.size() > 0) {
        for (auto& row : m_boardCells) {
            for (auto* cell : row) {
                m_boardLayout->removeWidget(cell);
                delete cell;
            }
        }
    }

    m_boardCells.clear();
    m_boardCells.resize(m_boardSize);

    for (int i = 0; i < m_boardSize; ++i) {
        m_boardCells[i].resize(m_boardSize);
        for (int j = 0; j < m_boardSize; ++j) {
            BoardCell* cell = new BoardCell(i, j);
            connect(cell, &BoardCell::leftClicked, [this](int row, int col) {
                onCellClicked(row, col, 1);
            });
            connect(cell, &BoardCell::rightClicked, [this](int row, int col) {
                onCellClicked(row, col, 2);
            });
            connect(cell, &BoardCell::doubleClicked, [this](int row, int col) {
                onCellClicked(row, col, 3);
            });

            m_boardCells[i][j] = cell;
            m_boardLayout->addWidget(cell, i, j);
        }
    }

    updateBoardDisplay();
}

void TetrisPuzzleGame::onBoardSizeChanged() {
    QString sizeText = m_boardSizeCombo->currentText();
    m_boardSize = sizeText.left(1).toInt();
    initBoard();
    createBoard();
    clearSolutions();
}

void TetrisPuzzleGame::onShowProcessToggled() {
    m_showProcess = m_showProcessCheck->isChecked();
    if (m_showProcess) {
        m_modeIndicator->setText("模式: 显示破解过程");
        m_modeIndicator->setStyleSheet(m_modeIndicator->styleSheet() + "color: blue;");
    } else {
        m_modeIndicator->setText("模式: 快速破解");
        m_modeIndicator->setStyleSheet(m_modeIndicator->styleSheet() + "color: red;");
    }
}

void TetrisPuzzleGame::onPieceCountChanged() {
    QPushButton* button = qobject_cast<QPushButton*>(sender());
    if (!button) return;

    QString pieceName = button->property("piece").toString();
    int count = button->property("count").toInt();

    // Uncheck other buttons in the same group
    auto& buttons = m_pieceCountButtons[pieceName.toStdString()];
    for (auto* btn : buttons) {
        btn->setChecked(false);
        btn->setStyleSheet("");
    }

    // Check the clicked button
    button->setChecked(true);
    button->setStyleSheet("QPushButton:checked { background-color: #4CAF50; color: white; }");

    // Update piece count
    m_pieceCounts[pieceName.toStdString()] = count;
}

void TetrisPuzzleGame::onCellClicked(int row, int col, int button) {
    if (m_solving) return;

    if (button == 1) { // Left click - required
        m_board[row][col] = 1;
    } else if (button == 2) { // Right click - forbidden
        m_board[row][col] = 2;
    } else if (button == 3) { // Double click - clear
        m_board[row][col] = 0;
    }

    updateCellDisplay(row, col);
}

void TetrisPuzzleGame::updateCellDisplay(int row, int col) {
    if (row >= 0 && row < m_boardSize && col >= 0 && col < m_boardSize) {
        m_boardCells[row][col]->updateDisplay(m_board[row][col], m_pieceColors);
    }
}

void TetrisPuzzleGame::updateBoardDisplay() {
    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            updateCellDisplay(i, j);
        }
    }
}

void TetrisPuzzleGame::startSolving() {
    if (m_solving) return;

    // Check if there are any required positions
    bool hasRequiredPositions = false;
    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            if (m_board[i][j] == 1) {
                hasRequiredPositions = true;
                break;
            }
        }
        if (hasRequiredPositions) break;
    }

    if (!hasRequiredPositions) {
        m_statusMain->setText("请先标记一些必需位置（左键点击）");
        m_statusMain->setStyleSheet("background-color: orange; padding: 5px; border-radius: 3px;");
        return;
    }

    // Check if there are any pieces to use
    bool hasPieces = false;
    for (const auto& pair : m_pieceCounts) {
        if (pair.second > 0) {
            hasPieces = true;
            break;
        }
    }

    if (!hasPieces) {
        m_statusMain->setText("请设置至少一个方块数量大于0");
        m_statusMain->setStyleSheet("background-color: orange; padding: 5px; border-radius: 3px;");
        return;
    }

    m_solving = true;
    clearSolutions();

    m_statusMain->setText("开始破解...");
    m_statusMain->setStyleSheet("background-color: lightgreen; padding: 5px; border-radius: 3px;");
    m_statusAlgorithm->setText("算法: 回溯法");

    // Create and start solver thread
    if (m_solverThread) {
        delete m_solverThread;
    }

    m_solverThread = new SolverThread(this);
    connect(m_solverThread, &SolverThread::solutionFound,
            this, &TetrisPuzzleGame::onSolutionFound);
    connect(m_solverThread, &SolverThread::statusUpdate,
            this, &TetrisPuzzleGame::onStatusUpdate);
    connect(m_solverThread, &SolverThread::solvingFinished,
            this, &TetrisPuzzleGame::onSolvingFinished);

    m_solverThread->setGameData(m_board, m_pieceCounts, m_tetrisShapes, m_showProcess);
    m_solverThread->start();
}

void TetrisPuzzleGame::stopSolving() {
    if (!m_solving || !m_solverThread) return;

    m_solverThread->stopSolving();
    m_solverThread->wait();

    m_solving = false;
    m_statusMain->setText("已停止破解");
    m_statusMain->setStyleSheet("background-color: lightcoral; padding: 5px; border-radius: 3px;");

    // Process any solutions found so far
    if (!m_rawSolutions.empty()) {
        processAllSolutions();
    }
}

void TetrisPuzzleGame::clearAllMarks() {
    if (m_solving) return;

    initBoard();
    updateBoardDisplay();
    m_statusMain->setText("已清除所有标记");
    m_statusPiece->setText("当前方块: 无");
    m_statusAlgorithm->setText("算法: 未选择");
}

void TetrisPuzzleGame::clearSolutions() {
    m_rawSolutions.clear();
    m_uniqueSolutions.clear();
    m_completeSolutions.clear();
    m_duplicatesFound = 0;
    m_cellCoverage = std::vector<std::vector<int>>(m_boardSize, std::vector<int>(m_boardSize, 0));

    // Clear solutions display
    if (m_solutionsGrid->layout()) {
        QLayoutItem* item;
        while ((item = m_solutionsGrid->layout()->takeAt(0)) != nullptr) {
            delete item->widget();
            delete item;
        }
        delete m_solutionsGrid->layout();
    }

    m_solutionsStats->setText("找到: 0 个完整解决方案");
    updateStatistics();
    updateProgress(0, "等待开始...");
}

void TetrisPuzzleGame::onSolutionFound(const std::vector<std::vector<int>>& board,
                                      const std::string& algorithm, int step,
                                      const std::map<std::string, int>& piecesUsed) {
    addRawSolution(board, algorithm, step, piecesUsed);
}

void TetrisPuzzleGame::onStatusUpdate(const QString& status, int steps,
                                     const QString& piece, const QString& algorithm) {
    m_statusMain->setText(status);
    m_statusSteps->setText(QString("步数: %1").arg(steps));
    if (!piece.isEmpty()) {
        m_statusPiece->setText(QString("当前方块: %1").arg(piece));
    }
    if (!algorithm.isEmpty()) {
        m_statusAlgorithm->setText(QString("算法: %1").arg(algorithm));
    }
}

void TetrisPuzzleGame::onSolvingFinished() {
    m_solving = false;
    processAllSolutions();
}

void TetrisPuzzleGame::addRawSolution(const std::vector<std::vector<int>>& board,
                                     const std::string& algorithm, int step,
                                     const std::map<std::string, int>& piecesUsed) {
    Solution solution;
    solution.board = board;
    solution.algorithm = algorithm;
    solution.step = step;
    solution.timestamp = QDateTime::currentMSecsSinceEpoch();
    solution.piecesUsed = piecesUsed;
    solution.hash = generateBoardHash(board);

    // Evaluate solution
    Solution eval = evaluateSolution(board);
    solution.score = eval.score;
    solution.filledCells = eval.filledCells;
    solution.requiredCovered = eval.requiredCovered;
    solution.forbiddenViolated = eval.forbiddenViolated;
    solution.isValid = eval.isValid;
    solution.isComplete = isCompleteSolution(board, piecesUsed);

    m_rawSolutions.push_back(solution);
}

std::string TetrisPuzzleGame::generateBoardHash(const std::vector<std::vector<int>>& board) {
    std::stringstream ss;
    for (const auto& row : board) {
        for (int cell : row) {
            ss << cell << ",";
        }
    }
    return ss.str();
}

bool TetrisPuzzleGame::isCompleteSolution(const std::vector<std::vector<int>>& board,
                                         const std::map<std::string, int>& piecesUsed) {
    // Check if all required cells are covered
    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            if (m_board[i][j] == 1 && board[i][j] <= 2) {
                return false; // Required position not covered
            }
            if (m_board[i][j] == 2 && board[i][j] > 2) {
                return false; // Forbidden position covered
            }
        }
    }

    // Check if all pieces are used correctly
    for (const auto& pair : m_pieceCounts) {
        if (pair.second > 0) {
            auto it = piecesUsed.find(pair.first);
            int usedCount = (it != piecesUsed.end()) ? it->second : 0;
            if (usedCount != pair.second) {
                return false; // Piece count mismatch
            }
        }
    }

    return true;
}

std::map<std::string, int> TetrisPuzzleGame::calculateUsedPieces(const std::vector<std::vector<int>>& board) {
    std::map<std::string, int> usedPieces;
    std::vector<std::string> pieceNames = {"T", "田", "横杠竖条", "Z", "L"};

    // Initialize counters
    for (const std::string& name : pieceNames) {
        usedPieces[name] = 0;
    }

    // Count pieces
    std::set<int> pieceIds;
    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            if (board[i][j] > 2) {
                pieceIds.insert(board[i][j]);
            }
        }
    }

    // Map piece IDs to piece names
    for (int pieceId : pieceIds) {
        int pieceIdx = pieceId - 3;
        if (pieceIdx >= 0 && pieceIdx < pieceNames.size()) {
            usedPieces[pieceNames[pieceIdx]]++;
        }
    }

    return usedPieces;
}

Solution TetrisPuzzleGame::evaluateSolution(const std::vector<std::vector<int>>& board) {
    Solution solution;
    solution.score = 0;
    solution.filledCells = 0;
    solution.requiredCovered = 0;
    solution.forbiddenViolated = 0;

    // Calculate basic metrics
    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            if (board[i][j] > 0) {
                solution.filledCells++;
            }
            if (m_board[i][j] == 1 && board[i][j] > 0) {
                solution.requiredCovered++;
            }
            if (m_board[i][j] == 2 && board[i][j] > 0) {
                solution.forbiddenViolated++;
            }
        }
    }

    // Calculate score
    solution.score += solution.requiredCovered * 100; // High score for covering required positions
    solution.score -= solution.forbiddenViolated * 1000; // Heavy penalty for violating forbidden positions
    solution.score += solution.filledCells * 10; // Bonus for filling more cells

    // Calculate continuity bonus (adjacent cells of same type)
    int continuityBonus = 0;
    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            if (board[i][j] > 0) {
                if (i > 0 && board[i-1][j] == board[i][j]) continuityBonus++;
                if (j > 0 && board[i][j-1] == board[i][j]) continuityBonus++;
            }
        }
    }
    solution.score += continuityBonus * 5;

    solution.isValid = (solution.forbiddenViolated == 0);

    return solution;
}

void TetrisPuzzleGame::processAllSolutions() {
    updateProgress(0, "开始处理解决方案...");

    // Step 1: Remove duplicates
    std::map<std::string, Solution> solutionMap;
    int processedCount = 0;

    for (const Solution& solution : m_rawSolutions) {
        processedCount++;
        const std::string& hash = solution.hash;

        if (solutionMap.find(hash) == solutionMap.end()) {
            solutionMap[hash] = solution;
        } else {
            m_duplicatesFound++;
        }

        // Update progress
        int progress = (processedCount * 50) / m_rawSolutions.size(); // First 50% for deduplication
        updateProgress(progress, QString("去重处理中... %1/%2").arg(processedCount).arg(m_rawSolutions.size()));
    }

    // Step 2: Categorize and sort
    m_uniqueSolutions.clear();
    m_completeSolutions.clear();

    for (const auto& pair : solutionMap) {
        m_uniqueSolutions.push_back(pair.second);
        if (pair.second.isComplete) {
            m_completeSolutions.push_back(pair.second);
        }
    }

    // Sort complete solutions by score
    std::sort(m_completeSolutions.begin(), m_completeSolutions.end(),
              [](const Solution& a, const Solution& b) {
                  return a.score > b.score;
              });

    updateProgress(75, "计算概率统计中...");

    // Step 3: Calculate probability statistics
    m_cellCoverage = std::vector<std::vector<int>>(m_boardSize, std::vector<int>(m_boardSize, 0));

    for (const Solution& solution : m_completeSolutions) {
        for (int i = 0; i < m_boardSize; ++i) {
            for (int j = 0; j < m_boardSize; ++j) {
                if (solution.board[i][j] > 0) {
                    m_cellCoverage[i][j]++;
                }
            }
        }
    }

    updateProgress(90, "更新显示中...");

    // Step 4: Update display
    updateSolutionsDisplay();
    updateStatistics();

    updateProgress(100, "处理完成！");

    // Show completion message
    QString resultMsg = QString(
        "处理完成！\n"
        "- 找到原始方案: %1 个\n"
        "- 去重后方案: %2 个\n"
        "- 完整解决方案: %3 个\n"
        "- 重复方案: %4 个"
    ).arg(m_rawSolutions.size())
     .arg(m_uniqueSolutions.size())
     .arg(m_completeSolutions.size())
     .arg(m_duplicatesFound);

    m_statusMain->setText("解决方案处理完成！");
    m_statusMain->setStyleSheet("background-color: lightgreen; padding: 5px; border-radius: 3px;");
}

void TetrisPuzzleGame::updateProgress(int percentage, const QString& text) {
    m_progressBar->setValue(percentage);
    m_progressText->setText(text);
}

void TetrisPuzzleGame::updateStatistics() {
    int totalComplete = m_completeSolutions.size();

    m_totalSolutionsLabel->setText(QString::number(m_rawSolutions.size()));
    m_validSolutionsLabel->setText(QString::number(totalComplete));
    m_duplicatesRemovedLabel->setText(QString::number(m_duplicatesFound));

    if (totalComplete == 0) {
        m_averageCoverageLabel->setText("0%");
        updateProbabilityHeatmap();
        return;
    }

    // Calculate average coverage
    double totalCoverage = 0;
    int totalCells = m_boardSize * m_boardSize;

    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            double probability = static_cast<double>(m_cellCoverage[i][j]) / totalComplete;
            totalCoverage += probability;
        }
    }

    double averageCoverage = (totalCoverage / totalCells) * 100;
    m_averageCoverageLabel->setText(QString::number(averageCoverage, 'f', 1) + "%");

    updateProbabilityHeatmap();
}

void TetrisPuzzleGame::updateProbabilityHeatmap() {
    // Clear existing heatmap
    if (m_probabilityHeatmap->layout()) {
        QLayoutItem* item;
        while ((item = m_probabilityHeatmap->layout()->takeAt(0)) != nullptr) {
            delete item->widget();
            delete item;
        }
        delete m_probabilityHeatmap->layout();
    }

    QGridLayout* heatmapLayout = new QGridLayout(m_probabilityHeatmap);
    heatmapLayout->setSpacing(0);
    heatmapLayout->setContentsMargins(2, 2, 2, 2);

    int totalComplete = m_completeSolutions.size();

    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            QLabel* cell = new QLabel;
            cell->setFixedSize(25, 25);
            cell->setAlignment(Qt::AlignCenter);
            cell->setStyleSheet("border: 0.5px solid #ddd; font-size: 8px; font-weight: bold;");

            if (totalComplete > 0) {
                double probability = static_cast<double>(m_cellCoverage[i][j]) / totalComplete;
                int percentage = static_cast<int>(probability * 100);
                int heatLevel = std::min(10, static_cast<int>(probability * 10));

                // Set heat level color
                QStringList heatColors = {
                    "#f8f9fa", "#e3f2fd", "#bbdefb", "#90caf9", "#64b5f6",
                    "#42a5f5", "#2196f3", "#1e88e5", "#1976d2", "#1565c0", "#0d47a1"
                };

                QStringList textColors = {
                    "#6c757d", "#1976d2", "#1565c0", "#1565c0", "#0d47a1",
                    "#0d47a1", "white", "white", "white", "white", "white"
                };

                cell->setStyleSheet(QString(
                    "border: 0.5px solid #ddd; font-size: 8px; font-weight: bold; "
                    "background-color: %1; color: %2;"
                ).arg(heatColors[heatLevel]).arg(textColors[heatLevel]));

                if (percentage > 0) {
                    cell->setText(QString::number(percentage) + "%");
                }

                cell->setToolTip(QString("位置 (%1,%2): %3% 概率被填充").arg(i).arg(j).arg(percentage));
            } else {
                cell->setStyleSheet("border: 0.5px solid #ddd; background-color: #f8f9fa; color: #6c757d;");
            }

            heatmapLayout->addWidget(cell, i, j);
        }
    }
}

void TetrisPuzzleGame::updateSolutionsDisplay() {
    // Clear existing solutions
    if (m_solutionsGrid->layout()) {
        QLayoutItem* item;
        while ((item = m_solutionsGrid->layout()->takeAt(0)) != nullptr) {
            delete item->widget();
            delete item;
        }
        delete m_solutionsGrid->layout();
    }

    QGridLayout* gridLayout = new QGridLayout(m_solutionsGrid);
    gridLayout->setSpacing(15);

    int completeCount = m_completeSolutions.size();
    int displayCount = std::min(completeCount, 10); // Display at most 10 solutions

    // Update statistics text
    m_solutionsStats->setText(QString(
        "找到: %1 个完整解决方案 (显示前%2个) (原始 %3 个，去重 %4 个)"
    ).arg(completeCount).arg(displayCount).arg(m_rawSolutions.size()).arg(m_duplicatesFound));

    // Display solutions
    int cols = 3; // 3 solutions per row
    for (int idx = 0; idx < displayCount; ++idx) {
        const Solution& solution = m_completeSolutions[idx];

        QWidget* solutionWidget = new QWidget;
        solutionWidget->setStyleSheet(
            "QWidget { border: 2px solid #4CAF50; border-radius: 8px; padding: 10px; "
            "background-color: #e8f5e8; }"
        );
        solutionWidget->setCursor(Qt::PointingHandCursor);

        QVBoxLayout* solutionLayout = new QVBoxLayout(solutionWidget);

        // Header with rank badge
        QHBoxLayout* headerLayout = new QHBoxLayout;

        QLabel* rankBadge = new QLabel(QString::number(idx + 1));
        rankBadge->setFixedSize(20, 20);
        rankBadge->setAlignment(Qt::AlignCenter);
        rankBadge->setStyleSheet(QString(
            "border-radius: 10px; background-color: %1; color: white; "
            "font-size: 10px; font-weight: bold;"
        ).arg(idx < 3 ? (idx == 0 ? "#FFD700" : (idx == 1 ? "#C0C0C0" : "#CD7F32")) : "#2196F3"));

        QLabel* headerLabel = new QLabel(QString::fromStdString(solution.algorithm) + " ✅");
        headerLabel->setStyleSheet("font-size: 11px; font-weight: bold;");

        headerLayout->addWidget(rankBadge);
        headerLayout->addWidget(headerLabel);
        headerLayout->addStretch();
        solutionLayout->addLayout(headerLayout);

        // Mini board
        QWidget* miniBoard = new QWidget;
        miniBoard->setStyleSheet("border: 1px solid #666; background-color: white;");
        QGridLayout* miniBoardLayout = new QGridLayout(miniBoard);
        miniBoardLayout->setSpacing(0);
        miniBoardLayout->setContentsMargins(0, 0, 0, 0);

        for (int i = 0; i < m_boardSize; ++i) {
            for (int j = 0; j < m_boardSize; ++j) {
                QLabel* cell = new QLabel;
                cell->setFixedSize(20, 20);
                cell->setAlignment(Qt::AlignCenter);
                cell->setStyleSheet("border: 0.5px solid #ccc; font-size: 8px;");

                int value = solution.board[i][j];
                int originalValue = m_board[i][j];

                if (value == 0) {
                    if (originalValue == 1) {
                        cell->setStyleSheet(cell->styleSheet() + "background-color: red; color: white;");
                        cell->setText("✓");
                    } else if (originalValue == 2) {
                        cell->setStyleSheet(cell->styleSheet() + "background-color: gray; color: white;");
                        cell->setText("✗");
                    } else {
                        cell->setStyleSheet(cell->styleSheet() + "background-color: lightblue;");
                    }
                } else {
                    std::vector<std::string> pieceNames = {"T", "田", "横杠竖条", "Z", "L"};
                    int pieceIdx = value - 3;
                    if (pieceIdx >= 0 && pieceIdx < pieceNames.size()) {
                        std::string pieceName = pieceNames[pieceIdx];
                        QColor color = m_pieceColors[pieceName];
                        cell->setStyleSheet(cell->styleSheet() +
                            QString("background-color: %1; color: white;").arg(color.name()));
                        cell->setText(QString::fromStdString(pieceName).left(1));
                    }
                }

                miniBoardLayout->addWidget(cell, i, j);
            }
        }

        solutionLayout->addWidget(miniBoard, 0, Qt::AlignCenter);

        // Solution info
        QLabel* infoLabel = new QLabel;
        QString usedPiecesText;
        for (const auto& pair : solution.piecesUsed) {
            if (pair.second > 0) {
                if (!usedPiecesText.isEmpty()) usedPiecesText += " ";
                usedPiecesText += QString::fromStdString(pair.first) + ":" + QString::number(pair.second);
            }
        }

        infoLabel->setText(QString(
            "评分: %1<br>"
            "步数: %2<br>"
            "方块: %3<br>"
            "填充: %4/%5"
        ).arg(solution.score)
         .arg(solution.step)
         .arg(usedPiecesText)
         .arg(solution.filledCells)
         .arg(m_boardSize * m_boardSize));

        infoLabel->setStyleSheet("font-size: 9px; text-align: center; color: #666;");
        solutionLayout->addWidget(infoLabel);

        // Add click event using event filter
        solutionWidget->installEventFilter(this);
        solutionWidget->setProperty("solutionBoard", QVariant::fromValue(solution.board));

        int row = idx / cols;
        int col = idx % cols;
        gridLayout->addWidget(solutionWidget, row, col);
    }
}

bool TetrisPuzzleGame::eventFilter(QObject* obj, QEvent* event) {
    if (event->type() == QEvent::MouseButtonPress) {
        QWidget* widget = qobject_cast<QWidget*>(obj);
        if (widget && widget->property("solutionBoard").isValid()) {
            auto board = widget->property("solutionBoard").value<std::vector<std::vector<int>>>();
            copySolutionToMainBoard(board);
            return true;
        }
    }
    return QMainWindow::eventFilter(obj, event);
}

void TetrisPuzzleGame::copySolutionToMainBoard(const std::vector<std::vector<int>>& solutionBoard) {
    if (m_solving) return;

    // Preserve original marks, only update piece placements
    for (int i = 0; i < m_boardSize; ++i) {
        for (int j = 0; j < m_boardSize; ++j) {
            if (m_board[i][j] <= 2) {
                // Keep original marks
            } else {
                m_board[i][j] = 0; // Clear old pieces
            }

            if (solutionBoard[i][j] > 0) {
                m_board[i][j] = solutionBoard[i][j];
            }
        }
    }

    updateBoardDisplay();

    m_statusMain->setText("已复制解决方案到主棋盘");
    m_statusMain->setStyleSheet("background-color: lightgreen; padding: 5px; border-radius: 3px;");
}

void TetrisPuzzleGame::importBoard() {
    if (m_solving) {
        QMessageBox::warning(this, "警告", "正在破解中，请先停止破解！");
        return;
    }

    QString importText = m_importTextEdit->toPlainText().trimmed();
    if (importText.isEmpty()) {
        QMessageBox::warning(this, "警告", "请输入要导入的棋盘数据！");
        return;
    }

    try {
        QJsonParseError error;
        QJsonDocument doc = QJsonDocument::fromJson(importText.toUtf8(), &error);

        if (error.error != QJsonParseError::NoError) {
            QMessageBox::warning(this, "错误", "JSON格式错误: " + error.errorString());
            return;
        }

        QJsonObject obj = doc.object();

        // Import board size
        if (obj.contains("board_size")) {
            int newSize = obj["board_size"].toInt();
            if (newSize >= 3 && newSize <= 9) {
                m_boardSize = newSize;
                m_boardSizeCombo->setCurrentText(QString("%1x%1").arg(newSize));
                initBoard();
                createBoard();
            }
        }

        // Import board data
        if (obj.contains("board")) {
            QJsonArray boardArray = obj["board"].toArray();
            for (int i = 0; i < boardArray.size() && i < m_boardSize; ++i) {
                QJsonArray rowArray = boardArray[i].toArray();
                for (int j = 0; j < rowArray.size() && j < m_boardSize; ++j) {
                    m_board[i][j] = rowArray[j].toInt();
                }
            }
        }

        // Import piece counts
        if (obj.contains("piece_counts")) {
            QJsonObject pieceCounts = obj["piece_counts"].toObject();
            for (auto it = pieceCounts.begin(); it != pieceCounts.end(); ++it) {
                std::string pieceName = it.key().toStdString();
                int count = it.value().toInt();
                if (m_pieceCounts.find(pieceName) != m_pieceCounts.end()) {
                    m_pieceCounts[pieceName] = count;

                    // Update UI buttons
                    auto& buttons = m_pieceCountButtons[pieceName];
                    for (auto* btn : buttons) {
                        btn->setChecked(false);
                        btn->setStyleSheet("");
                        if (btn->property("count").toInt() == count) {
                            btn->setChecked(true);
                            btn->setStyleSheet("QPushButton:checked { background-color: #4CAF50; color: white; }");
                        }
                    }
                }
            }
        }

        // Import show process setting
        if (obj.contains("show_process")) {
            bool showProcess = obj["show_process"].toBool();
            m_showProcessCheck->setChecked(showProcess);
            onShowProcessToggled();
        }

        updateBoardDisplay();
        clearSolutions();

        QMessageBox::information(this, "成功", "棋盘数据导入成功！");

    } catch (const std::exception& e) {
        QMessageBox::warning(this, "错误", QString("导入失败: %1").arg(e.what()));
    }
}

void TetrisPuzzleGame::exportBoard() {
    QJsonObject data;
    data["board_size"] = m_boardSize;
    data["show_process"] = m_showProcess;

    // Export board
    QJsonArray boardArray;
    for (int i = 0; i < m_boardSize; ++i) {
        QJsonArray rowArray;
        for (int j = 0; j < m_boardSize; ++j) {
            rowArray.append(m_board[i][j]);
        }
        boardArray.append(rowArray);
    }
    data["board"] = boardArray;

    // Export piece counts
    QJsonObject pieceCountsObj;
    for (const auto& pair : m_pieceCounts) {
        pieceCountsObj[QString::fromStdString(pair.first)] = pair.second;
    }
    data["piece_counts"] = pieceCountsObj;

    QJsonDocument doc(data);
    QString dataStr = doc.toJson(QJsonDocument::Indented);

    // Copy to clipboard
    QClipboard* clipboard = QApplication::clipboard();
    clipboard->setText(dataStr);

    QMessageBox::information(this, "成功", "棋盘数据已复制到剪贴板！");
}

// MOC file will be automatically generated and included by qmake
