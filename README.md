# 俄罗斯方块拼图游戏 (Tetris Puzzle Game)

这是一个基于Qt6和C++的俄罗斯方块拼图游戏，从原始的HTML/JavaScript版本转换而来。游戏保持了原版的所有功能和界面设计。

## 功能特性

- **多种棋盘大小**: 支持3x3到9x9的棋盘
- **多种俄罗斯方块**: T形、田字形、横杠竖条、Z形、L形方块
- **智能求解算法**: 回溯算法自动求解拼图
- **解决方案统计**: 显示找到的解决方案数量、去重统计等
- **概率热力图**: 显示每个位置被填充的概率
- **解决方案展示**: 可视化显示前10个最佳解决方案
- **导入导出功能**: 支持JSON格式的棋盘数据导入导出
- **实时进度显示**: 显示求解进度和状态

## 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, 或 Linux (Ubuntu 20.04+)
- **Qt版本**: Qt 6.2 或更高版本
- **编译器**: 
  - Windows: MSVC 2019+ 或 MinGW 8.1+
  - macOS: Xcode 12+ (Clang)
  - Linux: GCC 9+ 或 Clang 10+
- **CMake**: 3.16 或更高版本

## 构建说明

### 1. 安装依赖

#### Windows
1. 下载并安装 [Qt6](https://www.qt.io/download)
2. 下载并安装 [CMake](https://cmake.org/download/)
3. 安装 Visual Studio 2019+ 或 MinGW

#### macOS
```bash
# 使用 Homebrew 安装
brew install qt6 cmake
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install qt6-base-dev qt6-tools-dev cmake build-essential
```

### 2. 克隆或下载源代码

将所有源文件放在同一个目录中：
- `main.cpp`
- `TetrisPuzzleGame.h`
- `TetrisPuzzleGame.cpp`
- `CMakeLists.txt`

### 3. 构建项目

```bash
# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译项目
cmake --build . --config Release

# 或者在Windows上使用
cmake --build . --config Release --parallel
```

### 4. 运行程序

#### Windows
```bash
# 在build目录中
./Release/TetrisPuzzleGame.exe
```

#### macOS/Linux
```bash
# 在build目录中
./TetrisPuzzleGame
```

## 使用说明

### 基本操作

1. **设置棋盘**: 
   - 在右上角的悬浮控制面板中选择棋盘大小
   - 设置每种方块的数量（0-2个）

2. **标记棋盘**:
   - **左键点击**: 标记为必需位置（红色✓）
   - **右键点击**: 标记为禁止位置（灰色✗）
   - **双击**: 清除标记（蓝色空白）

3. **开始求解**:
   - 点击"开始标注"按钮开始自动求解
   - 可以勾选"显示破解过程"来查看求解步骤
   - 点击"停止破解"可以随时停止

4. **查看结果**:
   - 右侧显示解决方案统计信息
   - 概率热力图显示每个位置被填充的概率
   - 解决方案展示区显示前10个最佳解决方案
   - 点击任意解决方案可以复制到主棋盘

### 导入导出

- **导出**: 点击"复制棋盘"将当前棋盘数据复制到剪贴板
- **导入**: 在文本框中粘贴JSON格式的棋盘数据，然后点击"导入棋盘"

### 方块类型

- **T**: T形方块（紫色）
- **田**: 田字形方块（橙色）
- **横杠竖条**: I形方块（青色）
- **Z**: Z形和S形方块（红色）
- **L**: L形和J形方块（蓝色）

## 技术实现

### 架构设计

- **主窗口类** (`TetrisPuzzleGame`): 管理整个游戏界面和逻辑
- **棋盘单元格类** (`BoardCell`): 自定义按钮，处理鼠标事件
- **求解线程类** (`SolverThread`): 在后台线程中运行回溯算法
- **解决方案结构** (`Solution`): 存储解决方案数据和评分

### 核心算法

- **回溯算法**: 递归尝试放置每个方块的所有可能位置和旋转
- **去重算法**: 使用哈希值识别和移除重复的解决方案
- **评分系统**: 根据覆盖必需位置、避免禁止位置等因素评分
- **概率统计**: 计算每个位置在所有解决方案中被填充的概率

### Qt特性使用

- **信号槽机制**: 用于UI事件处理和线程间通信
- **自定义控件**: 继承QPushButton创建棋盘单元格
- **多线程**: 使用QThread在后台运行求解算法
- **JSON处理**: 使用QJsonDocument处理导入导出数据
- **样式表**: 使用QSS美化界面外观

## 故障排除

### 常见问题

1. **编译错误**: 确保Qt6正确安装并且CMake能找到Qt
2. **运行时错误**: 确保Qt6运行时库在系统PATH中
3. **界面显示问题**: 检查系统DPI设置和Qt缩放因子

### 调试模式

在CMake配置时使用Debug模式：
```bash
cmake -DCMAKE_BUILD_TYPE=Debug ..
```

## 许可证

本项目基于原始HTML/JavaScript版本转换，保持开源性质。

## 贡献

欢迎提交问题报告和功能建议。如需贡献代码，请：

1. Fork 本项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 更新日志

### v1.0.0
- 完整的C++/Qt6实现
- 保持原版所有功能
- 优化的用户界面
- 多线程求解算法
- 完整的导入导出功能
