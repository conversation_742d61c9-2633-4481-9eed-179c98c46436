#############################################################################
# Makefile for building: TetrisPuzzleGame
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ..\TetrisPuzzleGame.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Debug

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -fno-keep-inline-dllexport -g -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I../../els -I. -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtCore -Idebug -I/include -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,-subsystem,console -mthreads
LIBS        =        C:\Qt\6.9.1\mingw_64\lib\libQt6Widgets.a C:\Qt\6.9.1\mingw_64\lib\libQt6Gui.a C:\Qt\6.9.1\mingw_64\lib\libQt6Core.a   
QMAKE         = C:\Qt\6.9.1\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = C:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = C:\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = debug

####### Files

SOURCES       = ..\main.cpp \
		..\TetrisPuzzleGame.cpp debug\moc_TetrisPuzzleGame.cpp
OBJECTS       = debug/main.o \
		debug/TetrisPuzzleGame.o \
		debug/moc_TetrisPuzzleGame.o

DIST          =  ..\TetrisPuzzleGame.h ..\main.cpp \
		..\TetrisPuzzleGame.cpp
QMAKE_TARGET  = TetrisPuzzleGame
DESTDIR        = debug\ #avoid trailing-slash linebreak
TARGET         = TetrisPuzzleGame.exe
DESTDIR_TARGET = debug\TetrisPuzzleGame.exe

####### Build rules

first: all
all: Makefile.Debug  debug/TetrisPuzzleGame.exe

debug/TetrisPuzzleGame.exe: C:/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a C:/Qt/6.9.1/mingw_64/lib/libQt6Gui.a C:/Qt/6.9.1/mingw_64/lib/libQt6Core.a $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) $(OBJECTS) $(LIBS)

qmake: FORCE
	@$(QMAKE) -o Makefile.Debug ..\TetrisPuzzleGame.pro

qmake_all: FORCE

dist:
	$(ZIP) TetrisPuzzleGame.zip $(SOURCES) $(DIST) ..\..\TetrisPuzzleGame.pro C:\Qt\6.9.1\mingw_64\mkspecs\features\spec_pre.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\device_config.prf C:\Qt\6.9.1\mingw_64\mkspecs\common\sanitize.conf C:\Qt\6.9.1\mingw_64\mkspecs\common\gcc-base.conf C:\Qt\6.9.1\mingw_64\mkspecs\common\g++-base.conf C:\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf C:\Qt\6.9.1\mingw_64\mkspecs\common\windows-vulkan.conf C:\Qt\6.9.1\mingw_64\mkspecs\common\g++-win32.conf C:\Qt\6.9.1\mingw_64\mkspecs\common\windows-desktop.conf C:\Qt\6.9.1\mingw_64\mkspecs\qconfig.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_freetype.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libpng.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_linguist.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_png_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_tools_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri C:\Qt\6.9.1\mingw_64\mkspecs\features\qt_functions.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\qt_config.prf C:\Qt\6.9.1\mingw_64\mkspecs\win32-g++\qmake.conf C:\Qt\6.9.1\mingw_64\mkspecs\features\spec_post.prf .qmake.stash C:\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\toolchain.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\default_pre.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\win32\default_pre.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\resolve_config.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds_post.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\default_post.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\build_pass.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\win32\console.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\precompile_header.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\warn_on.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\permissions.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\qt.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\resources_functions.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\resources.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\moc.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\win32\opengl.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\uic.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\qmake_use.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\file_copies.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\testcase_targets.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\exceptions.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\yacc.prf C:\Qt\6.9.1\mingw_64\mkspecs\features\lex.prf ..\TetrisPuzzleGame.pro C:\Qt\6.9.1\mingw_64\lib\Qt6Widgets.prl C:\Qt\6.9.1\mingw_64\lib\Qt6Gui.prl C:\Qt\6.9.1\mingw_64\lib\Qt6Core.prl    C:\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp ..\TetrisPuzzleGame.h  ..\main.cpp ..\TetrisPuzzleGame.cpp     

clean: compiler_clean 
	-$(DEL_FILE) debug\main.o debug\TetrisPuzzleGame.o debug\moc_TetrisPuzzleGame.o

distclean: clean 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Debug

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: debug/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) debug\moc_predefs.h
debug/moc_predefs.h: C:/Qt/6.9.1/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -g -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o debug\moc_predefs.h C:\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: debug/moc_TetrisPuzzleGame.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) debug\moc_TetrisPuzzleGame.cpp
debug/moc_TetrisPuzzleGame.cpp: ../TetrisPuzzleGame.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgets \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgetsDepends \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QtCore \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QtCoreDepends \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20chrono.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20map.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20vector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q23functional.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractnativeeventfilter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qapplicationstatic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qassociativeiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomicscopedvaluerollback.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbitarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraymatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcache.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcbormap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamreader.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qchronotimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qproperty.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyprivate.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcollator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineparser.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconcatenatetablesproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdiriterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qeasingcurve.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfileselector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfilesystemwatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturesynchronizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qidentityproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlibrary.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlibraryinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlockfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qloggingcategory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmessageauthenticationcode.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimedatabase.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimetype.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectcleanuphandler.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qoperatingsystemversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qparallelanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpauseanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpermissions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpluginloader.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qqueue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qreadwritelock.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qresource.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsavefile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedvaluerollback.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsemaphore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedmemory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtipccommon.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsignalmapper.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsimd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsocketnotifier.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsortfilterproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstack.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstaticlatin1stringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstorageinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlistmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemsemaphore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtemporarydir.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtemporaryfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtextboundaryfinder.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadstorage.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimeline.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtmocconstants.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtranslator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtransposeproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtsymbolmacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvarianthash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QHash \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QString \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QList \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qwaitcondition.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QDeadlineTimer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qwineventnotifier.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxmlstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxpfunctional.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QtGui \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QtGuiDepends \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qabstractfileiconprovider.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qabstracttextdocumentlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qglyphrun.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrawfont.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontdatabase.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible_base.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessiblebridge.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qactiongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbackingstore.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsurface.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsurfaceformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qclipboard.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolorspace.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolortransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qdrag.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfilesystemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericmatrix.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericpluginfactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qiconengine.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qiconengineplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimagewriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qmatrix4x4.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector3d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector4d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qquaternion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopengl.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglext.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qt_windows.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QSurfaceFormat \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglextrafunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglfunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagedpaintdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagelayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagesize.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpageranges.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevicewindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QWindow \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDevice \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintengine.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainterstateguard.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpdfoutputintent.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpdfwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmapcache.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrasterwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDeviceWindow \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgbafloat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsessionmanager.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qshortcut.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstandarditemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstatictext.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstylehints.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsyntaxhighlighter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentfragment.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtexttable.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qundogroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qundostack.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowsmimeconverter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qaccessiblewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QAction \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qactiongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QActionGroup \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qbuttongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcalendarwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolordialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolormap.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolumnview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommandlinkbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommonstyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcompleter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatawidgetmapper.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatetimeedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdial.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdockwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdrawutil.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qerrormessage.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfileiconprovider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfilesystemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QFileSystemModel \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfocusframe.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontcombobox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qformlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesture.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesturerecognizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsanchorlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicseffect.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsgridlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitemanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslinearlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsproxywidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicssceneevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicstransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QVector3D \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix4x4 \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qinputdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemeditorfactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qkeysequenceedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlcdnumber.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdiarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdisubwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qplaintextedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qproxystyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCommonStyle \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qradiobutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrhiwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscroller.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QPointF \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollerProperties \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollerproperties.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qshortcut.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QShortcut \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizegrip.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplashscreen.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleditemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylefactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylepainter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsystemtrayicon.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtableview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtablewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextbrowser.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtooltip.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qundoview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwhatsthis.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidgetaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwizard.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGridLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCheckBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollArea \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSpinBox \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QClipboard \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QMouseEvent \
		debug/moc_predefs.h \
		C:/Qt/6.9.1/mingw_64/bin/moc.exe
	C:\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include C:/Users/<USER>/Documents/augment-projects/els/build-qmake/debug/moc_predefs.h -IC:/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IC:/Users/<USER>/Documents/augment-projects/els -IC:/Qt/6.9.1/mingw_64/include -IC:/Qt/6.9.1/mingw_64/include/QtWidgets -IC:/Qt/6.9.1/mingw_64/include/QtGui -IC:/Qt/6.9.1/mingw_64/include/QtCore -I. -IC:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++ -IC:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32 -IC:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward -IC:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -IC:/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -IC:/mingw64/x86_64-w64-mingw32/include ..\TetrisPuzzleGame.h -o debug\moc_TetrisPuzzleGame.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all:
compiler_uic_clean:
compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean 



####### Compile

debug/main.o: ../main.cpp C:/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QStyleFactory \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylefactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QDir \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QStandardPaths \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QFontDatabase \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontdatabase.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		../TetrisPuzzleGame.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgets \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgetsDepends \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QtCore \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QtCoreDepends \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20chrono.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20map.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20vector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q23functional.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractnativeeventfilter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qapplicationstatic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qassociativeiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomicscopedvaluerollback.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbitarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraymatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcache.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcbormap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamreader.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qchronotimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qproperty.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyprivate.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcollator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineparser.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconcatenatetablesproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdiriterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qeasingcurve.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfileselector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfilesystemwatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturesynchronizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qidentityproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlibrary.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlibraryinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlockfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qloggingcategory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmessageauthenticationcode.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimedatabase.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimetype.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectcleanuphandler.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qoperatingsystemversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qparallelanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpauseanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpermissions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpluginloader.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qqueue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qreadwritelock.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qresource.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsavefile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedvaluerollback.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsemaphore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedmemory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtipccommon.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsignalmapper.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsimd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsocketnotifier.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsortfilterproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstack.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstaticlatin1stringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstorageinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlistmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemsemaphore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtemporarydir.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtemporaryfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtextboundaryfinder.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadstorage.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimeline.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtmocconstants.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtranslator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtransposeproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtsymbolmacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvarianthash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QHash \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QString \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QList \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qwaitcondition.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QDeadlineTimer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qwineventnotifier.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxmlstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxpfunctional.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QtGui \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QtGuiDepends \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qabstractfileiconprovider.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qabstracttextdocumentlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qglyphrun.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrawfont.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible_base.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessiblebridge.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qactiongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbackingstore.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsurface.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsurfaceformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qclipboard.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolorspace.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolortransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qdrag.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfilesystemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericmatrix.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericpluginfactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qiconengine.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qiconengineplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimagewriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qmatrix4x4.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector3d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector4d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qquaternion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopengl.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglext.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qt_windows.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QSurfaceFormat \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglextrafunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglfunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagedpaintdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagelayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagesize.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpageranges.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevicewindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QWindow \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDevice \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintengine.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainterstateguard.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpdfoutputintent.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpdfwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmapcache.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrasterwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDeviceWindow \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgbafloat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsessionmanager.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qshortcut.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstandarditemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstatictext.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstylehints.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsyntaxhighlighter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentfragment.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtexttable.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qundogroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qundostack.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowsmimeconverter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qaccessiblewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QAction \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qactiongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QActionGroup \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qbuttongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcalendarwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolordialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolormap.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolumnview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommandlinkbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommonstyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcompleter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatawidgetmapper.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatetimeedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdial.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdockwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdrawutil.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qerrormessage.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfileiconprovider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfilesystemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QFileSystemModel \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfocusframe.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontcombobox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qformlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesture.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesturerecognizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsanchorlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicseffect.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsgridlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitemanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslinearlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsproxywidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicssceneevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicstransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QVector3D \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix4x4 \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qinputdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemeditorfactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qkeysequenceedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlcdnumber.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdiarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdisubwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qplaintextedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qproxystyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCommonStyle \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qradiobutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrhiwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscroller.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QPointF \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollerProperties \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollerproperties.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qshortcut.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QShortcut \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizegrip.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplashscreen.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleditemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylepainter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsystemtrayicon.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtableview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtablewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextbrowser.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtooltip.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qundoview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwhatsthis.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidgetaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwizard.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGridLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCheckBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollArea \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSpinBox \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QClipboard \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QMouseEvent
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\main.o ..\main.cpp

debug/TetrisPuzzleGame.o: ../TetrisPuzzleGame.cpp ../TetrisPuzzleGame.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgets \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QtWidgetsDepends \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QtCore \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QtCoreDepends \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20algorithm.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20chrono.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20map.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q20vector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q23functional.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/q26numeric.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractitemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractnativeeventfilter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qabstractproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qapplicationstatic.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qassociativeiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qatomicscopedvaluerollback.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbitarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbuffer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qbytearraymatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcache.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborvalue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborcommon.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qregularexpression.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/quuid.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcbormap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamreader.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcborstreamwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qchronotimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qproperty.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyprivate.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcollator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcommandlineparser.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qconcatenatetablesproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qcryptographichash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qdiriterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qeasingcurve.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfactoryinterface.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfileselector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfilesystemwatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturesynchronizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qfuturewatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qidentityproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qitemselectionmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonarray.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonvalue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsondocument.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonparseerror.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qjsonobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlibrary.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlibraryinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qlockfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qloggingcategory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmessageauthenticationcode.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimedata.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimedatabase.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qmimetype.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qobjectcleanuphandler.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qoperatingsystemversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qparallelanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpauseanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpermissions.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpluginloader.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qprocess.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qpropertyanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qqueue.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrandom.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qreadwritelock.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qresource.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsavefile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qscopedvaluerollback.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsemaphore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialanimationgroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsequentialiterable.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsettings.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsharedmemory.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtipccommon.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsignalmapper.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsimd.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsocketnotifier.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsortfilterproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstack.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstaticlatin1stringmatcher.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstorageinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qstringlistmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qsystemsemaphore.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtcoreversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtemporarydir.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtemporaryfile.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtextboundaryfinder.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qthreadstorage.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtimeline.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtmocconstants.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtranslator.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtransposeproxymodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qtsymbolmacros.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qurlquery.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvarianthash.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QHash \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QString \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QList \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qvector.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qwaitcondition.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QDeadlineTimer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qwineventnotifier.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxmlstream.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qxpfunctional.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QtGui \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QtGuiDepends \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qabstractfileiconprovider.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qabstracttextdocumentlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qglyphrun.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrawfont.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontdatabase.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextcursor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessible_base.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessiblebridge.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaccessibleplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qactiongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbackingstore.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QEvent \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMargins \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsurface.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsurfaceformat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qclipboard.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolorspace.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qcolortransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qdesktopservices.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qdrag.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfilesystemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericmatrix.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qgenericpluginfactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qiconengine.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qiconengineplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimageiohandler.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimagereader.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qimagewriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qmatrix4x4.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector3d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvector4d.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qquaternion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qmovie.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qoffscreensurface_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopengl.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglext.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/qt_windows.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QSurfaceFormat \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglcontext_platform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglextrafunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qopenglfunctions.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagedpaintdevice.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagelayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpagesize.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpageranges.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevicewindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QWindow \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDevice \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpaintengine.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainterpath.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpainterstateguard.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpdfoutputintent.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpdfwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qpixmapcache.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrasterwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QPaintDeviceWindow \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qrgbafloat.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsessionmanager.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qshortcut.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstandarditemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstatictext.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qstylehints.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qsyntaxhighlighter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextobject.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentfragment.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextdocumentwriter.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtextlist.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtexttable.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qtguiversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qundogroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qundostack.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qvalidator.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/qwindowsmimeconverter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleoption.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractspinbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrubberband.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractitemview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractscrollarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qaccessiblewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QAction \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qactiongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QActionGroup \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qbuttongroup.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcalendarwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcheckbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolordialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolormap.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcolumnview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcombobox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommandlinkbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcommonstyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qcompleter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatawidgetmapper.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdatetimeedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdial.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdialogbuttonbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdockwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qdrawutil.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qerrormessage.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfileiconprovider.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfilesystemmodel.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QFileSystemModel \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfocusframe.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontcombobox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qfontdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qformlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesture.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgesturerecognizer.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsanchorlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslayoutitem.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicseffect.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsgridlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsitemanimation.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicslinearlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsproxywidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicswidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsscene.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicssceneevent.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicstransform.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QVector3D \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QMatrix4x4 \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgraphicsview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qgroupbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qheaderview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qinputdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlineedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qitemeditorfactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qkeysequenceedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlcdnumber.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qlistwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdiarea.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmdisubwindow.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenu.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmenubar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qmessagebox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qplaintextedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextedit.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qprogressdialog.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qproxystyle.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCommonStyle \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qradiobutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qrhiwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscroller.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QPointF \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollerProperties \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qscrollerproperties.h \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QScopedPointer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QMetaType \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qshortcut.h \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QShortcut \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsizegrip.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qspinbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplashscreen.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsplitter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedlayout.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstackedwidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstatusbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleditemdelegate.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylefactory.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstylepainter.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qstyleplugin.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qsystemtrayicon.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtableview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtablewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtextbrowser.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbar.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbox.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtoolbutton.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtooltip.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreeview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidget.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtreewidgetitemiterator.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsversion.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qundoview.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwhatsthis.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwidgetaction.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/qwizard.h \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGridLayout \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QComboBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QCheckBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QTextEdit \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QProgressBar \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QScrollArea \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QGroupBox \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QSpinBox \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonDocument \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonObject \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QJsonArray \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QClipboard \
		C:/Qt/6.9.1/mingw_64/include/QtWidgets/QMessageBox \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		C:/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		C:/Qt/6.9.1/mingw_64/include/QtGui/QMouseEvent
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\TetrisPuzzleGame.o ..\TetrisPuzzleGame.cpp

debug/moc_TetrisPuzzleGame.o: debug/moc_TetrisPuzzleGame.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o debug\moc_TetrisPuzzleGame.o debug\moc_TetrisPuzzleGame.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

.SUFFIXES:

