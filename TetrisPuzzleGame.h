#ifndef TETRISPUZZLEGAME_H
#define TETRISPUZZLEGAME_H

#include <QtWidgets>
#include <QApplication>
#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QLabel>
#include <QComboBox>
#include <QCheckBox>
#include <QTextEdit>
#include <QProgressBar>
#include <QScrollArea>
#include <QGroupBox>
#include <QSpinBox>
#include <QTimer>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QClipboard>
#include <QMessageBox>
#include <QThread>
#include <QMutex>
#include <QDateTime>
#include <QMouseEvent>
#include <QEvent>
#include <vector>
#include <map>
#include <set>
#include <string>

// Forward declaration for QVariant registration
Q_DECLARE_METATYPE(std::vector<std::vector<int>>)

struct TetrisShape {
    std::vector<std::vector<std::pair<int, int>>> rotations;
};

struct Solution {
    std::vector<std::vector<int>> board;
    std::string algorithm;
    int step;
    qint64 timestamp;
    std::map<std::string, int> piecesUsed;
    bool isComplete;
    std::string hash;
    int score;
    int filledCells;
    int requiredCovered;
    int forbiddenViolated;
    bool isValid;
};

class BoardCell : public QPushButton {
    Q_OBJECT

public:
    BoardCell(int row, int col, QWidget* parent = nullptr);
    void updateDisplay(int value, const std::map<std::string, QColor>& pieceColors);
    
signals:
    void leftClicked(int row, int col);
    void rightClicked(int row, int col);
    void doubleClicked(int row, int col);

protected:
    void mousePressEvent(QMouseEvent* event) override;
    void mouseDoubleClickEvent(QMouseEvent* event) override;

private:
    int m_row;
    int m_col;
};

class SolverThread : public QThread {
    Q_OBJECT

public:
    SolverThread(QObject* parent = nullptr);
    void setGameData(const std::vector<std::vector<int>>& board,
                     const std::map<std::string, int>& pieceCounts,
                     const std::map<std::string, TetrisShape>& shapes,
                     bool showProcess);
    void stopSolving();

signals:
    void solutionFound(const std::vector<std::vector<int>>& board, 
                      const std::string& algorithm, int step,
                      const std::map<std::string, int>& piecesUsed);
    void statusUpdate(const QString& status, int steps, const QString& piece, const QString& algorithm);
    void solvingFinished();

protected:
    void run() override;

private:
    bool canPlacePiece(const std::vector<std::vector<int>>& board,
                      const std::vector<std::pair<int, int>>& shape,
                      int startRow, int startCol, int pieceId);
    void placePiece(std::vector<std::vector<int>>& board,
                   const std::vector<std::pair<int, int>>& shape,
                   int startRow, int startCol, int pieceId);
    void removePiece(std::vector<std::vector<int>>& board,
                    const std::vector<std::pair<int, int>>& shape,
                    int startRow, int startCol);
    void backtrackSolve(std::vector<std::vector<int>>& board,
                       std::vector<std::string>& pieces,
                       std::map<std::string, int>& usedCounts,
                       int& step);
    std::string generateBoardHash(const std::vector<std::vector<int>>& board);

    std::vector<std::vector<int>> m_originalBoard;
    std::map<std::string, int> m_pieceCounts;
    std::map<std::string, TetrisShape> m_tetrisShapes;
    bool m_showProcess;
    bool m_stopRequested;
    int m_boardSize;
    QMutex m_mutex;
};

class TetrisPuzzleGame : public QMainWindow {
    Q_OBJECT

public:
    TetrisPuzzleGame(QWidget* parent = nullptr);
    ~TetrisPuzzleGame();

private slots:
    void onBoardSizeChanged();
    void onShowProcessToggled();
    void onPieceCountChanged();
    void onCellClicked(int row, int col, int button); // 1=left, 2=right, 3=double
    void startSolving();
    void stopSolving();
    void clearAllMarks();
    void importBoard();
    void exportBoard();
    void onSolutionFound(const std::vector<std::vector<int>>& board,
                        const std::string& algorithm, int step,
                        const std::map<std::string, int>& piecesUsed);
    void onStatusUpdate(const QString& status, int steps, const QString& piece, const QString& algorithm);
    void onSolvingFinished();
    void copySolutionToMainBoard(const std::vector<std::vector<int>>& solutionBoard);

protected:
    bool eventFilter(QObject* obj, QEvent* event) override;

private:
    void setupUI();
    void setupFloatingControls();
    void setupControlPanel();
    void setupMainGameArea();
    void setupSolutionsArea();
    void initBoard();
    void createBoard();
    void updateBoardDisplay();
    void updateCellDisplay(int row, int col);
    void clearSolutions();
    void processAllSolutions();
    void updateStatistics();
    void updateProbabilityHeatmap();
    void updateSolutionsDisplay();
    void updateProgress(int percentage, const QString& text);
    
    // Helper functions
    std::string generateBoardHash(const std::vector<std::vector<int>>& board);
    bool isCompleteSolution(const std::vector<std::vector<int>>& board,
                           const std::map<std::string, int>& piecesUsed);
    std::map<std::string, int> calculateUsedPieces(const std::vector<std::vector<int>>& board);
    Solution evaluateSolution(const std::vector<std::vector<int>>& board);
    void addRawSolution(const std::vector<std::vector<int>>& board,
                       const std::string& algorithm, int step,
                       const std::map<std::string, int>& piecesUsed);

    // UI Components
    QWidget* m_centralWidget;
    QHBoxLayout* m_mainLayout;
    
    // Floating controls
    QWidget* m_floatingControls;
    QComboBox* m_boardSizeCombo;
    std::map<std::string, std::vector<QPushButton*>> m_pieceCountButtons;
    
    // Control panel
    QWidget* m_controlPanel;
    QCheckBox* m_showProcessCheck;
    QTextEdit* m_importTextEdit;
    QLabel* m_statusMain;
    QLabel* m_statusSteps;
    QLabel* m_statusPiece;
    QLabel* m_statusAlgorithm;
    QProgressBar* m_progressBar;
    QLabel* m_progressText;
    
    // Main game area
    QWidget* m_mainGameArea;
    QLabel* m_modeIndicator;
    QWidget* m_gameBoardWidget;
    QGridLayout* m_boardLayout;
    std::vector<std::vector<BoardCell*>> m_boardCells;
    
    // Solutions area
    QWidget* m_solutionsArea;
    QLabel* m_totalSolutionsLabel;
    QLabel* m_validSolutionsLabel;
    QLabel* m_duplicatesRemovedLabel;
    QLabel* m_averageCoverageLabel;
    QWidget* m_probabilityHeatmap;
    QScrollArea* m_solutionsScrollArea;
    QWidget* m_solutionsGrid;
    QLabel* m_solutionsStats;
    
    // Game data
    int m_boardSize;
    std::vector<std::vector<int>> m_board;
    std::vector<std::vector<int>> m_solutionBoard;
    bool m_solving;
    bool m_showProcess;
    std::string m_currentAlgorithm;
    
    // Solutions data
    std::vector<Solution> m_rawSolutions;
    std::vector<Solution> m_uniqueSolutions;
    std::vector<Solution> m_completeSolutions;
    int m_duplicatesFound;
    std::vector<std::vector<int>> m_cellCoverage;
    
    // Tetris shapes and piece data
    std::map<std::string, TetrisShape> m_tetrisShapes;
    std::map<std::string, int> m_pieceCounts;
    std::map<std::string, QColor> m_pieceColors;
    
    // Solver thread
    SolverThread* m_solverThread;
};

#endif // TETRISPUZZLEGAME_H
