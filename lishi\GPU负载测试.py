#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU负载测试 - 验证GPU是否真正被使用
"""

import time
import threading
import subprocess
import os

def monitor_gpu_usage():
    """监控GPU使用率"""
    print("🔍 开始监控GPU使用率...")
    print("💡 请在另一个终端运行: nvidia-smi -l 1")
    print("或者打开任务管理器 -> 性能 -> GPU")
    
    gpu_usage_data = []
    
    def check_gpu():
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=utilization.gpu', '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                usage = int(result.stdout.strip())
                gpu_usage_data.append(usage)
                return usage
        except:
            pass
        return 0
    
    # 监控线程
    monitoring = True
    
    def monitor_thread():
        while monitoring:
            usage = check_gpu()
            if usage > 0:
                print(f"📊 GPU使用率: {usage}%")
            time.sleep(1)
    
    monitor = threading.Thread(target=monitor_thread, daemon=True)
    monitor.start()
    
    return lambda: setattr(monitor_thread, 'monitoring', False), gpu_usage_data

def test_gpu_intensive_operations():
    """测试GPU密集型操作"""
    print("\n🔥 测试GPU密集型操作...")
    
    try:
        import cupy as cp
        
        print("1️⃣ 大型矩阵运算...")
        start_time = time.time()
        
        # 创建大型矩阵
        size = 3000
        a = cp.random.random((size, size), dtype=cp.float32)
        b = cp.random.random((size, size), dtype=cp.float32)
        
        print(f"   创建 {size}x{size} 矩阵")
        
        # 矩阵乘法
        c = cp.dot(a, b)
        cp.cuda.Stream.null.synchronize()  # 等待GPU完成
        
        matrix_time = time.time() - start_time
        print(f"   矩阵乘法完成: {matrix_time:.2f}秒")
        
        print("2️⃣ 并行数组操作...")
        start_time = time.time()
        
        # 大量并行操作
        for i in range(100):
            d = cp.sin(a) + cp.cos(b)
            e = cp.exp(d * 0.1)
            f = cp.sum(e, axis=1)
        
        cp.cuda.Stream.null.synchronize()
        
        array_time = time.time() - start_time
        print(f"   并行操作完成: {array_time:.2f}秒")
        
        print("3️⃣ 内存密集型操作...")
        start_time = time.time()
        
        # 大量内存分配和释放
        arrays = []
        for i in range(50):
            arr = cp.random.random((1000, 1000), dtype=cp.float32)
            arrays.append(arr)
        
        # 清理
        del arrays
        cp.get_default_memory_pool().free_all_blocks()
        
        memory_time = time.time() - start_time
        print(f"   内存操作完成: {memory_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        return False

def test_puzzle_solver_gpu_load():
    """测试拼图求解器的GPU负载"""
    print("\n🧩 测试拼图求解器GPU负载...")
    
    # 您的测试棋盘
    board = [
        [0, 0, 0, 0, 0, 1, 2, 0],
        [0, 0, 0, 0, 0, 1, 1, 0],
        [0, 0, 0, 0, 0, 0, 1, 2],
        [0, 0, 0, 0, 0, 0, 1, 0],
        [0, 0, 0, 0, 0, 0, 2, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 2, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0]
    ]
    
    pieces = {
        "T": 1,
        "田": 0,
        "横杠竖条": 0,
        "Z": 1,
        "L": 1
    }
    
    try:
        from 简化GPU加速回溯法 import SimplifiedGPUBacktrackSolver

        solver = SimplifiedGPUBacktrackSolver()
        
        if not solver.gpu_available:
            print("❌ GPU不可用，无法测试GPU负载")
            return False
        
        print("🚀 开始GPU加速求解...")
        print("💡 现在应该能看到GPU使用率上升")
        
        start_time = time.time()
        solution_steps, final_board = solver.solve(board, pieces, max_parallel=5000)
        solve_time = time.time() - start_time
        
        print(f"✅ 求解完成: {solve_time:.3f}秒")
        print(f"找到解决方案: {'是' if solution_steps else '否'}")
        
        return True
        
    except ImportError:
        print("❌ 真正GPU加速求解器不可用")
        return False
    except Exception as e:
        print(f"❌ GPU求解测试失败: {e}")
        return False

def create_gpu_stress_test():
    """创建GPU压力测试"""
    print("\n💪 GPU压力测试...")
    
    try:
        import cupy as cp
        
        print("🔥 启动GPU压力测试（持续30秒）...")
        print("💡 现在GPU使用率应该接近100%")
        
        start_time = time.time()
        
        while time.time() - start_time < 30:  # 运行30秒
            # 大型矩阵运算
            a = cp.random.random((2000, 2000), dtype=cp.float32)
            b = cp.random.random((2000, 2000), dtype=cp.float32)
            c = cp.dot(a, b)
            
            # 复杂数学运算
            d = cp.sin(a) + cp.cos(b) + cp.tan(c * 0.1)
            e = cp.exp(d * 0.01)
            
            # FFT变换
            f = cp.fft.fft2(e)
            g = cp.fft.ifft2(f)
            
            # 清理部分内存
            del a, b, c, d, e, f, g
            
            if int(time.time() - start_time) % 5 == 0:
                elapsed = int(time.time() - start_time)
                print(f"   压力测试进行中... {elapsed}/30秒")
        
        print("✅ GPU压力测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GPU压力测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎮 GPU负载测试工具")
    print("=" * 60)
    print("🎯 目标: 验证GPU是否真正被使用")
    print("💡 请同时监控GPU使用率:")
    print("   - 方法1: nvidia-smi -l 1")
    print("   - 方法2: 任务管理器 -> 性能 -> GPU")
    print("=" * 60)
    
    # 启动GPU监控
    stop_monitor, usage_data = monitor_gpu_usage()
    
    try:
        # 测试1: 基础GPU操作
        print("\n🧪 测试1: 基础GPU密集型操作")
        basic_ok = test_gpu_intensive_operations()
        
        time.sleep(2)
        
        # 测试2: 拼图求解器GPU负载
        print("\n🧪 测试2: 拼图求解器GPU负载")
        puzzle_ok = test_puzzle_solver_gpu_load()
        
        time.sleep(2)
        
        # 测试3: GPU压力测试
        choice = input("\n是否进行GPU压力测试? (y/n): ").lower().strip()
        if choice == 'y':
            stress_ok = create_gpu_stress_test()
        else:
            stress_ok = True
        
        # 生成报告
        print("\n📋 测试报告")
        print("=" * 60)
        
        tests = [
            ("基础GPU操作", basic_ok),
            ("拼图求解器", puzzle_ok),
            ("压力测试", stress_ok if choice == 'y' else None)
        ]
        
        for test_name, result in tests:
            if result is None:
                status = "⏭️ 跳过"
            elif result:
                status = "✅ 通过"
            else:
                status = "❌ 失败"
            print(f"{test_name:15s}: {status}")
        
        # GPU使用率分析
        if usage_data:
            max_usage = max(usage_data)
            avg_usage = sum(usage_data) / len(usage_data)
            print(f"\n📊 GPU使用率统计:")
            print(f"最高使用率: {max_usage}%")
            print(f"平均使用率: {avg_usage:.1f}%")
            
            if max_usage > 50:
                print("✅ GPU负载正常，确实在使用GPU计算")
            elif max_usage > 10:
                print("⚠️ GPU有一定负载，但可能不够充分")
            else:
                print("❌ GPU负载很低，可能没有真正使用GPU")
        
        print("\n💡 建议:")
        if not basic_ok:
            print("1. 检查CuPy安装: pip install cupy-cuda11x")
            print("2. 检查CUDA驱动")
        elif not puzzle_ok:
            print("1. 拼图求解器可能没有真正使用GPU")
            print("2. 尝试增加并行度参数")
            print("3. 检查求解器实现")
        else:
            print("1. GPU加速工作正常")
            print("2. 可以在网页中使用GPU加速功能")
        
    finally:
        stop_monitor()
    
    print(f"\n🎯 下一步:")
    print("1. 启动GPU网页服务: python GPU回溯法网页服务.py")
    print("2. 在网页中测试您的8x8棋盘")
    print("3. 观察GPU使用率变化")

if __name__ == "__main__":
    main()
