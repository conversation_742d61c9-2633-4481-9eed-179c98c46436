#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复PyTorch ReduceLROnPlateau verbose参数兼容性问题
"""

import os
import re

def fix_verbose_issue():
    """修复verbose参数问题"""
    print("🔧 修复PyTorch ReduceLROnPlateau verbose参数问题")
    print("=" * 60)
    
    # 需要修复的文件列表
    files_to_fix = [
        "GPU深度学习训练.py",
        "深度学习拼图破解器.py",
        "一键深度学习训练.py"
    ]
    
    fixed_count = 0
    
    for filename in files_to_fix:
        if not os.path.exists(filename):
            print(f"⚠️ 文件不存在: {filename}")
            continue
        
        try:
            # 读取文件
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复模式1: ReduceLROnPlateau(..., verbose=True)
            pattern1 = r'ReduceLROnPlateau\([^)]*verbose=True[^)]*\)'
            matches1 = re.findall(pattern1, content)
            
            for match in matches1:
                # 移除verbose=True参数
                fixed_match = re.sub(r',\s*verbose=True', '', match)
                fixed_match = re.sub(r'verbose=True,\s*', '', fixed_match)
                fixed_match = re.sub(r'verbose=True', '', fixed_match)
                content = content.replace(match, fixed_match)
                print(f"  🔧 修复: {match[:50]}...")
            
            # 修复模式2: 单独的verbose=True参数
            content = re.sub(r',\s*verbose=True', '', content)
            content = re.sub(r'verbose=True,\s*', '', content)
            
            # 检查是否有修改
            if content != original_content:
                # 写回文件
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ {filename} 修复完成")
                fixed_count += 1
            else:
                print(f"✅ {filename} 无需修复")
                
        except Exception as e:
            print(f"❌ 修复 {filename} 失败: {e}")
    
    print(f"\n📊 修复总结: {fixed_count} 个文件已修复")
    return fixed_count > 0

def test_fix():
    """测试修复是否成功"""
    print("\n🧪 测试修复结果")
    print("=" * 60)
    
    try:
        import torch
        import torch.optim as optim
        from torch.optim.lr_scheduler import ReduceLROnPlateau
        
        # 创建测试模型
        model = torch.nn.Linear(1, 1)
        optimizer = optim.Adam(model.parameters())
        
        # 测试不带verbose参数
        try:
            scheduler = ReduceLROnPlateau(optimizer, mode='max', factor=0.8, patience=20)
            print("✅ ReduceLROnPlateau创建成功（无verbose参数）")
            return True
        except Exception as e:
            print(f"❌ ReduceLROnPlateau创建失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def create_backup():
    """创建备份"""
    print("💾 创建文件备份")
    print("=" * 60)
    
    import shutil
    import datetime
    
    files_to_backup = [
        "GPU深度学习训练.py",
        "深度学习拼图破解器.py", 
        "一键深度学习训练.py"
    ]
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backup_{timestamp}"
    
    try:
        os.makedirs(backup_dir, exist_ok=True)
        
        for filename in files_to_backup:
            if os.path.exists(filename):
                backup_path = os.path.join(backup_dir, filename)
                shutil.copy2(filename, backup_path)
                print(f"✅ 备份: {filename} → {backup_path}")
        
        print(f"📁 备份目录: {backup_dir}")
        return backup_dir
        
    except Exception as e:
        print(f"❌ 备份失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 PyTorch ReduceLROnPlateau verbose参数快速修复工具")
    print("=" * 80)
    print("🎯 解决错误: ReduceLROnPlateau.__init__() got an unexpected keyword argument 'verbose'")
    print("=" * 80)
    
    # 1. 创建备份
    backup_dir = create_backup()
    if backup_dir:
        print(f"✅ 文件已备份到: {backup_dir}")
    
    # 2. 修复问题
    success = fix_verbose_issue()
    
    # 3. 测试修复
    if success:
        test_success = test_fix()
        
        if test_success:
            print("\n🎉 修复成功！")
            print("✅ verbose参数问题已解决")
            print("🚀 现在可以正常运行训练脚本了")
            
            print("\n📋 下一步:")
            print("   python 一键深度学习训练.py")
            print("   或")
            print("   python GPU深度学习训练.py")
        else:
            print("\n⚠️ 修复可能不完整")
            print("💡 建议手动检查代码")
    else:
        print("\n✅ 所有文件都已经是兼容的")
    
    print(f"\n📁 如需恢复，备份文件在: {backup_dir}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"\n❌ 修复过程出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        input("\n按回车键退出...")
