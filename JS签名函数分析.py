#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JS签名函数分析工具
根据JS代码片段分析ki函数的可能实现
"""

import hashlib
import hmac
import time
import json

def analyze_ki_function():
    """分析ki函数的可能实现"""
    print("🔍 分析ki函数签名算法")
    print("=" * 60)
    
    print("从JS代码可以看出:")
    print("1. 使用 Date.now() 获取时间戳（毫秒级）")
    print("2. ki函数接收参数: {phone: 手机号, timestamp: 时间戳}")
    print("3. 返回值作为sign参数使用")
    print()
    
    # 模拟真实数据
    phone = "18699133341"
    # 由于我们不知道确切的时间戳，尝试一些可能的值
    
    # 已知的其他参数
    activity_id = "625"
    channel = "bac27baa773c5bc2d3060edeb9881c64"
    target_sign = "0aa2427fbb22fe6308893b59926b700e"
    
    print(f"已知参数:")
    print(f"  手机号: {phone}")
    print(f"  活动ID: {activity_id}")
    print(f"  渠道: {channel}")
    print(f"  目标签名: {target_sign}")
    print()
    
    # 尝试不同的时间戳（最近的时间范围）
    current_time = int(time.time() * 1000)  # 当前毫秒时间戳
    
    print("🕐 尝试不同时间戳的ki函数实现...")
    
    # 常见的密钥
    possible_secrets = [
        'secret', 'key', 'app_secret', 'api_secret',
        'unicom', 'wostore', 'wo_activity', 'huodong',
        '123456', 'abcdef123456', 'mobile_secret',
        'ki_secret', 'sign_secret', 'wo_secret'
    ]
    
    # 测试最近24小时内的时间戳（每小时一个点）
    for hours_ago in range(24):
        test_timestamp = current_time - (hours_ago * 3600 * 1000)
        
        print(f"\n测试时间戳: {test_timestamp} ({time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(test_timestamp/1000))})")
        
        for secret in possible_secrets:
            # 模式1: MD5(phone + timestamp + secret)
            test_string = f"{phone}{test_timestamp}{secret}"
            sign = hashlib.md5(test_string.encode()).hexdigest()
            if sign == target_sign:
                print(f"✅ 找到匹配! ki函数实现:")
                print(f"   算法: MD5(phone + timestamp + secret)")
                print(f"   密钥: {secret}")
                print(f"   时间戳: {test_timestamp}")
                print(f"   签名字符串: {test_string}")
                generate_ki_function_code("MD5(phone + timestamp + secret)", secret)
                return True
            
            # 模式2: MD5(timestamp + phone + secret)
            test_string = f"{test_timestamp}{phone}{secret}"
            sign = hashlib.md5(test_string.encode()).hexdigest()
            if sign == target_sign:
                print(f"✅ 找到匹配! ki函数实现:")
                print(f"   算法: MD5(timestamp + phone + secret)")
                print(f"   密钥: {secret}")
                print(f"   时间戳: {test_timestamp}")
                print(f"   签名字符串: {test_string}")
                generate_ki_function_code("MD5(timestamp + phone + secret)", secret)
                return True
            
            # 模式3: MD5(phone + timestamp + activityId + secret)
            test_string = f"{phone}{test_timestamp}{activity_id}{secret}"
            sign = hashlib.md5(test_string.encode()).hexdigest()
            if sign == target_sign:
                print(f"✅ 找到匹配! ki函数实现:")
                print(f"   算法: MD5(phone + timestamp + activityId + secret)")
                print(f"   密钥: {secret}")
                print(f"   时间戳: {test_timestamp}")
                print(f"   签名字符串: {test_string}")
                generate_ki_function_code("MD5(phone + timestamp + activityId + secret)", secret)
                return True
            
            # 模式4: HMAC-MD5
            try:
                sign = hmac.new(secret.encode(), f"{phone}{test_timestamp}".encode(), hashlib.md5).hexdigest()
                if sign == target_sign:
                    print(f"✅ 找到匹配! ki函数实现:")
                    print(f"   算法: HMAC-MD5(phone + timestamp)")
                    print(f"   密钥: {secret}")
                    print(f"   时间戳: {test_timestamp}")
                    generate_ki_function_code("HMAC-MD5", secret)
                    return True
            except:
                pass
    
    print("❌ 未找到匹配的时间戳和算法组合")
    return False

def generate_ki_function_code(algorithm, secret):
    """生成ki函数的Python实现代码"""
    print(f"\n📝 ki函数的Python实现:")
    print("=" * 40)
    
    if "MD5(phone + timestamp + secret)" in algorithm:
        code = f'''
def ki(params):
    """
    模拟JS中的ki函数
    params: {{"phone": "手机号", "timestamp": 时间戳}}
    """
    import hashlib
    
    phone = params["phone"]
    timestamp = str(params["timestamp"])
    secret = "{secret}"
    
    sign_string = f"{{phone}}{{timestamp}}{{secret}}"
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    
    return sign

# 使用示例
import time
params = {{
    "phone": "18699133341",
    "timestamp": int(time.time() * 1000)  # 毫秒时间戳
}}
sign = ki(params)
print(f"生成的签名: {{sign}}")
'''
    
    elif "MD5(timestamp + phone + secret)" in algorithm:
        code = f'''
def ki(params):
    import hashlib
    
    phone = params["phone"]
    timestamp = str(params["timestamp"])
    secret = "{secret}"
    
    sign_string = f"{{timestamp}}{{phone}}{{secret}}"
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    
    return sign
'''
    
    elif "HMAC-MD5" in algorithm:
        code = f'''
def ki(params):
    import hmac
    import hashlib
    
    phone = params["phone"]
    timestamp = str(params["timestamp"])
    secret = "{secret}"
    
    sign_string = f"{{phone}}{{timestamp}}"
    sign = hmac.new(secret.encode(), sign_string.encode(), hashlib.md5).hexdigest()
    
    return sign
'''
    
    else:
        code = "# 请根据具体算法实现"
    
    print(code)

def suggest_js_analysis():
    """建议如何进一步分析JS代码"""
    print(f"\n💡 进一步分析JS代码的建议:")
    print("=" * 40)
    
    print("1. 查找ki函数定义:")
    print("   - 在JS文件中搜索 'function ki' 或 'ki=' 或 'ki:function'")
    print("   - 查找 'const ki' 或 'let ki' 或 'var ki'")
    print()
    
    print("2. 常见的JS签名函数模式:")
    print("   - CryptoJS.MD5(string).toString()")
    print("   - CryptoJS.HmacMD5(string, key).toString()")
    print("   - btoa() 或 atob() Base64编码")
    print("   - 自定义哈希函数")
    print()
    
    print("3. 查找相关变量:")
    print("   - 搜索 'ACT_CODE' 的值")
    print("   - 搜索 'channelId' 的值")
    print("   - 查找可能的密钥常量")
    print()
    
    print("4. 浏览器调试技巧:")
    print("   - 在ki函数处设置断点")
    print("   - 在console中直接调用ki函数测试")
    print("   - 查看函数的输入输出参数")
    print()
    
    print("5. 如果找到ki函数，请提供:")
    print("   - ki函数的完整代码")
    print("   - ACT_CODE和channelId的值")
    print("   - 任何相关的常量定义")

def test_with_current_timestamp():
    """使用当前时间戳测试"""
    print(f"\n🧪 使用当前时间戳测试ki函数")
    print("=" * 40)
    
    phone = "18699133341"
    current_timestamp = int(time.time() * 1000)
    
    print(f"当前时间戳: {current_timestamp}")
    print(f"对应时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_timestamp/1000))}")
    
    # 生成一些可能的签名供参考
    secrets = ['secret', 'key', 'unicom', 'wostore']
    
    print(f"\n可能的签名值:")
    for secret in secrets:
        test_string = f"{phone}{current_timestamp}{secret}"
        sign = hashlib.md5(test_string.encode()).hexdigest()
        print(f"  MD5({phone}+{current_timestamp}+{secret}) = {sign}")

if __name__ == "__main__":
    print("🔐 JS签名函数分析工具")
    print("=" * 60)
    print("分析目标: ki({phone:this.uMobile,timestamp:t}) 函数")
    print()
    
    # 尝试分析ki函数
    found = analyze_ki_function()
    
    if not found:
        # 提供分析建议
        suggest_js_analysis()
        
        # 测试当前时间戳
        test_with_current_timestamp()
    
    print(f"\n🎯 总结:")
    print("需要找到JS文件中ki函数的具体实现才能完全破解签名算法。")
    print("请在浏览器开发者工具中查找ki函数的定义。")
