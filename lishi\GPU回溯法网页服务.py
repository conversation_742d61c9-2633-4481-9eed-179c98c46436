from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import time
import os
import traceback

app = Flask(__name__)
CORS(app)

class GPUBacktrackWebService:
    """GPU回溯法网页服务"""
    
    def __init__(self):
        self.solver = None
        self.gpu_available = False
        self.solver_type = "None"
        self.init_gpu_solver()
    
    def init_gpu_solver(self):
        """初始化GPU求解器"""
        print("🔍 检测GPU加速方案...")

        # 尝试重度GPU加速方案
        try:
            from 重度GPU加速回溯法 import HeavyGPUBacktrackSolver
            self.solver = HeavyGPUBacktrackSolver()

            if hasattr(self.solver, 'gpu_available') and self.solver.gpu_available:
                self.gpu_available = True
                self.solver_type = "重度GPU加速"
                print("✅ 重度GPU加速已启用")
                print("💡 现在GPU使用率应该接近100%")
                return
            else:
                print("⚠️ 重度GPU加速使用CPU模式")
                self.gpu_available = False
                self.solver_type = "重度GPU加速(CPU)"
                return

        except ImportError:
            print("⚠️ 重度GPU加速求解器不可用")
        except Exception as e:
            print(f"⚠️ 重度GPU加速初始化失败: {e}")

        # 尝试简化GPU加速方案
        try:
            from 简化GPU加速回溯法 import SimplifiedGPUBacktrackSolver
            self.solver = SimplifiedGPUBacktrackSolver()

            if hasattr(self.solver, 'gpu_available') and self.solver.gpu_available:
                self.gpu_available = True
                self.solver_type = "简化GPU加速"
                print("✅ 简化GPU加速已启用")
                print("💡 现在GPU应该有明显负载")
                return
            else:
                print("⚠️ 简化GPU加速使用CPU模式")
                self.gpu_available = False
                self.solver_type = "简化GPU加速(CPU)"
                return

        except ImportError:
            print("⚠️ 简化GPU加速求解器不可用")
        except Exception as e:
            print(f"⚠️ 简化GPU加速初始化失败: {e}")

        # 尝试修复版CuPy方案
        try:
            from 修复版CuPy加速回溯法 import FixedCuPyBacktrackSolver
            self.solver = FixedCuPyBacktrackSolver()

            if hasattr(self.solver, 'gpu_available') and self.solver.gpu_available:
                self.gpu_available = True
                self.solver_type = "修复版CuPy"
                print("✅ 修复版CuPy GPU加速已启用")
                return
            else:
                print("⚠️ 修复版CuPy使用CPU模式")
                self.gpu_available = False
                self.solver_type = "修复版CuPy(CPU)"
                return

        except ImportError:
            print("⚠️ 修复版CuPy求解器不可用")
        except Exception as e:
            print(f"⚠️ 修复版CuPy初始化失败: {e}")

        # 尝试原版CuPy方案
        try:
            import cupy as cp
            test_array = cp.array([1, 2, 3])
            _ = cp.sum(test_array)

            from CuPy加速回溯法 import CuPyBacktrackSolver
            self.solver = CuPyBacktrackSolver()

            if hasattr(self.solver, 'gpu_available') and self.solver.gpu_available:
                self.gpu_available = True
                self.solver_type = "CuPy"
                print("✅ 原版CuPy GPU加速已启用")
                return
            else:
                print("⚠️ 原版CuPy求解器GPU不可用")

        except ImportError:
            print("⚠️ CuPy未安装")
        except Exception as e:
            print(f"⚠️ 原版CuPy初始化失败: {e}")

        # 尝试Numba CUDA方案
        try:
            from numba import cuda
            if cuda.is_available():
                # 测试CUDA是否真正可用
                cuda.detect()  # 检测CUDA设备

                from GPU加速回溯法破解器 import GPUBacktrackSolver
                self.solver = GPUBacktrackSolver()
                self.gpu_available = True
                self.solver_type = "Numba CUDA"
                print("✅ Numba CUDA GPU加速已启用")
                return
            else:
                print("⚠️ CUDA不可用")
        except ImportError:
            print("⚠️ Numba未安装")
        except Exception as e:
            print(f"⚠️ Numba CUDA初始化失败: {e}")

        # CPU备用方案
        print("🖥️ 使用CPU备用方案")
        self.solver = CPUBacktrackSolver()
        self.gpu_available = False
        self.solver_type = "CPU"
    
    def solve_puzzle(self, board, pieces, mode='rotating'):
        """求解拼图"""
        try:
            start_time = time.time()
            
            if self.solver:
                solution_steps, final_board = self.solver.solve(board, pieces, mode)
                solve_time = (time.time() - start_time) * 1000
                
                return {
                    'success': len(solution_steps) > 0,
                    'solution': solution_steps,
                    'final_board': final_board,
                    'time_ms': solve_time,
                    'steps': len(solution_steps),
                    'solver_type': self.solver_type,
                    'gpu_accelerated': self.gpu_available
                }
            else:
                return {
                    'success': False,
                    'error': 'GPU求解器未初始化',
                    'solution': [],
                    'final_board': board,
                    'time_ms': 0,
                    'solver_type': 'None'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f'GPU求解失败: {str(e)}',
                'solution': [],
                'final_board': board,
                'time_ms': 0,
                'solver_type': self.solver_type
            }

class CPUBacktrackSolver:
    """CPU备用回溯求解器"""
    
    def __init__(self):
        self.tetris_shapes = {
            "T": [
                [(0,1), (1,0), (1,1), (1,2)],
                [(0,1), (1,1), (1,2), (2,1)],
                [(0,0), (1,0), (1,1), (2,0)],
                [(0,0), (0,1), (0,2), (1,1)],
                [(0,1), (1,0), (1,1), (2,1)]
            ],
            "田": [[(0,0), (0,1), (1,0), (1,1)]],
            "横杠竖条": [
                [(0,0), (0,1), (0,2), (0,3)],
                [(0,0), (1,0), (2,0), (3,0)]
            ],
            "Z": [
                [(0,0), (0,1), (1,1), (1,2)],
                [(0,1), (1,0), (1,1), (2,0)],
                [(0,1), (0,2), (1,0), (1,1)],
                [(0,0), (1,0), (1,1), (2,1)]
            ],
            "L": [
                [(0,0), (1,0), (2,0), (2,1)],
                [(0,1), (1,1), (2,1), (2,0)],
                [(0,0), (1,0), (1,1), (1,2)],
                [(0,0), (0,1), (1,0), (2,0)],
                [(0,0), (0,1), (0,2), (1,2)]
            ]
        }
    
    def solve(self, board, pieces, mode='rotating'):
        """CPU回溯求解"""
        import copy
        
        board_copy = copy.deepcopy(board)
        pieces_copy = copy.deepcopy(pieces)
        
        solution = self._backtrack(board_copy, pieces_copy)
        
        if solution:
            steps = self._generate_steps(board, solution, pieces)
            return steps, solution
        else:
            return [], board
    
    def _backtrack(self, board, pieces, depth=0):
        """回溯算法"""
        if depth > 20:
            return None
            
        if self._is_solved(board):
            return board
        
        board_size = len(board)
        
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(pieces.keys()).index(piece_name) + 3
            
            for shape in shapes:
                for r in range(board_size):
                    for c in range(board_size):
                        if self._can_place(board, shape, r, c):
                            # 放置方块
                            for dr, dc in shape:
                                board[r + dr][c + dc] = piece_id
                            pieces[piece_name] -= 1
                            
                            # 递归求解
                            result = self._backtrack(board, pieces, depth + 1)
                            if result is not None:
                                return result
                            
                            # 回溯
                            for dr, dc in shape:
                                board[r + dr][c + dc] = 0
                            pieces[piece_name] += 1
        
        return None
    
    def _can_place(self, board, shape, start_r, start_c):
        """检查是否可以放置"""
        board_size = len(board)
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if r < 0 or r >= board_size or c < 0 or c >= board_size:
                return False
            if board[r][c] > 2:
                return False
            if board[r][c] == 2:
                return False
        
        return True
    
    def _is_solved(self, board):
        """检查是否解决"""
        for row in board:
            for cell in row:
                if cell == 1:
                    return False
        return True
    
    def _generate_steps(self, original_board, solution_board, pieces):
        """生成解决方案步骤"""
        steps = []
        piece_names = list(pieces.keys())
        
        for i in range(len(solution_board)):
            for j in range(len(solution_board[0])):
                if solution_board[i][j] > 2 and original_board[i][j] <= 2:
                    piece_id = solution_board[i][j] - 3
                    if piece_id < len(piece_names):
                        steps.append({
                            'step': len(steps) + 1,
                            'piece': piece_names[piece_id],
                            'position': (i, j),
                            'rotation': 0
                        })
        
        return steps

# 创建服务实例
gpu_service = GPUBacktrackWebService()

@app.route('/')
def index():
    """返回网页"""
    return send_from_directory('.', 'cainiao688.html')

@app.route('/api/gpu_solve', methods=['POST'])
def api_gpu_solve():
    """GPU加速求解API"""
    try:
        data = request.get_json()
        
        if not data or 'board' not in data or 'pieces' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必要参数: board 和 pieces'
            }), 400
        
        board = data['board']
        pieces = data['pieces']
        mode = data.get('mode', 'rotating')
        
        result = gpu_service.solve_puzzle(board, pieces, mode)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}',
            'traceback': traceback.format_exc()
        }), 500

@app.route('/api/gpu_status', methods=['GET'])
def api_gpu_status():
    """获取GPU状态"""
    return jsonify({
        'gpu_available': gpu_service.gpu_available,
        'solver_type': gpu_service.solver_type,
        'ready': gpu_service.solver is not None,
        'acceleration': 'GPU' if gpu_service.gpu_available else 'CPU'
    })

@app.route('/api/benchmark', methods=['POST'])
def api_benchmark():
    """性能基准测试"""
    test_cases = [
        {
            'name': '3x3 简单',
            'board': [[1, 0, 0], [0, 0, 0], [0, 0, 2]],
            'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '4x4 中等',
            'board': [[1, 0, 0, 1], [0, 0, 0, 0], [0, 0, 0, 0], [2, 0, 0, 2]],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
        }
    ]
    
    results = []
    total_time = 0
    success_count = 0
    
    for case in test_cases:
        start_time = time.time()
        result = gpu_service.solve_puzzle(case['board'], case['pieces'])
        solve_time = time.time() - start_time
        total_time += solve_time
        
        if result['success']:
            success_count += 1
        
        results.append({
            'name': case['name'],
            'success': result['success'],
            'time_ms': solve_time * 1000,
            'steps': result.get('steps', 0),
            'solver_type': result.get('solver_type', 'Unknown')
        })
    
    return jsonify({
        'success': True,
        'results': results,
        'summary': {
            'success_rate': success_count / len(test_cases),
            'average_time_ms': (total_time / len(test_cases)) * 1000,
            'total_tests': len(test_cases),
            'passed_tests': success_count,
            'gpu_accelerated': gpu_service.gpu_available,
            'solver_type': gpu_service.solver_type
        }
    })

if __name__ == '__main__':
    print("🚀 GPU加速回溯法拼图破解器 Web服务")
    print("=" * 60)
    print(f"🖥️ 求解器类型: {gpu_service.solver_type}")
    print(f"⚡ GPU加速: {'启用' if gpu_service.gpu_available else '禁用'}")
    print("🌐 访问地址: http://localhost:5001")
    print("📋 API端点:")
    print("   POST /api/gpu_solve - GPU加速求解")
    print("   GET  /api/gpu_status - GPU状态")
    print("   POST /api/benchmark - 性能测试")
    
    if not gpu_service.gpu_available:
        print("\n💡 GPU加速不可用，建议:")
        print("   1. 安装CuPy: pip install cupy-cuda11x")
        print("   2. 安装Numba: pip install numba")
        print("   3. 检查CUDA驱动")
    
    app.run(debug=True, host='0.0.0.0', port=5001)
