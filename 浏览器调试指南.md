# 🔍 浏览器调试指南 - 找到ki函数实现

## 🎯 目标
找到JS代码中`ki`函数的具体实现，破解签名算法：
```javascript
let t=Date.now(),
e=ki({phone:this.uMobile,timestamp:t});
```

## 🛠️ 调试步骤

### 第一步：打开开发者工具
1. 在目标网页按 `F12` 或右键选择"检查"
2. 切换到 `Sources` 标签页
3. 在左侧文件列表中找到JS文件

### 第二步：搜索ki函数
在 `Sources` 面板中按 `Ctrl+Shift+F` 打开全局搜索，搜索以下关键词：

#### 🔍 搜索关键词
```javascript
// 函数定义
function ki
ki=function
ki:function
const ki
let ki
var ki

// 函数调用
ki({
ki(

// 可能的变量名
ki =
ki:
```

### 第三步：查找相关常量
同时搜索这些相关变量：
```javascript
ACT_CODE
channelId
uMobile
timestamp
sign
```

### 第四步：设置断点调试
1. 找到调用`ki`函数的地方
2. 在该行设置断点（点击行号）
3. 触发发送验证码的操作
4. 当断点触发时，在Console中执行：

```javascript
// 查看ki函数的定义
console.log(ki.toString());

// 查看相关变量
console.log('ACT_CODE:', this.ACT_CODE);
console.log('channelId:', this.channelId);
console.log('uMobile:', this.uMobile);

// 测试ki函数
let testParams = {phone: '18699133341', timestamp: Date.now()};
let testSign = ki(testParams);
console.log('测试签名:', testSign);
```

### 第五步：分析函数实现
当找到`ki`函数后，查看其实现方式：

#### 常见模式1：CryptoJS
```javascript
function ki(params) {
    return CryptoJS.MD5(params.phone + params.timestamp + 'secret').toString();
}
```

#### 常见模式2：HMAC
```javascript
function ki(params) {
    return CryptoJS.HmacMD5(params.phone + params.timestamp, 'secret_key').toString();
}
```

#### 常见模式3：自定义哈希
```javascript
function ki(params) {
    let str = params.phone + params.timestamp + SECRET_KEY;
    return customMD5(str);
}
```

## 🔧 实用技巧

### 技巧1：美化压缩的JS代码
如果JS代码被压缩，在Sources面板中：
1. 找到对应的JS文件
2. 点击左下角的 `{}` 按钮美化代码
3. 重新搜索`ki`函数

### 技巧2：使用Console直接测试
在Console中直接执行：
```javascript
// 查找所有包含ki的全局变量
Object.getOwnPropertyNames(window).filter(name => name.includes('ki'));

// 如果ki是某个对象的方法
for(let key in window) {
    if(typeof window[key] === 'object' && window[key] && window[key].ki) {
        console.log(key, window[key].ki.toString());
    }
}
```

### 技巧3：监控网络请求
1. 切换到 `Network` 标签页
2. 触发发送验证码操作
3. 找到对应的请求
4. 查看请求的 `Initiator` 列，点击可以跳转到发起请求的代码位置

### 技巧4：搜索字符串特征
搜索签名相关的字符串：
```javascript
"sign"
"signature"
"timestamp"
"phone"
"mobile"
```

## 📋 需要收集的信息

当找到`ki`函数后，请收集以下信息：

### 1. 函数完整代码
```javascript
// 完整的ki函数实现
function ki(params) {
    // ... 具体实现
}
```

### 2. 相关常量值
```javascript
ACT_CODE: "具体值"
channelId: "具体值"
SECRET_KEY: "具体值" // 如果有的话
```

### 3. 依赖的库
- 是否使用了CryptoJS
- 是否使用了其他加密库
- 是否有自定义的哈希函数

### 4. 参数格式
```javascript
// ki函数接收的参数格式
{
    phone: "手机号",
    timestamp: 时间戳,
    // 可能还有其他参数
}
```

## 🎯 快速验证方法

找到`ki`函数后，在Console中快速验证：

```javascript
// 使用已知数据测试
let testParams = {
    phone: '18699133341',
    timestamp: 1640995200000  // 使用一个固定时间戳
};

let generatedSign = ki(testParams);
console.log('生成的签名:', generatedSign);

// 如果签名是 0aa2427fbb22fe6308893b59926b700e，说明找对了！
```

## 🚨 常见问题

### 问题1：找不到ki函数
- 可能函数名被混淆了，搜索调用模式：`({phone:`, `timestamp:`
- 可能在其他JS文件中，检查所有加载的JS文件

### 问题2：函数代码被混淆
- 使用美化工具格式化代码
- 查找关键字符串如"phone"、"timestamp"

### 问题3：函数使用了外部库
- 查看页面加载的所有JS库
- 特别注意CryptoJS、MD5、HMAC相关的库

## 📝 示例报告格式

找到函数后，请按以下格式提供信息：

```
ki函数实现:
function ki(params) {
    // 具体代码
}

相关常量:
- ACT_CODE: "625"
- channelId: "bac27baa773c5bc2d3060edeb9881c64"
- 其他常量...

测试结果:
- 输入: {phone: '18699133341', timestamp: 1640995200000}
- 输出: '0aa2427fbb22fe6308893b59926b700e'
```

## 🎉 成功后的下一步

一旦找到`ki`函数的实现，我就可以：
1. 编写对应的Python实现
2. 集成到你的联通话费领取工具中
3. 实现自动签名生成功能

现在请按照这个指南去浏览器中查找`ki`函数的具体实现吧！
