import cupy as cp
import numpy as np
import time
from numba import cuda
import math

class TrueGPUBacktrackSolver:
    """真正的GPU加速回溯法求解器"""
    
    def __init__(self):
        self.gpu_available = False
        self.device_info = "CPU"
        self._init_gpu()
        
        # 俄罗斯方块定义（编码为GPU友好格式）
        self.tetris_shapes = self._encode_shapes()
        
    def _init_gpu(self):
        """初始化GPU"""
        try:
            # 测试CuPy
            test_array = cp.array([1, 2, 3])
            result = cp.sum(test_array)
            
            if result == 6:
                self.gpu_available = True
                self.cp = cp
                device = cp.cuda.Device()
                self.device_info = f"GPU-{device.id}"
                print(f"✅ 真正GPU加速初始化成功: {self.device_info}")
                
                # 预热GPU
                self._warmup_gpu()
            else:
                raise Exception("GPU计算验证失败")
                
        except Exception as e:
            print(f"⚠️ GPU不可用，使用CPU: {e}")
            self.gpu_available = False
            self.device_info = "CPU"
    
    def _warmup_gpu(self):
        """预热GPU"""
        print("🔥 预热GPU...")
        try:
            # 创建大型矩阵运算来预热GPU
            a = cp.random.random((1000, 1000))
            b = cp.random.random((1000, 1000))
            c = cp.dot(a, b)
            cp.cuda.Stream.null.synchronize()
            print("✅ GPU预热完成")
        except Exception as e:
            print(f"⚠️ GPU预热失败: {e}")
    
    def _encode_shapes(self):
        """编码方块形状为GPU格式"""
        shapes = {
            "T": [
                [(0,1), (1,0), (1,1), (1,2)],
                [(0,1), (1,1), (1,2), (2,1)],
                [(0,0), (1,0), (1,1), (2,0)],
                [(0,0), (0,1), (0,2), (1,1)],
                [(0,1), (1,0), (1,1), (2,1)]
            ],
            "田": [[(0,0), (0,1), (1,0), (1,1)]],
            "横杠竖条": [
                [(0,0), (0,1), (0,2), (0,3)],
                [(0,0), (1,0), (2,0), (3,0)]
            ],
            "Z": [
                [(0,0), (0,1), (1,1), (1,2)],
                [(0,1), (1,0), (1,1), (2,0)],
                [(0,1), (0,2), (1,0), (1,1)],
                [(0,0), (1,0), (1,1), (2,1)]
            ],
            "L": [
                [(0,0), (1,0), (2,0), (2,1)],
                [(0,1), (1,1), (2,1), (2,0)],
                [(0,0), (1,0), (1,1), (1,2)],
                [(0,0), (0,1), (1,0), (2,0)],
                [(0,0), (0,1), (0,2), (1,2)]
            ]
        }
        
        # 转换为GPU数组格式
        gpu_shapes = {}
        for piece_name, rotations in shapes.items():
            piece_arrays = []
            for rotation in rotations:
                # 每个形状最多4个方块，用-1填充
                shape_array = cp.full((4, 2), -1, dtype=cp.int32)
                for i, (r, c) in enumerate(rotation):
                    shape_array[i, 0] = r
                    shape_array[i, 1] = c
                piece_arrays.append(shape_array)
            gpu_shapes[piece_name] = cp.stack(piece_arrays)
        
        return gpu_shapes
    
    def solve(self, board, pieces, mode='rotating', max_parallel=2222222):
        """GPU加速求解"""
        print(f"🚀 启动真正GPU加速回溯法求解")
        print(f"📊 棋盘大小: {len(board)}x{len(board[0])}")
        print(f"🧩 方块配置: {pieces}")
        print(f"🖥️ 计算设备: {self.device_info}")
        print(f"⚡ 并行度: {max_parallel}")
        
        if not self.gpu_available:
            return self._cpu_fallback(board, pieces)
        
        start_time = time.time()
        
        try:
            solution_steps, final_board = self._true_gpu_solve(board, pieces, max_parallel)
        except Exception as e:
            print(f"⚠️ GPU求解失败，切换到CPU: {e}")
            solution_steps, final_board = self._cpu_fallback(board, pieces)
        
        solve_time = time.time() - start_time
        print(f"⚡ 求解完成: {solve_time:.3f}秒")
        
        return solution_steps, final_board
    
    def _true_gpu_solve(self, board, pieces, max_parallel):
        """真正的GPU并行求解"""
        print("🎮 使用GPU并行计算")

        # 先转换为numpy数组，再转换为GPU数组
        board_np = np.array(board, dtype=np.int32)
        board_gpu = cp.asarray(board_np)
        board_size = len(board)
        
        # 生成所有可能的放置组合
        all_placements = self._generate_all_placements_gpu(board_gpu, pieces, max_parallel)
        
        if len(all_placements) == 0:
            return [], board
        
        print(f"🔍 生成GPU并行任务: {len(all_placements)}个")
        
        # GPU并行验证所有组合
        solutions = self._gpu_parallel_validate(board_gpu, all_placements, pieces)
        
        if solutions:
            best_solution = solutions[0]
            steps = self._generate_solution_steps(board, best_solution, pieces)
            return steps, cp.asnumpy(best_solution).tolist()
        
        return [], board
    
    def _generate_all_placements_gpu(self, board_gpu, pieces, max_parallel):
        """在GPU上生成所有可能的放置组合"""
        print("🔄 GPU生成放置组合...")

        board_size = int(board_gpu.shape[0])
        all_placements = []
        
        # 为每种方块生成所有可能的放置
        piece_names = list(pieces.keys())
        
        for piece_name, count in pieces.items():
            if count <= 0:
                continue
            
            piece_id = piece_names.index(piece_name) + 3
            shapes = self.tetris_shapes[piece_name]
            
            # 对每种旋转
            for shape_idx in range(len(shapes)):
                shape = shapes[shape_idx]
                
                # 对每个位置
                for r in range(board_size):
                    for c in range(board_size):
                        # 检查是否可以放置（在GPU上）
                        if self._can_place_gpu(board_gpu, shape, r, c, board_size):
                            placement = {
                                'piece_name': piece_name,
                                'piece_id': piece_id,
                                'shape_idx': shape_idx,
                                'position': (r, c),
                                'shape': shape
                            }
                            all_placements.append(placement)
                            
                            if len(all_placements) >= max_parallel:
                                break
                    if len(all_placements) >= max_parallel:
                        break
                if len(all_placements) >= max_parallel:
                    break
            if len(all_placements) >= max_parallel:
                break
        
        return all_placements
    
    def _can_place_gpu(self, board_gpu, shape, start_r, start_c, board_size):
        """在GPU上检查是否可以放置"""
        try:
            # 转换为CPU进行检查（避免GPU数组访问问题）
            board_cpu = cp.asnumpy(board_gpu)
            shape_cpu = cp.asnumpy(shape)

            for i in range(4):
                if shape_cpu[i, 0] == -1:  # 无效位置
                    break

                r = start_r + int(shape_cpu[i, 0])
                c = start_c + int(shape_cpu[i, 1])

                if r < 0 or r >= board_size or c < 0 or c >= board_size:
                    return False
                if board_cpu[r, c] > 2:  # 已被占用
                    return False
                if board_cpu[r, c] == 2:  # 禁止位置
                    return False

            return True
        except Exception as e:
            print(f"⚠️ GPU放置检查失败: {e}")
            return False
    
    def _gpu_parallel_validate(self, board_gpu, all_placements, pieces):
        """GPU并行验证所有放置组合"""
        print("⚡ GPU并行验证中...")
        
        batch_size = min(1000, len(all_placements))
        board_size = board_gpu.shape[0]
        
        # 分批处理避免内存溢出
        for i in range(0, len(all_placements), batch_size):
            batch = all_placements[i:i+batch_size]
            current_batch_size = len(batch)
            
            print(f"  处理批次 {i//batch_size + 1}: {current_batch_size}个任务")
            
            # 创建批量棋盘
            batch_boards = cp.tile(board_gpu[None, :, :], (current_batch_size, 1, 1))
            
            # 在GPU上应用所有放置
            for j, placement in enumerate(batch):
                self._apply_placement_gpu(batch_boards[j], placement)
            
            # GPU并行检查哪些是有效解决方案
            valid_solutions = self._gpu_check_solutions(batch_boards, pieces, batch)
            
            if valid_solutions:
                return valid_solutions
        
        return []
    
    def _apply_placement_gpu(self, board, placement):
        """在GPU上应用方块放置"""
        shape = placement['shape']
        start_r, start_c = placement['position']
        piece_id = placement['piece_id']
        
        for i in range(4):
            if shape[i, 0] == -1:
                break
            r = start_r + int(shape[i, 0])
            c = start_c + int(shape[i, 1])
            board[r, c] = piece_id
    
    def _gpu_check_solutions(self, batch_boards, pieces, batch_placements):
        """GPU并行检查解决方案"""
        solutions = []
        
        # 检查每个棋盘是否满足必需位置
        for i in range(len(batch_boards)):
            board = batch_boards[i]
            
            # 检查所有必需位置(1)是否被覆盖
            required_covered = cp.all(board != 1)
            
            if required_covered:
                # 这是一个有效解决方案
                solutions.append(board)
                print(f"✅ 找到GPU解决方案!")
                
                # 只返回第一个解决方案
                break
        
        return solutions
    
    def _cpu_fallback(self, board, pieces):
        """CPU备用方案"""
        print("🖥️ 使用CPU备用求解")
        
        # 简单的CPU回溯实现
        import copy
        
        def backtrack(board, pieces, depth=0):
            if depth > 20:
                return None
            
            # 检查是否解决
            if not any(1 in row for row in board):
                return board
            
            board_size = len(board)
            piece_names = list(pieces.keys())
            
            for piece_name, count in pieces.items():
                if count <= 0:
                    continue
                
                # 简化的形状定义
                shapes = {
                    "T": [[(0,1), (1,0), (1,1), (1,2)]],
                    "Z": [[(0,0), (0,1), (1,1), (1,2)]],
                    "L": [[(0,0), (1,0), (2,0), (2,1)]],
                    "田": [[(0,0), (0,1), (1,0), (1,1)]],
                    "横杠竖条": [[(0,0), (0,1), (0,2), (0,3)]]
                }
                
                if piece_name not in shapes:
                    continue
                
                piece_id = piece_names.index(piece_name) + 3
                
                for shape in shapes[piece_name]:
                    for r in range(board_size):
                        for c in range(board_size):
                            # 检查是否可以放置
                            can_place = True
                            for dr, dc in shape:
                                nr, nc = r + dr, c + dc
                                if (nr < 0 or nr >= board_size or nc < 0 or nc >= board_size or
                                    board[nr][nc] > 2 or board[nr][nc] == 2):
                                    can_place = False
                                    break
                            
                            if can_place:
                                # 放置方块
                                new_board = copy.deepcopy(board)
                                for dr, dc in shape:
                                    new_board[r + dr][c + dc] = piece_id
                                
                                new_pieces = pieces.copy()
                                new_pieces[piece_name] -= 1
                                
                                result = backtrack(new_board, new_pieces, depth + 1)
                                if result is not None:
                                    return result
            
            return None
        
        start_time = time.time()
        result = backtrack(copy.deepcopy(board), pieces.copy())
        cpu_time = time.time() - start_time
        
        print(f"⏱️ CPU求解时间: {cpu_time:.3f}秒")
        
        if result:
            steps = self._generate_solution_steps(board, result, pieces)
            return steps, result
        else:
            return [], board
    
    def _generate_solution_steps(self, original_board, solution_board, pieces):
        """生成解决方案步骤"""
        steps = []
        piece_names = list(pieces.keys())
        
        for i in range(len(solution_board)):
            for j in range(len(solution_board[0])):
                if solution_board[i][j] > 2 and original_board[i][j] <= 2:
                    piece_id = solution_board[i][j] - 3
                    if piece_id < len(piece_names):
                        steps.append({
                            'step': len(steps) + 1,
                            'piece': piece_names[piece_id],
                            'position': (i, j),
                            'rotation': 0
                        })
        
        return steps

def test_with_your_board():
    """测试您提供的棋盘"""
    print("🧪 测试您的8x8棋盘")
    print("=" * 60)
    
    # 您的测试数据
    board = [
        [0, 0, 0, 0, 0, 1, 2, 0],
        [0, 0, 0, 0, 0, 1, 1, 0],
        [0, 0, 0, 0, 0, 0, 1, 2],
        [0, 0, 0, 0, 0, 0, 1, 0],
        [0, 0, 0, 0, 0, 0, 2, 0],
        [0, 0, 0, 0, 0, 0, 0, 0],
        [0, 2, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0]
    ]
    
    pieces = {
        "T": 1,
        "田": 0,
        "横杠竖条": 0,
        "Z": 1,
        "L": 1
    }
    
    solver = TrueGPUBacktrackSolver()
    
    print("🚀 开始GPU加速求解...")
    print("💡 请观察GPU使用率（nvidia-smi 或任务管理器）")
    
    start_time = time.time()
    solution_steps, final_board = solver.solve(board, pieces)
    total_time = time.time() - start_time
    
    print(f"\n📊 求解结果:")
    print(f"总时间: {total_time:.3f}秒")
    print(f"找到解决方案: {'是' if solution_steps else '否'}")
    print(f"解决方案步数: {len(solution_steps)}")
    
    if solution_steps:
        print("\n📋 解决方案:")
        for step in solution_steps:
            print(f"  步骤{step['step']}: {step['piece']} → {step['position']}")

if __name__ == "__main__":
    test_with_your_board()
