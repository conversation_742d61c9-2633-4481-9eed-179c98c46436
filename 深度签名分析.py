#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度签名分析 - 针对联通活动请求的详细分析
mobile=18699133341&activityId=625&channel=bac27baa773c5bc2d3060edeb9881c64&sign=0aa2427fbb22fe6308893b59926b700e&regionLimitFlag=1
"""

import hashlib
import hmac
import time
import base64
import urllib.parse
import json

def analyze_unicom_sign():
    """分析联通活动的签名算法"""
    print("🔍 深度分析联通活动签名算法")
    print("=" * 60)
    
    # 真实请求数据
    params = {
        'mobile': '18699133341',
        'activityId': '625', 
        'channel': 'bac27baa773c5bc2d3060edeb9881c64',
        'regionLimitFlag': '1'
    }
    target_sign = '0aa2427fbb22fe6308893b59926b700e'
    
    print("请求参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    print(f"目标签名: {target_sign}")
    print()
    
    # 扩展的密钥列表
    secrets = [
        # 基础密钥
        'secret', 'key', 'app_secret', 'api_secret',
        
        # 联通相关
        'unicom', 'wostore', 'wo_activity', 'huodong', '10155',
        'chinaunicom', 'wo', 'activity_key', 'mobile_secret',
        
        # 数字密钥
        '123456', '888888', '666666', '000000', '111111',
        '12345678', '87654321', 'abcdef123456',
        
        # 可能的业务密钥
        'sms_secret', 'mobile_key', 'activity_secret',
        'huodong_secret', 'wo_secret', 'unicom_secret',
        
        # 基于channel的密钥
        'bac27baa773c5bc2d3060edeb9881c64',
        'channel_secret', 'bac27baa',
        
        # 时间相关
        '2024', '2023', '20240101', '20231201',
        
        # 其他可能
        'default', 'test', 'demo', 'prod', 'production',
        'dev', 'development', 'staging'
    ]
    
    print(f"🔧 测试 {len(secrets)} 个可能的密钥...")
    
    found_matches = []
    
    for i, secret in enumerate(secrets):
        if i % 10 == 0:
            print(f"进度: {i}/{len(secrets)}")
        
        # 测试各种算法组合
        test_cases = [
            # 基础组合
            f"{params['mobile']}{params['activityId']}{secret}",
            f"{secret}{params['mobile']}{params['activityId']}",
            f"{params['mobile']}{secret}{params['activityId']}",
            
            # 包含channel
            f"{params['mobile']}{params['activityId']}{params['channel']}{secret}",
            f"{secret}{params['mobile']}{params['activityId']}{params['channel']}",
            f"{params['mobile']}{params['channel']}{params['activityId']}{secret}",
            
            # 包含regionLimitFlag
            f"{params['mobile']}{params['activityId']}{params['regionLimitFlag']}{secret}",
            f"{params['mobile']}{params['activityId']}{params['channel']}{params['regionLimitFlag']}{secret}",
            
            # 参数名+值的组合
            f"mobile{params['mobile']}activityId{params['activityId']}{secret}",
            f"mobile={params['mobile']}&activityId={params['activityId']}&key={secret}",
            
            # 排序后的组合
            f"{params['activityId']}{params['channel']}{params['mobile']}{params['regionLimitFlag']}{secret}",
            
            # 特殊格式
            f"mobile={params['mobile']}&activityId={params['activityId']}&channel={params['channel']}&regionLimitFlag={params['regionLimitFlag']}&key={secret}",
            
            # 只用部分参数
            f"{params['mobile']}{secret}",
            f"{params['activityId']}{secret}",
            f"{params['channel']}{secret}",
            
            # 反向组合
            f"{secret}{params['activityId']}{params['mobile']}",
            f"{params['activityId']}{params['mobile']}{secret}",
        ]
        
        for test_string in test_cases:
            # MD5测试
            md5_sign = hashlib.md5(test_string.encode()).hexdigest()
            if md5_sign == target_sign:
                found_matches.append({
                    'algorithm': 'MD5',
                    'secret': secret,
                    'sign_string': test_string,
                    'generated_sign': md5_sign
                })
            
            # MD5大写测试
            if md5_sign.upper() == target_sign.upper():
                found_matches.append({
                    'algorithm': 'MD5(uppercase)',
                    'secret': secret,
                    'sign_string': test_string,
                    'generated_sign': md5_sign.upper()
                })
            
            # SHA1测试
            sha1_sign = hashlib.sha1(test_string.encode()).hexdigest()
            if sha1_sign == target_sign:
                found_matches.append({
                    'algorithm': 'SHA1',
                    'secret': secret,
                    'sign_string': test_string,
                    'generated_sign': sha1_sign
                })
            
            # HMAC-MD5测试
            try:
                hmac_md5_sign = hmac.new(secret.encode(), test_string.encode(), hashlib.md5).hexdigest()
                if hmac_md5_sign == target_sign:
                    found_matches.append({
                        'algorithm': 'HMAC-MD5',
                        'secret': secret,
                        'sign_string': test_string,
                        'generated_sign': hmac_md5_sign
                    })
            except:
                pass
    
    print(f"\n完成测试，共测试了 {len(secrets)} 个密钥")
    
    if found_matches:
        print(f"\n🎯 找到 {len(found_matches)} 个匹配的算法:")
        for i, match in enumerate(found_matches, 1):
            print(f"\n匹配 {i}:")
            print(f"  算法: {match['algorithm']}")
            print(f"  密钥: {match['secret']}")
            print(f"  签名字符串: {match['sign_string']}")
            print(f"  生成的签名: {match['generated_sign']}")
            
            # 生成对应的Python代码
            print(f"\n  Python代码:")
            if match['algorithm'].startswith('MD5'):
                print(f"  sign = hashlib.md5('{match['sign_string']}'.encode()).hexdigest()")
            elif match['algorithm'].startswith('HMAC'):
                print(f"  sign = hmac.new('{match['secret']}'.encode(), '{match['sign_string']}'.encode(), hashlib.md5).hexdigest()")
    else:
        print("\n❌ 未找到匹配的算法")
        print("\n🔍 进一步分析建议:")
        
        # 分析签名特征
        print(f"\n签名特征分析:")
        print(f"  签名长度: {len(target_sign)} 字符")
        print(f"  签名格式: {'全小写' if target_sign.islower() else '包含大写' if target_sign.isupper() else '混合大小写'}")
        print(f"  是否为十六进制: {all(c in '0123456789abcdefABCDEF' for c in target_sign)}")
        
        if len(target_sign) == 32:
            print("  可能是MD5哈希 (32位)")
        elif len(target_sign) == 40:
            print("  可能是SHA1哈希 (40位)")
        elif len(target_sign) == 64:
            print("  可能是SHA256哈希 (64位)")
        
        # 尝试时间戳相关
        print(f"\n🕐 尝试时间戳相关算法:")
        current_time = int(time.time())
        for time_offset in range(-3600, 3601, 60):  # 前后1小时，每分钟一个点
            test_timestamp = current_time + time_offset
            test_cases = [
                f"{params['mobile']}{test_timestamp}secret",
                f"{params['mobile']}{params['activityId']}{test_timestamp}",
                f"{test_timestamp}{params['mobile']}{params['activityId']}",
            ]
            
            for test_string in test_cases:
                md5_sign = hashlib.md5(test_string.encode()).hexdigest()
                if md5_sign == target_sign:
                    print(f"  ✅ 找到时间戳匹配: {test_string}")
                    print(f"     时间戳: {test_timestamp} ({time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(test_timestamp))})")
                    return
        
        print("  未找到时间戳相关的匹配")
        
        # 建议下一步
        print(f"\n💡 下一步建议:")
        print("  1. 查看前端JavaScript代码中的签名生成逻辑")
        print("  2. 使用浏览器开发者工具查看网络请求的完整参数")
        print("  3. 检查是否有隐藏的参数（如时间戳、nonce等）")
        print("  4. 尝试不同的参数顺序和拼接方式")
        print("  5. 查看API文档或联系开发者获取签名算法")

def test_specific_patterns():
    """测试特定的签名模式"""
    print("\n🧪 测试特定签名模式")
    print("=" * 40)
    
    params = {
        'mobile': '18699133341',
        'activityId': '625', 
        'channel': 'bac27baa773c5bc2d3060edeb9881c64',
        'regionLimitFlag': '1'
    }
    target_sign = '0aa2427fbb22fe6308893b59926b700e'
    
    # 测试一些特殊的模式
    special_patterns = [
        # 可能的API密钥或固定字符串
        'wo_activity_secret_2024',
        'unicom_mobile_api_key',
        'huodong_10155_secret',
        'activity_625_secret',
        
        # 基于参数的组合密钥
        f"secret_{params['activityId']}",
        f"key_{params['channel'][:8]}",
        f"mobile_secret_{params['activityId']}",
    ]
    
    for secret in special_patterns:
        test_string = f"{params['mobile']}{params['activityId']}{secret}"
        md5_sign = hashlib.md5(test_string.encode()).hexdigest()
        print(f"测试: {test_string[:50]}... -> {md5_sign}")
        if md5_sign == target_sign:
            print(f"✅ 匹配! 密钥: {secret}")

if __name__ == "__main__":
    analyze_unicom_sign()
    test_specific_patterns()
