import sys
import requests
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QLineEdit, QPushButton, QTextEdit, QLabel, QCheckBox)
from PyQt5.QtCore import QTimer
import re
import random
from datetime import datetime
import time
import json
import os
import urllib.parse
import configparser
import urllib3
# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class ForumVisitor(QMainWindow):
    def __init__(self):
        super().__init__()
        self.config_file = '平台账号.ini'  # ini 文件路径
        self.initUI()  # Initialize UI first
        self.config = self.load_config()  # Load config after UI
        self.populate_config_fields()  # Populate UI fields with config values
        self.timer = QTimer()
        self.timer.timeout.connect(self.visit_forum)
        self.is_running = False

        # 发帖相关变量
        self.post_timer = QTimer()
        self.post_timer.timeout.connect(self.check_daily_post)
        self.last_post_date = None  # 记录最后发帖日期
        self.daily_post_done = False  # 今日是否已发帖
        self.morning_post_done = False  # 今日早安是否已发
        self.evening_post_done = False  # 今日晚安是否已发
        self.morning_post_attempts = 0  # 早安发帖尝试次数
        self.evening_post_attempts = 0  # 晚安发帖尝试次数
        self.max_post_attempts = 5  # 最大尝试次数

        # 自动重启相关变量
        self.restart_timer = QTimer()
        self.restart_timer.timeout.connect(self.check_auto_restart)
        self.last_restart_date = None  # 记录最后重启日期
        self.daily_restart_done = False  # 今日是否已重启
        self.exception_restart_timer = QTimer()
        self.exception_restart_timer.timeout.connect(self.restart_after_exception)
        self.exception_restart_timer.setSingleShot(True)  # 单次触发
        self.last_exception_time = None  # 记录最后异常时间
        self.restart_delay_timer = QTimer()
        self.restart_delay_timer.timeout.connect(self.delayed_start)
        self.restart_delay_timer.setSingleShot(True)  # 单次触发

        # 配置requests会话以信任自签名证书
        self.session = requests.Session()
        self.session.verify = False  # 禁用SSL验证

        # 重试配置
        self.max_retries = 3
        self.max_timeout = 50
        self.post_ids = []  # 保存当前获取的帖子ID
        self.visited_posts = set()  # 记录已访问的帖子ID
        self.posts_visited_count = 0  # 记录已访问的帖子数量
        self.log_file = 'forum_log.txt'  # 保存标题、API回复和回帖返回的TXT文件
        self.visited_posts_file = 'visited_posts.txt'  # 保存已访问帖子ID的文件
        self.load_visited_posts()  # 加载历史访问的帖子ID

    def load_config(self):
        """从平台账号.ini加载配置"""
        config = configparser.ConfigParser()
        try:
            if os.path.exists(self.config_file):
                config.read(self.config_file, encoding='utf-8')
            else:
                config['DEFAULT'] = {
                    'cookie': 'GUID=4b436a2710585417; sidyaohuo=99E561D23A85C4340_8_47_81474_100100-2; NewReplyUI=0; ASP.NET_SessionId=ig1wuk3yw52igfvsfze4gwft; _d_id=574a0a9a3b26bf2a7a09a922782e59; arp_scroll_position=2000',
                    'api_url': 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
                    'authorization': 'Bearer b906343484cc4a47b6909bc0b1b33e8c.ucynYND6FdxI6rYf',
                    'api_request_content': json.dumps({
                        "model": "GLM-4-Flash-250414",
                        "messages": [{"role": "user", "content": ""}]
                    }, ensure_ascii=False)
                }
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    config.write(f)
            return config
        except Exception as e:
            # Log error after UI is initialized
            return configparser.ConfigParser()

    def populate_config_fields(self):
        """将配置文件内容填充到UI输入框"""
        try:
            self.cookie_input.setText(self.config['DEFAULT'].get('cookie', 'GUID=4b436a2710585417; sidyaohuo=99E561D23A85C4340_8_47_81474_100100-2; NewReplyUI=0; ASP.NET_SessionId=ig1wuk3yw52igfvsfze4gwft; _d_id=574a0a9a3b26bf2a7a09a922782e59; arp_scroll_position=2000'))
            self.api_url_input.setText(self.config['DEFAULT'].get('api_url', 'https://open.bigmodel.cn/api/paas/v4/chat/completions'))
            self.authorization_input.setText(self.config['DEFAULT'].get('authorization', 'Bearer b906343484cc4a47b6909bc0b1b33e8c.ucynYND6FdxI6rYf'))
            self.api_content_input.setText(self.config['DEFAULT'].get('api_request_content', json.dumps({
                "model": "GLM-4-Flash-250414",
                "messages": [{"role": "user", "content": ""}]
            }, ensure_ascii=False)))
            if not os.path.exists(self.config_file):
                self.log(f'未找到 {self.config_file}，已创建默认配置文件')
        except Exception as e:
            self.log(f'加载配置文件到UI失败: {str(e)}')

    def initUI(self):
        self.setWindowTitle('论坛帖子定时访问器')
        self.setGeometry(100, 100, 600, 500)

        # 主布局
        widget = QWidget()
        self.setCentralWidget(widget)
        layout = QVBoxLayout()
        widget.setLayout(layout)

        # Cookie输入框
        layout.addWidget(QLabel('Cookie:'))
        self.cookie_input = QLineEdit()
        layout.addWidget(self.cookie_input)

        # API 网址输入框
        layout.addWidget(QLabel('API 网址:'))
        self.api_url_input = QLineEdit()
        layout.addWidget(self.api_url_input)

        # Authorization 输入框
        layout.addWidget(QLabel('Authorization:'))
        self.authorization_input = QLineEdit()
        layout.addWidget(self.authorization_input)

        # API 请求内容输入框
        layout.addWidget(QLabel('API 请求内容 (JSON):'))
        self.api_content_input = QTextEdit()
        layout.addWidget(self.api_content_input)

        # 随机访问间隔范围输入框
        layout.addWidget(QLabel('随机访问间隔范围（分钟，格式：min-max，例：1-10）:'))
        self.interval_range_input = QLineEdit('1-10')
        layout.addWidget(self.interval_range_input)

        # 访问时间段输入框
        layout.addWidget(QLabel('访问时间段（格式：HH:MM-HH:MM，例：07:49-22:00）:'))
        self.time_range_input = QLineEdit('07:49-22:00')
        layout.addWidget(self.time_range_input)

        # 回帖功能开关
        layout.addWidget(QLabel('回帖功能:'))
        self.reply_checkbox = QCheckBox('启用回帖（将API回复发布到论坛）')
        layout.addWidget(self.reply_checkbox)

        # 发帖功能开关
        layout.addWidget(QLabel('发帖功能:'))
        self.post_checkbox = QCheckBox('启用每日发帖 (10:00-13:00 随机发布历史上的今天)')
        self.post_checkbox.setChecked(False)
        layout.addWidget(self.post_checkbox)

        # 手动发帖按钮
        self.manual_post_button = QPushButton('立即发布历史上的今天')
        self.manual_post_button.clicked.connect(self.manual_post)
        layout.addWidget(self.manual_post_button)

        # 手动早安晚安按钮
        self.manual_morning_button = QPushButton('立即发布早安问候')
        self.manual_morning_button.clicked.connect(self.manual_morning_post)
        layout.addWidget(self.manual_morning_button)

        self.manual_evening_button = QPushButton('立即发布晚安问候')
        self.manual_evening_button.clicked.connect(self.manual_evening_post)
        layout.addWidget(self.manual_evening_button)

        # 早安晚安发帖开关
        layout.addWidget(QLabel('问候发帖:'))
        self.greeting_checkbox = QCheckBox('启用早安(7:49-8点)和晚安(21-22点)问候发帖')
        self.greeting_checkbox.setChecked(False)
        layout.addWidget(self.greeting_checkbox)

        # 代理开关
        layout.addWidget(QLabel('调试功能:'))
        self.proxy_checkbox = QCheckBox('启用代理 (Fiddler 127.0.0.1:8888)')
        self.proxy_checkbox.setChecked(False)
        self.proxy_checkbox.stateChanged.connect(self.toggle_proxy)
        layout.addWidget(self.proxy_checkbox)

        # 自动重启功能
        layout.addWidget(QLabel('自动重启:'))
        self.auto_restart_checkbox = QCheckBox('启用每日凌晨自动重启 (00:00-00:30)')
        self.auto_restart_checkbox.setChecked(True)
        layout.addWidget(self.auto_restart_checkbox)

        self.exception_restart_checkbox = QCheckBox('启用异常自动重启 (异常后30分钟重启)')
        self.exception_restart_checkbox.setChecked(True)
        layout.addWidget(self.exception_restart_checkbox)

        # 日志显示框
        layout.addWidget(QLabel('日志:'))
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)

        # 启动/停止按钮
        self.start_stop_button = QPushButton('启动')
        self.start_stop_button.clicked.connect(self.toggle_script)
        layout.addWidget(self.start_stop_button)

    def log(self, message):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.log_display.append(f'[{timestamp}] {message}')
        self.log_display.ensureCursorVisible()

    def load_visited_posts(self):
        """从本地文件加载已访问的帖子ID"""
        try:
            if os.path.exists(self.visited_posts_file):
                with open(self.visited_posts_file, 'r', encoding='utf-8') as f:
                    self.visited_posts = set(line.strip() for line in f if line.strip())
                self.log(f'已加载 {len(self.visited_posts)} 个历史访问帖子ID')
        except Exception as e:
            self.log(f'加载已访问帖子ID失败: {str(e)}')

    def save_visited_post(self, post_id):
        """保存已访问的帖子ID到本地文件"""
        try:
            with open(self.visited_posts_file, 'a', encoding='utf-8') as f:
                f.write(f'{post_id}\n')
        except Exception as e:
            self.log(f'保存帖子ID到文件失败: {str(e)}')

    def save_to_txt(self, title, api_reply, post_response=None):
        """保存标题、API回复和回帖返回内容到TXT文件"""
        try:
            with open(self.log_file, 'a', encoding='utf-8') as f:
                timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                f.write(f'[{timestamp}]\n标题: {title}\nAPI回复: {api_reply}\n')
                if post_response:
                    f.write(f'论坛回帖返回: {post_response}\n')
                f.write('\n')
            self.log(f'已保存到 {self.log_file}')
        except Exception as e:
            self.log(f'保存TXT文件失败: {str(e)}')

    def is_within_time_range(self):
        current_time = datetime.now().time()
        time_range = self.time_range_input.text().strip()
        
        try:
            start_time_str, end_time_str = time_range.split('-')
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()
            
            return start_time <= current_time <= end_time
        except ValueError:
            self.log('时间格式错误，使用默认07:49-22:00')
            return datetime.strptime('07:49', '%H:%M').time() <= current_time <= datetime.strptime('22:00', '%H:%M').time()

    def get_random_interval(self):
        interval_range = self.interval_range_input.text().strip()
        try:
            min_interval, max_interval = map(float, interval_range.split('-'))
            if min_interval <= 0 or max_interval < min_interval:
                raise ValueError('间隔范围无效')
            return random.uniform(min_interval, max_interval) * 60 * 1000
        except ValueError:
            self.log('随机间隔范围格式错误，使用默认1-10分钟')
            return random.uniform(1, 10) * 60 * 1000

    def fetch_post_ids(self):
        """从首页获取帖子ID，过滤已访问的ID"""
        try:
            cookies = {'cookie': self.cookie_input.text().strip()}
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = self.retry_request('GET', 'https://www.yaohuo.me/', '获取首页帖子列表', headers=headers, cookies=cookies)
            response.raise_for_status()
            self.post_ids = re.findall(r'/bbs-(\d+)\.html', response.text)
            self.post_ids = [pid for pid in self.post_ids if pid not in self.visited_posts]
            self.log(f'获取到 {len(self.post_ids)} 个新帖子ID')
            if not self.post_ids:
                self.log('未找到新的帖子ID，尝试重新获取')
        except requests.RequestException as e:
            self.log(f'获取首页帖子ID错误: {str(e)}')
            self.post_ids = []
            self.schedule_exception_restart()

    def extract_replies(self, content):
        """提取回复内容，最多30层"""
        reply_pattern = r'<div class="reline list-reply" data-floor="(\d+)">.*?<span class="retext">(.*?)</span><span style="color: #404040;'
        replies = re.findall(reply_pattern, content, re.DOTALL)
        replies = replies[:30]  # 限制最多30层
        formatted_replies = [f'楼层 {floor}: {text.strip()}' for floor, text in replies]
        return '\n'.join(formatted_replies) if replies else '未找到回复', len(replies)

    def generate_reply_content(self, title, replies):
        """生成API提交内容"""
        prompt = ("根据标题和其他人的回复 生成你的回复 "
                  "尽量不要让发现你是机器人 要简短不要有表情 "
                  "只需要回复 不要有多余的提示 不要有标点符号 最多10个字")
        return f"{prompt}\n标题: {title}\n回复:\n{replies}"

    def send_to_api(self, content):
        """发送到API并提取回复"""
        api_url = self.api_url_input.text().strip()
        headers = {
            'Content-Type': 'application/json',
            'Authorization': self.authorization_input.text().strip(),
            'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64; rv:46.0) Gecko/20100101 Firefox/46.0'
        }
        try:
            api_content = json.loads(self.api_content_input.toPlainText().strip())
            api_content['messages'][0]['content'] = content
        except json.JSONDecodeError as e:
            self.log(f'API请求内容格式错误: {str(e)}')
            api_content = {
                "model": "gpt-3.5-turbo-16k",
                "messages": [{"role": "user", "content": content}],
                "stream": False
            }
        
        try:
            response = self.retry_request('POST', api_url, 'API生成回复', headers=headers, json=api_content)
            response.raise_for_status()
            result = response.json()
            reply_content = result['choices'][0]['message']['content']
            return reply_content
        except requests.RequestException as e:
            self.log(f'API请求错误: {str(e)}')
            self.schedule_exception_restart()
            return None
        except KeyError:
            self.log('API返回格式错误，未找到content字段')
            return None

    def post_reply(self, post_id, content, classid):
        """发布回复到论坛并返回响应内容"""
        reply_url = f'https://www.yaohuo.me/bbs/book_re.aspx?ajax=1&siteid=1000&classid={classid}&id={post_id}'
        headers = {
            'Host': 'www.yaohuo.me',
            'Connection': 'keep-alive',
            'sec-ch-ua-mobile': '?0',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
            'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
            'Accept': '*/*',
            'Origin': 'https://www.yaohuo.me',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Accept-Language': 'zh-CN,zh;q=0.9'
        }
        cookies = {'cookie': self.cookie_input.text().strip()}
        encoded_content = urllib.parse.urlencode({'content': content, 'g': '快速回复'})
        data = f"{encoded_content}&action=add&id={post_id}&siteid=1000&lpage=1&classid={classid}"
        try:
            response = self.retry_request('POST', reply_url, f'发布回复到帖子{post_id}', headers=headers, cookies=cookies, data=data.encode('utf-8'))
            response.raise_for_status()
            try:
                response_content = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                self.log(f'成功发布回复到 {reply_url}: {content}')
                self.log(f'论坛回帖返回: {response_content}')
                return response_content
            except ValueError:
                self.log(f'解析回帖返回内容失败，原始内容: {response.text}')
                return response.text
        except requests.RequestException as e:
            self.log(f'发布回复失败: {str(e)}')
            return None

    def visit_forum(self):
        if not self.is_within_time_range():
            self.log('当前时间不在设定时间段内，暂停访问')
            return

        if not self.post_ids or self.posts_visited_count >= 6:
            self.fetch_post_ids()
            self.posts_visited_count = 0

        if not self.post_ids:
            self.log('无可用帖子ID，等待下次尝试')
            interval = self.get_random_interval()
            self.timer.setInterval(int(interval))
            self.log(f'下一次尝试将在 {interval/60000:.2f} 分钟后进行')
            return

        try:
            cookies = {'cookie': self.cookie_input.text().strip()}
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            selected_id = random.choice(self.post_ids)
            post_url = f'https://www.yaohuo.me/bbs-{selected_id}.html'
            
            response = self.retry_request('GET', post_url, f'访问帖子{selected_id}', headers=headers, cookies=cookies)
            response.raise_for_status()
            
            classid_match = re.search(r'classid=(\d+)', response.text)
            classid = classid_match.group(1) if classid_match else '177'
            self.log(f'提取到的classid: {classid}')
            
            # 检查是否包含礼金或慶
            if '礼金' in response.text:
                self.log(f'帖子 {post_url} 包含“礼金”，跳过操作')
                self.visited_posts.add(selected_id)
                self.post_ids.remove(selected_id)
                self.posts_visited_count += 1
                self.save_visited_post(selected_id)
                interval = self.get_random_interval()
                self.timer.setInterval(int(interval))
                self.log(f'下一次访问将在 {interval/60000:.2f} 分钟后进行')
                return

            if '慶' in response.text:
                self.log(f'帖子 {post_url} 包含"慶"，跳过回复操作')
                self.visited_posts.add(selected_id)
                self.post_ids.remove(selected_id)
                self.posts_visited_count += 1
                self.save_visited_post(selected_id)
                interval = self.get_random_interval()
                self.timer.start(int(interval))
                self.log(f'下一次访问将在 {interval/60000:.2f} 分钟后进行')
                return
            
            self.visited_posts.add(selected_id)
            self.post_ids.remove(selected_id)
            self.posts_visited_count += 1
            self.save_visited_post(selected_id)
            
            title_match = re.search(r'\[标题\]</span>(.*?)<span', response.text, re.DOTALL)
            title = title_match.group(1).strip() if title_match else '未找到标题'
            
            replies, reply_count = self.extract_replies(response.text)
            
            self.log(f'成功访问帖子: {post_url}，标题: {title}')
            self.log(f'提取的回复（共 {reply_count} 楼）:\n{replies}')
            
            if reply_count > 3:
                api_content = self.generate_reply_content(title, replies)
                api_reply = self.send_to_api(api_content)
                if api_reply:
                    self.log(f'API生成的回复: {api_reply}')
                    if self.reply_checkbox.isChecked():
                        post_response = self.post_reply(selected_id, api_reply, classid)
                        self.save_to_txt(title, api_reply, post_response)
                    else:
                        self.save_to_txt(title, api_reply)
                else:
                    self.save_to_txt(title, api_reply)
            else:
                self.log(f'回复楼层数 {reply_count} <= 3，不发送到API')
            
            interval = self.get_random_interval()
            self.timer.setInterval(int(interval))
            self.log(f'下一次访问将在 {interval/60000:.2f} 分钟后进行')

        except requests.RequestException as e:
            self.log(f'访问错误: {str(e)}')
            self.schedule_exception_restart()
        except Exception as e:
            self.log(f'发生错误: {str(e)}')
            self.schedule_exception_restart()

    def toggle_script(self):
        if not self.is_running:
            try:
                self.fetch_post_ids()
                if not self.post_ids:
                    self.log('未获取到帖子ID，请检查网络或Cookie')
                    return
                interval = self.get_random_interval()
                self.timer.start(int(interval))
                self.is_running = True
                self.start_stop_button.setText('停止')
                self.log(f'脚本已启动，首次访问将在 {interval/60000:.2f} 分钟后进行')

                # 启动发帖定时器（每10分钟检查一次）
                if self.post_checkbox.isChecked() or self.greeting_checkbox.isChecked():
                    self.post_timer.start(600000)  # 10分钟 = 600000毫秒
                    if self.post_checkbox.isChecked():
                        self.log('每日发帖功能已启动，将在10:00-13:00随机发布历史上的今天')
                    if self.greeting_checkbox.isChecked():
                        self.log('问候发帖功能已启动，将在7:49-8点发早安，21-22点发晚安')

                # 启动自动重启定时器（每5分钟检查一次）
                if self.auto_restart_checkbox.isChecked():
                    self.restart_timer.start(300000)  # 5分钟 = 300000毫秒
                    self.log('每日自动重启功能已启动，将在凌晨00:00-00:30随机重启')
                
            except Exception as e:
                self.log(f'启动失败: {str(e)}')
        else:
            self.timer.stop()
            self.is_running = False
            self.start_stop_button.setText('启动')
            self.log('脚本已停止')
            # 同时停止所有定时器
            self.post_timer.stop()
            self.restart_timer.stop()
            self.exception_restart_timer.stop()
            self.restart_delay_timer.stop()

    def retry_request(self, method, url, description="请求", **kwargs):
        """通用重试请求方法"""
        for attempt in range(1, self.max_retries + 1):
            try:
                # 计算当前尝试的超时时间：第1次15秒，第2次30秒，第3次50秒
                timeout = min(15 * attempt, self.max_timeout)
                kwargs['timeout'] = timeout

                self.log(f'{description} - 第{attempt}次尝试 (超时{timeout}秒)')

                if method.upper() == 'GET':
                    response = self.session.get(url, **kwargs)
                elif method.upper() == 'POST':
                    response = self.session.post(url, **kwargs)
                else:
                    raise ValueError(f"不支持的HTTP方法: {method}")

                response.raise_for_status()
                self.log(f'{description} - 第{attempt}次尝试成功')
                return response

            except requests.exceptions.Timeout:
                self.log(f'{description} - 第{attempt}次尝试超时 ({timeout}秒)')
                if attempt == self.max_retries:
                    self.log(f'{description} - 所有重试均超时，放弃请求')
                    raise
                time.sleep(2)  # 重试前等待2秒

            except requests.RequestException as e:
                self.log(f'{description} - 第{attempt}次尝试失败: {str(e)}')
                if attempt == self.max_retries:
                    self.log(f'{description} - 所有重试均失败，放弃请求')
                    raise
                time.sleep(2)  # 重试前等待2秒

    def get_history_today(self):
        """获取历史上的今天内容"""
        try:
            response = self.retry_request('GET', 'https://jkapi.com/api/history', '获取历史上的今天')
            content = response.text.strip()

            if content:
                lines = content.split('\n')
                title = lines[0].strip() if lines else '历史上的今天'
                # 确保标题不为空
                if not title:
                    title = '历史上的今天'
                return title, content
            else:
                return '历史上的今天', '今天是特殊的一天'

        except Exception as e:
            self.log(f'获取历史上的今天最终失败: {str(e)}')
            return '历史上的今天', '今天是特殊的一天'

    def get_image_url_with_fallback(self):
        """获取图片URL，支持多个API自动切换"""
        image_apis = [
            {
                'name': 'API1 - bimg.cc',
                'url': 'https://api.bimg.cc/random?w=1920&h=1080&mkt=zh-CN',
                'method': 'redirect'
            },
            {
                'name': 'API2 - bing.ee123.net',
                'url': 'https://bing.ee123.net/img/rand',
                'method': 'redirect'
            },
            {
                'name': 'API3 - xsot.cn',
                'url': 'https://api.xsot.cn/bing/?jump=true',
                'method': 'html_parse'
            },
            {
                'name': 'API4 - vvhan.com (备用)',
                'url': 'https://api.vvhan.com/api/bing?type=json&rand=sj',
                'method': 'json'
            }
        ]

        for api in image_apis:
            try:
                self.log(f'尝试获取图片: {api["name"]}')

                if api['method'] == 'redirect':
                    # 获取重定向的Location头
                    response = self.session.head(api['url'], allow_redirects=False, timeout=10)
                    if response.status_code in [301, 302, 303, 307, 308]:
                        location = response.headers.get('Location', '')
                        if location:
                            self.log(f'从 {api["name"]} 获取到图片URL: {location}')
                            return location
                    else:
                        self.log(f'{api["name"]} 未返回重定向，状态码: {response.status_code}')

                elif api['method'] == 'html_parse':
                    # 解析HTML内容获取图片链接
                    response = self.session.get(api['url'], timeout=10)
                    if response.status_code == 200:
                        # 提取 <a href="...">Found</a> 中的链接
                        import re
                        match = re.search(r'<a href="([^"]+)">Found</a>', response.text)
                        if match:
                            image_url = match.group(1)
                            self.log(f'从 {api["name"]} 解析到图片URL: {image_url}')
                            return image_url
                        else:
                            self.log(f'{api["name"]} 未找到图片链接')
                    else:
                        self.log(f'{api["name"]} 请求失败，状态码: {response.status_code}')

                elif api['method'] == 'json':
                    # JSON格式API
                    response = self.session.get(api['url'], timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        if data.get('success') and 'data' in data:
                            url = data['data'].get('url', '')
                            if '&w=4096' in url:
                                url = url.replace('&w=4096', '')
                            if url:
                                self.log(f'从 {api["name"]} 获取到图片URL: {url}')
                                return url
                        else:
                            # 兼容旧格式
                            url = data.get('url', '')
                            if '&w=4096' in url:
                                url = url.replace('&w=4096', '')
                            if url:
                                self.log(f'从 {api["name"]} 获取到图片URL: {url}')
                                return url
                        self.log(f'{api["name"]} 返回的JSON中未找到有效图片URL')
                    else:
                        self.log(f'{api["name"]} 请求失败，状态码: {response.status_code}')

            except Exception as e:
                self.log(f'{api["name"]} 获取图片失败: {str(e)}')
                continue

        self.log('所有图片API都失败了，返回空URL')
        return ''

    def get_morning_greeting(self):
        """获取早安问候内容"""
        current_date = datetime.now().strftime('%Y年%m月%d日')
        title = f'早安问候 {current_date}'
        default_content = '新的一天开始了，愿你今天充满阳光和快乐！'

        try:
            # 获取早安问候文字
            response = self.retry_request('GET', 'https://jkapi.com/api/zaoan?type=json', '获取早安问候文字')
            data = response.json()
            content = data.get('content', default_content)

            # 获取早安图片 - 使用新的多API切换方法
            try:
                image_url = self.get_image_url_with_fallback()
                if image_url:
                    content += f'\n\n[img]{image_url}[/img]'
                    self.log(f'早安问候添加图片成功: {image_url}')
                else:
                    self.log('早安问候未能获取到图片')

            except Exception as e:
                self.log(f'获取早安图片最终失败: {str(e)}')

            return title, content

        except Exception as e:
            self.log(f'获取早安问候最终失败: {str(e)}')
            current_date = datetime.now().strftime('%Y年%m月%d日')
            return f'早安问候 {current_date}', default_content

    def get_evening_greeting(self):
        """获取晚安问候内容"""
        current_date = datetime.now().strftime('%Y年%m月%d日')
        title = f'晚安问候 {current_date}'
        default_content = '夜深了，愿你带着美好的梦境入睡，晚安！'

        try:
            # 获取晚安问候文字
            response = self.retry_request('GET', 'https://jkapi.com/api/wanan?type=json', '获取晚安问候文字')
            data = response.json()
            content = data.get('content', default_content)

            # 获取晚安图片 - 使用新的多API切换方法
            try:
                image_url = self.get_image_url_with_fallback()
                if image_url:
                    content += f'\n\n[img]{image_url}[/img]'
                    self.log(f'晚安问候添加图片成功: {image_url}')
                else:
                    self.log('晚安问候未能获取到图片')

            except Exception as e:
                self.log(f'获取晚安图片最终失败: {str(e)}')

            return title, content

        except Exception as e:
            self.log(f'获取晚安问候最终失败: {str(e)}')
            current_date = datetime.now().strftime('%Y年%m月%d日')
            return f'晚安问候 {current_date}', default_content

    def create_daily_post(self, post_type='history'):
        """创建每日发帖"""
        try:
            # 根据类型获取不同内容
            if post_type == 'morning':
                title, content = self.get_morning_greeting()
            elif post_type == 'evening':
                title, content = self.get_evening_greeting()
            else:  # history
                title, content = self.get_history_today()

            # 解析cookie字符串
            cookie_str = self.cookie_input.text().strip()
            cookies = {}
            if cookie_str:
                for item in cookie_str.split('; '):
                    if '=' in item:
                        key, value = item.split('=', 1)
                        cookies[key] = value

            headers = {
                'Host': 'www.yaohuo.me',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
                'Origin': 'https://www.yaohuo.me',
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Accept-Language': 'zh-CN,zh;q=0.9'
            }

            # URL编码标题和内容
            encoded_title = urllib.parse.quote(title, safe='')
            encoded_content = urllib.parse.quote(content, safe='')

            data = f'action=gomod&classid=177&siteid=1000&book_title={encoded_title}&book_content={encoded_content}&sendmoney=&g='

            response = self.retry_request(
                'POST',
                'https://www.yaohuo.me/bbs/book_view_add.aspx',
                f'发布帖子: {title[:20]}...',
                data=data,
                headers=headers,
                cookies=cookies
            )
            response.raise_for_status()

            self.log(f'成功发布每日帖子: {title}')
            self.log(f'发帖返回: {response.text[:200]}...')  # 只显示前200字符

            # 保存发帖记录
            self.save_post_to_txt(title, content, response.text)

            # 标记今日已发帖
            self.daily_post_done = True
            self.last_post_date = datetime.now().strftime('%Y-%m-%d')

            return True

        except requests.RequestException as e:
            self.log(f'发布每日帖子失败: {str(e)}')
            return False
        except Exception as e:
            self.log(f'发布每日帖子错误: {str(e)}')
            return False

    def check_daily_post(self):
        """检查是否需要进行每日发帖"""
        current_time = datetime.now()
        current_date = current_time.strftime('%Y-%m-%d')
        current_hour = current_time.hour

        # 如果是新的一天，重置所有发帖状态
        if self.last_post_date != current_date:
            self.daily_post_done = False
            self.morning_post_done = False
            self.evening_post_done = False
            self.morning_post_attempts = 0
            self.evening_post_attempts = 0
            self.last_post_date = current_date

        # 检查早安发帖 (7:49-8:00)
        if (self.greeting_checkbox.isChecked() and
            ((current_hour == 7 and current_time.minute >= 49) or current_hour == 8) and
            not self.morning_post_done and
            self.morning_post_attempts < self.max_post_attempts):
            if random.random() < 0.1:  # 10%概率
                self.log(f'开始执行早安发帖... (第{self.morning_post_attempts + 1}次尝试)')
                self.morning_post_attempts += 1
                if self.create_daily_post('morning'):
                    self.log('早安发帖完成')
                    self.morning_post_done = True
                else:
                    self.log(f'早安发帖失败 (已尝试{self.morning_post_attempts}次)')
                    if self.morning_post_attempts >= self.max_post_attempts:
                        self.log('早安发帖达到最大尝试次数，今日不再尝试')
                        self.schedule_exception_restart()  # 触发异常重启

        # 检查历史上的今天发帖 (10:00-13:00)
        if (self.post_checkbox.isChecked() and
            10 <= current_hour < 13 and
            not self.daily_post_done):
            if random.random() < 0.05:  # 5%概率
                self.log('开始执行每日发帖...')
                if self.create_daily_post('history'):
                    self.log('每日发帖完成')
                    self.daily_post_done = True
                else:
                    self.log('每日发帖失败')

        # 检查晚安发帖 (21:00-22:00)
        if (self.greeting_checkbox.isChecked() and
            21 <= current_hour < 22 and
            not self.evening_post_done and
            self.evening_post_attempts < self.max_post_attempts):
            if random.random() < 0.1:  # 10%概率
                self.log(f'开始执行晚安发帖... (第{self.evening_post_attempts + 1}次尝试)')
                self.evening_post_attempts += 1
                if self.create_daily_post('evening'):
                    self.log('晚安发帖完成')
                    self.evening_post_done = True
                else:
                    self.log(f'晚安发帖失败 (已尝试{self.evening_post_attempts}次)')
                    if self.evening_post_attempts >= self.max_post_attempts:
                        self.log('晚安发帖达到最大尝试次数，今日不再尝试')
                        self.schedule_exception_restart()  # 触发异常重启

    def save_post_to_txt(self, title, content, response):
        """保存发帖记录到TXT文件"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_content = f'[{timestamp}] 每日发帖\n标题: {title}\n内容: {content}\n发帖返回: {response}\n\n'

            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_content)
            self.log(f'发帖记录已保存到 {self.log_file}')
        except Exception as e:
            self.log(f'保存发帖记录错误: {str(e)}')

    def manual_post(self):
        """手动发帖"""
        self.log('手动执行发帖...')
        if self.create_daily_post():
            self.log('手动发帖完成')
        else:
            self.log('手动发帖失败')

    def manual_morning_post(self):
        """手动早安发帖"""
        self.log('手动执行早安发帖...')
        if self.create_daily_post('morning'):
            self.log('手动早安发帖完成')
        else:
            self.log('手动早安发帖失败')

    def manual_evening_post(self):
        """手动晚安发帖"""
        self.log('手动执行晚安发帖...')
        if self.create_daily_post('evening'):
            self.log('手动晚安发帖完成')
        else:
            self.log('手动晚安发帖失败')

    def toggle_proxy(self):
        """切换代理设置"""
        if self.proxy_checkbox.isChecked():
            # 启用代理
            self.session.proxies = {
                'http': 'http://127.0.0.1:8888',
                'https': 'http://127.0.0.1:8888'
            }
            self.log('已启用代理 (127.0.0.1:8888)')
        else:
            # 禁用代理
            self.session.proxies = {}
            self.log('已禁用代理')

    def check_auto_restart(self):
        """检查是否需要每日自动重启"""
        if not self.auto_restart_checkbox.isChecked():
            return

        current_time = datetime.now()
        current_date = current_time.strftime('%Y-%m-%d')
        current_hour = current_time.hour
        current_minute = current_time.minute

        # 检查是否在凌晨重启时间范围内 (00:00-00:30)
        if not (current_hour == 0 and current_minute < 30):
            return

        # 检查今日是否已重启
        if self.daily_restart_done and self.last_restart_date == current_date:
            return

        # 如果是新的一天，重置重启状态
        if self.last_restart_date != current_date:
            self.daily_restart_done = False
            self.last_restart_date = current_date

        # 随机决定是否在这个时间点重启 (每次检查有10%的概率重启)
        if random.random() < 0.1:
            self.log('开始执行每日自动重启...')
            self.auto_restart_script()

    def schedule_exception_restart(self):
        """安排异常后的自动重启"""
        if not self.exception_restart_checkbox.isChecked():
            return

        current_time = datetime.now()

        # 如果距离上次异常不到30分钟，不重复安排
        if (self.last_exception_time and
            (current_time - self.last_exception_time).total_seconds() < 1800):
            return

        self.last_exception_time = current_time
        self.log('检测到异常，将在30分钟后自动重启脚本')

        # 30分钟 = 1800000毫秒
        self.exception_restart_timer.start(1800000)

    def restart_after_exception(self):
        """异常后执行重启"""
        self.log('异常重启时间到，开始重启脚本...')
        self.auto_restart_script()

    def auto_restart_script(self):
        """自动重启脚本"""
        if self.is_running:
            self.log('正在停止脚本...')
            self.timer.stop()
            self.post_timer.stop()
            self.is_running = False
            self.start_stop_button.setText('启动')
            self.log('脚本已停止，30秒后自动重新启动')

            # 30秒后重新启动
            self.restart_delay_timer.start(30000)
        else:
            self.log('脚本未运行，直接启动')
            self.delayed_start()

    def delayed_start(self):
        """延迟启动脚本"""
        self.log('开始自动重新启动脚本...')
        try:
            self.fetch_post_ids()
            if not self.post_ids:
                self.log('未获取到帖子ID，重启失败')
                return

            interval = self.get_random_interval()
            self.timer.start(int(interval))
            self.is_running = True
            self.start_stop_button.setText('停止')
            self.log(f'脚本自动重启成功，首次访问将在 {interval/60000:.2f} 分钟后进行')

            # 启动发帖定时器
            if self.post_checkbox.isChecked() or self.greeting_checkbox.isChecked():
                self.post_timer.start(600000)  # 10分钟检查一次

            # 标记重启完成
            if self.auto_restart_checkbox.isChecked():
                self.daily_restart_done = True

        except Exception as e:
            self.log(f'自动重启失败: {str(e)}')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ForumVisitor()
    window.show()
    sys.exit(app.exec_())