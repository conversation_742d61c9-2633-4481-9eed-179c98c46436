import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import time
import json
import matplotlib.pyplot as plt
from collections import deque
import os
from 深度学习拼图破解器 import PuzzleNet, PuzzleEnvironment, DataGenerator

class DeepPuzzleTrainer:
    """深度学习拼图训练器"""
    def __init__(self, target_accuracy=0.90):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.target_accuracy = target_accuracy
        
        print(f"🖥️ 使用设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
            print(f"📊 显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
        
        # 初始化模型
        self.model = PuzzleNet().to(self.device)
        self.data_generator = DataGenerator()
        
        # 训练参数
        self.learning_rate = 0.001
        self.batch_size = 64 if torch.cuda.is_available() else 16
        self.max_epochs = 1000
        
        # 优化器和损失函数
        self.optimizer = optim.AdamW(self.model.parameters(), 
                                   lr=self.learning_rate, 
                                   weight_decay=0.01)
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer, mode='max', factor=0.8, patience=20)
        
        self.position_criterion = nn.CrossEntropyLoss()
        self.value_criterion = nn.MSELoss()
        
        # 训练历史
        self.train_history = {
            'epoch': [],
            'train_loss': [],
            'train_acc': [],
            'val_loss': [],
            'val_acc': [],
            'learning_rate': []
        }
        
        # 最佳模型跟踪
        self.best_accuracy = 0.0
        self.patience_counter = 0
        self.max_patience = 50
        
    def train_epoch(self, epoch):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        correct_predictions = 0
        total_samples = 0
        
        # 动态调整每个epoch的批次数
        batches_per_epoch = max(50, 200 - epoch // 10)
        
        for batch_idx in range(batches_per_epoch):
            # 生成训练数据
            boards, pieces, targets, values = self.data_generator.generate_training_batch(
                self.batch_size, board_sizes=[3,4,5,6,7,8])
            
            boards = boards.to(self.device)
            pieces = pieces.to(self.device)
            targets = targets.to(self.device)
            values = values.to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            position_scores, predicted_values = self.model(boards, pieces)
            
            # 计算损失
            position_loss = self.position_criterion(position_scores, targets)
            value_loss = self.value_criterion(predicted_values.squeeze(), values)
            total_loss_batch = position_loss + 0.5 * value_loss
            
            # 反向传播
            total_loss_batch.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            self.optimizer.step()
            
            # 统计
            total_loss += total_loss_batch.item()
            _, predicted = torch.max(position_scores, 1)
            correct_predictions += (predicted == targets).sum().item()
            total_samples += targets.size(0)
            
            # 进度显示
            if batch_idx % 20 == 0:
                current_acc = correct_predictions / total_samples * 100
                print(f"  Batch {batch_idx:3d}/{batches_per_epoch}: "
                      f"Loss: {total_loss_batch.item():.4f}, "
                      f"Acc: {current_acc:.1f}%")
        
        avg_loss = total_loss / batches_per_epoch
        accuracy = correct_predictions / total_samples
        
        return avg_loss, accuracy
    
    def validate(self):
        """验证模型"""
        self.model.eval()
        total_loss = 0
        correct_predictions = 0
        total_samples = 0
        
        with torch.no_grad():
            for _ in range(20):  # 验证20个批次
                boards, pieces, targets, values = self.data_generator.generate_training_batch(
                    self.batch_size, board_sizes=[3,4,5,6,7,8])
                
                boards = boards.to(self.device)
                pieces = pieces.to(self.device)
                targets = targets.to(self.device)
                values = values.to(self.device)
                
                position_scores, predicted_values = self.model(boards, pieces)
                
                position_loss = self.position_criterion(position_scores, targets)
                value_loss = self.value_criterion(predicted_values.squeeze(), values)
                total_loss += (position_loss + 0.5 * value_loss).item()
                
                _, predicted = torch.max(position_scores, 1)
                correct_predictions += (predicted == targets).sum().item()
                total_samples += targets.size(0)
        
        avg_loss = total_loss / 20
        accuracy = correct_predictions / total_samples
        
        return avg_loss, accuracy
    
    def test_on_real_puzzles(self):
        """在真实拼图上测试"""
        self.model.eval()
        test_cases = [
            # 3x3 简单
            {
                'board_size': 3,
                'required': [(0,0), (2,2)],
                'forbidden': [(1,1)],
                'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
            },
            # 4x4 中等
            {
                'board_size': 4,
                'required': [(0,0), (0,3), (3,0), (3,3)],
                'forbidden': [(1,1), (2,2)],
                'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
            },
            # 5x5 复杂
            {
                'board_size': 5,
                'required': [(0,2), (2,0), (2,4), (4,2)],
                'forbidden': [(2,2)],
                'pieces': {"T": 1, "田": 1, "横杠竖条": 1, "Z": 0, "L": 0}
            }
        ]
        
        success_count = 0
        total_time = 0
        
        with torch.no_grad():
            for i, test_case in enumerate(test_cases):
                start_time = time.time()
                
                # 设置环境
                env = PuzzleEnvironment(test_case['board_size'])
                env.reset()
                
                # 设置必需和禁止位置
                for r, c in test_case['required']:
                    env.board[r, c] = 1
                    env.required_positions.add((r, c))
                
                for r, c in test_case['forbidden']:
                    env.board[r, c] = 2
                    env.forbidden_positions.add((r, c))
                
                env.pieces_left = test_case['pieces'].copy()
                
                # 尝试求解
                solved = self.solve_puzzle(env, max_steps=20)
                solve_time = time.time() - start_time
                total_time += solve_time
                
                if solved:
                    success_count += 1
                    print(f"  测试 {i+1}: ✅ 成功 ({solve_time:.3f}s)")
                else:
                    print(f"  测试 {i+1}: ❌ 失败 ({solve_time:.3f}s)")
        
        success_rate = success_count / len(test_cases)
        avg_time = total_time / len(test_cases)
        
        print(f"\n🎯 测试结果: {success_count}/{len(test_cases)} "
              f"({success_rate*100:.1f}%) 平均时间: {avg_time:.3f}s")
        
        return success_rate
    
    def solve_puzzle(self, env, max_steps=50):
        """使用模型求解拼图"""
        for step in range(max_steps):
            if env.is_solved():
                return True
            
            # 获取当前状态
            board_state, piece_state = env.get_state()
            
            # 转换为tensor
            board_tensor = torch.FloatTensor(board_state).unsqueeze(0).unsqueeze(0).to(self.device)
            piece_tensor = torch.FloatTensor(piece_state).unsqueeze(0).to(self.device)
            
            # 预测
            position_scores, _ = self.model(board_tensor, piece_tensor)
            position_probs = torch.softmax(position_scores, dim=1)
            
            # 获取有效动作
            valid_actions = env.get_valid_actions()
            if not valid_actions:
                break
            
            # 选择最佳动作
            best_action = None
            best_score = -1
            
            for action in valid_actions:
                piece_name, shape_idx, r, c = action
                pos_idx = r * 8 + c
                score = position_probs[0, pos_idx].item()
                
                if score > best_score:
                    best_score = score
                    best_action = action
            
            if best_action:
                piece_name, shape_idx, r, c = best_action
                success, reward = env.place_piece(piece_name, shape_idx, r, c)
                if not success:
                    break
            else:
                break
        
        return env.is_solved()
    
    def train_until_target(self):
        """训练直到达到目标准确率"""
        print(f"🎯 开始训练，目标准确率: {self.target_accuracy*100:.1f}%")
        print(f"📊 批次大小: {self.batch_size}")
        print(f"🔄 最大轮数: {self.max_epochs}")
        print("=" * 80)
        
        start_time = time.time()
        
        for epoch in range(self.max_epochs):
            epoch_start = time.time()
            
            # 训练
            train_loss, train_acc = self.train_epoch(epoch)
            
            # 验证
            val_loss, val_acc = self.validate()
            
            # 学习率调度
            old_lr = self.optimizer.param_groups[0]['lr']
            self.scheduler.step(val_acc)
            current_lr = self.optimizer.param_groups[0]['lr']

            # 手动输出学习率变化信息
            if current_lr != old_lr:
                print(f"  📉 学习率降低: {old_lr:.6f} → {current_lr:.6f}")
            
            # 记录历史
            self.train_history['epoch'].append(epoch)
            self.train_history['train_loss'].append(train_loss)
            self.train_history['train_acc'].append(train_acc)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['val_acc'].append(val_acc)
            self.train_history['learning_rate'].append(current_lr)
            
            epoch_time = time.time() - epoch_start
            
            print(f"Epoch {epoch+1:3d}/{self.max_epochs}: "
                  f"Train Acc: {train_acc*100:5.1f}% | "
                  f"Val Acc: {val_acc*100:5.1f}% | "
                  f"Loss: {val_loss:.4f} | "
                  f"LR: {current_lr:.6f} | "
                  f"Time: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if val_acc > self.best_accuracy:
                self.best_accuracy = val_acc
                self.patience_counter = 0
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'epoch': epoch,
                    'accuracy': val_acc,
                    'train_history': self.train_history
                }, 'best_puzzle_model.pth')
                print(f"  ✅ 保存最佳模型 (准确率: {val_acc*100:.1f}%)")
            else:
                self.patience_counter += 1
            
            # 检查是否达到目标
            if val_acc >= self.target_accuracy:
                print(f"\n🎉 达到目标准确率 {self.target_accuracy*100:.1f}%!")
                
                # 在真实拼图上测试
                print("\n🧪 在真实拼图上测试...")
                real_success_rate = self.test_on_real_puzzles()
                
                if real_success_rate >= self.target_accuracy:
                    print(f"✅ 真实测试也达到目标: {real_success_rate*100:.1f}%")
                    break
                else:
                    print(f"⚠️ 真实测试未达标: {real_success_rate*100:.1f}%，继续训练...")
            
            # 早停检查
            if self.patience_counter >= self.max_patience:
                print(f"\n⏹️ 早停触发 (patience: {self.max_patience})")
                break
            
            # 每50轮测试一次真实拼图
            if (epoch + 1) % 50 == 0:
                print(f"\n🧪 第{epoch+1}轮真实拼图测试...")
                self.test_on_real_puzzles()
        
        total_time = time.time() - start_time
        print(f"\n🏁 训练完成!")
        print(f"⏱️ 总时间: {total_time/3600:.1f}小时")
        print(f"🎯 最佳准确率: {self.best_accuracy*100:.1f}%")
        
        # 保存训练历史
        with open('training_history.json', 'w') as f:
            json.dump(self.train_history, f, indent=2)
        
        # 绘制训练曲线
        self.plot_training_curves()
        
        return self.best_accuracy >= self.target_accuracy
    
    def plot_training_curves(self):
        """绘制训练曲线"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = self.train_history['epoch']
        
        # 准确率曲线
        ax1.plot(epochs, [acc*100 for acc in self.train_history['train_acc']], 'b-', label='训练准确率')
        ax1.plot(epochs, [acc*100 for acc in self.train_history['val_acc']], 'r-', label='验证准确率')
        ax1.axhline(y=self.target_accuracy*100, color='g', linestyle='--', label=f'目标准确率 {self.target_accuracy*100:.1f}%')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('准确率 (%)')
        ax1.set_title('模型准确率')
        ax1.legend()
        ax1.grid(True)
        
        # 损失曲线
        ax2.plot(epochs, self.train_history['train_loss'], 'b-', label='训练损失')
        ax2.plot(epochs, self.train_history['val_loss'], 'r-', label='验证损失')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('损失')
        ax2.set_title('模型损失')
        ax2.legend()
        ax2.grid(True)
        
        # 学习率曲线
        ax3.plot(epochs, self.train_history['learning_rate'], 'g-')
        ax3.set_xlabel('Epoch')
        ax3.set_ylabel('学习率')
        ax3.set_title('学习率变化')
        ax3.set_yscale('log')
        ax3.grid(True)
        
        # 准确率分布
        val_accs = [acc*100 for acc in self.train_history['val_acc']]
        ax4.hist(val_accs, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax4.axvline(x=self.target_accuracy*100, color='r', linestyle='--', label=f'目标: {self.target_accuracy*100:.1f}%')
        ax4.set_xlabel('验证准确率 (%)')
        ax4.set_ylabel('频次')
        ax4.set_title('准确率分布')
        ax4.legend()
        ax4.grid(True)
        
        plt.tight_layout()
        plt.savefig('training_curves.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 训练曲线已保存为 training_curves.png")

def main():
    """主函数"""
    print("🧠 深度学习拼图破解器 - GPU训练")
    print("=" * 60)
    
    # 检查CUDA
    if not torch.cuda.is_available():
        print("⚠️ 未检测到CUDA，将使用CPU训练（速度较慢）")
        choice = input("是否继续? (y/n): ")
        if choice.lower() != 'y':
            return
    
    # 设置目标准确率
    target_acc = float(input("请输入目标准确率 (0.90): ") or "0.90")
    
    # 创建训练器
    trainer = DeepPuzzleTrainer(target_accuracy=target_acc)
    
    # 开始训练
    success = trainer.train_until_target()
    
    if success:
        print("🎉 训练成功完成！")
        print("📁 模型已保存为: best_puzzle_model.pth")
        print("📊 训练历史已保存为: training_history.json")
    else:
        print("⚠️ 未达到目标准确率，但已保存最佳模型")

if __name__ == "__main__":
    main()
