import tkinter as tk
from tkinter import messagebox
import requests
import threading
import time
from datetime import datetime, timedelta
import json
from concurrent.futures import ThreadPoolExecutor
from queue import Queue
import os
import psutil

class SecKillApp:
    def __init__(self, root):
        self.root = root
        self.root.title("秒杀工具 - 毫秒级精确定时抢购 (自定义延迟)")
        self.accounts = []
        self.result_queue = Queue()  # 用于线程安全的结果更新
        self.is_seckilling = False  # 防止重复点击
        self.is_auto_waiting = False  # 防止重复启动自动抢购
        self.auto_thread = None  # 存储自动抢购线程

        # 根据当前小时设置 range
        current_hour = datetime.now().hour
        self.range_value = 10 if current_hour in [9, 10] else 16 if current_hour in [15, 16] else 10

        # 设置进程优先级为最高（提高抢购优先级）
        self.set_high_priority()

        self.create_widgets()

        # 请求头
        self.headers = {
            "Host": "**************:8080",
            "Content-Type": "application/json",
            "Origin": "http://**************:8080",
            "Connection": "keep-alive",
            "Accept": "application/json, text/plain, */*",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_8_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 unicom{version:iphone_c@11.0502}",
            "Accept-Language": "zh-CN,zh-Hans;q=0.9"
        }

        # 高并发持久连接会话（减少TCP握手/复用连接）
        self.setup_http_session()


        # 定期检查结果队列以更新 GUI
        self.update_results()
        # 定期更新倒计时显示（更频繁更新以显示毫秒）
        self.update_countdown_ms()

    def set_high_priority(self):
        """设置进程为最高优先级，提高抢购优先级"""
        try:
            current_process = psutil.Process(os.getpid())
            if os.name == 'nt':  # Windows系统
                current_process.nice(psutil.HIGH_PRIORITY_CLASS)
            else:  # Linux/Mac系统
                current_process.nice(-20)  # 最高优先级
            print("已设置进程为最高优先级")
        except Exception as e:
            print(f"设置进程优先级失败: {e}")

    def setup_http_session(self):
        """建立持久会话并调优连接池参数，提升并发与复用效率"""
        try:
            self.session = requests.Session()
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=200,
                pool_maxsize=500,
                max_retries=0,
                pool_block=False,
            )
            self.session.mount('http://', adapter)
            self.session.mount('https://', adapter)
            self.session.headers.update(self.headers)
            # 可选：预热
            # self.session.get('http://**************:8080/', timeout=1)
        except Exception as e:
            print(f"初始化HTTP会话失败: {e}")
            self.session = requests

    def create_widgets(self):
        # 第一行：账号输入和结果显示
        tk.Label(self.root, text="账号 (账号----token):").grid(row=0, column=0, padx=5, pady=5)
        self.accounts_text = tk.Text(self.root, height=15, width=50)
        self.accounts_text.grid(row=1, column=0, padx=5, pady=5)

        tk.Label(self.root, text="结果:").grid(row=0, column=1, padx=5, pady=5)
        self.results_text = tk.Text(self.root, height=15, width=50)
        self.results_text.grid(row=1, column=1, padx=5, pady=5)

        # 第二行：商品代码
        tk.Label(self.root, text="商品代码 1:").grid(row=2, column=0, padx=5, pady=5)
        self.item_code1 = tk.Entry(self.root, width=40)
        self.item_code1.grid(row=3, column=0, padx=5, pady=5)
        self.item_code1.insert(0, "AWARD_AHFridaySecKill_10_tx")

        tk.Label(self.root, text="商品代码 2:").grid(row=2, column=1, padx=5, pady=5)
        self.item_code2 = tk.Entry(self.root, width=40)
        self.item_code2.grid(row=3, column=1, padx=5, pady=5)
        self.item_code2.insert(0, "AWARD_AHFridaySecKill_10_hb5")

        # 第三行：第三个商品代码
        tk.Label(self.root, text="商品代码 3:").grid(row=4, column=0, padx=5, pady=5)
        self.item_code3 = tk.Entry(self.root, width=40)
        self.item_code3.grid(row=5, column=0, padx=5, pady=5)
        self.item_code3.insert(0, "AWARD_AHFridaySecKill_16_hb10")

        # 第三行：自定义延迟设置
        delay_frame = tk.Frame(self.root)
        delay_frame.grid(row=5, column=1, padx=5, pady=5)

        tk.Label(delay_frame, text="自定义延迟(毫秒):").pack(side=tk.TOP, pady=2)
        self.custom_delay_entry = tk.Entry(delay_frame, width=20, justify=tk.CENTER)
        self.custom_delay_entry.pack(side=tk.TOP, pady=2)
        self.custom_delay_entry.insert(0, "0")  # 默认0毫秒延迟

        delay_help = tk.Label(delay_frame, text="正数=延后执行，负数=提前执行", font=("Arial", 8), fg="gray")
        delay_help.pack(side=tk.TOP)

        # 第四行：按钮
        tk.Button(self.root, text="导入账号", command=self.import_accounts).grid(row=6, column=0, pady=10)
        tk.Button(self.root, text="立即秒杀", command=self.start_seckill).grid(row=6, column=1, pady=10)

        # 第五行：自动抢购功能
        auto_frame = tk.Frame(self.root)
        auto_frame.grid(row=7, column=0, columnspan=2, pady=10)

        tk.Label(auto_frame, text="自动抢购(精确到毫秒):").pack(side=tk.LEFT, padx=5)
        self.auto_start_btn = tk.Button(auto_frame, text="开始自动抢购", command=self.start_auto_seckill, bg="lightgreen")
        self.auto_start_btn.pack(side=tk.LEFT, padx=5)

        self.auto_stop_btn = tk.Button(auto_frame, text="停止自动抢购", command=self.stop_auto_seckill, bg="lightcoral", state=tk.DISABLED)
        self.auto_stop_btn.pack(side=tk.LEFT, padx=5)

        # 第六行：毫秒级倒计时显示
        self.countdown_label = tk.Label(self.root, text="", font=("Arial", 14, "bold"), fg="red")
        self.countdown_label.grid(row=8, column=0, columnspan=2, pady=5)

        # 第七行：当前时间显示
        self.current_time_label = tk.Label(self.root, text="", font=("Arial", 10), fg="blue")
        self.current_time_label.grid(row=9, column=0, columnspan=2, pady=2)

        # 第八行：时间同步
        time_frame = tk.Frame(self.root)
        time_frame.grid(row=10, column=0, columnspan=2, pady=5)

        tk.Button(time_frame, text="同步网络时间", command=self.sync_network_time).pack(side=tk.LEFT, padx=5)
        self.time_diff_label = tk.Label(time_frame, text="时间差: 未同步")
        self.time_diff_label.pack(side=tk.LEFT, padx=5)

        tk.Button(time_frame, text="测试延迟设置", command=self.test_delay_setting).pack(side=tk.LEFT, padx=5)
        self.delay_status_label = tk.Label(time_frame, text="延迟: 0ms")
        self.delay_status_label.pack(side=tk.LEFT, padx=5)

        self.time_diff = 0  # 本地时间与网络时间的差值（秒）

    def get_custom_delay(self):
        """获取自定义延迟（秒）"""
        try:
            delay_ms = int(self.custom_delay_entry.get())
            return delay_ms / 1000.0  # 转换为秒
        except ValueError:
            return 0.0

    def test_delay_setting(self):
        """测试延迟设置"""
        delay_ms = self.get_custom_delay() * 1000
        self.delay_status_label.config(text=f"延迟: {delay_ms:.0f}ms")
        if delay_ms > 0:
            self.result_queue.put(f"延迟设置: 将在目标时间后{delay_ms:.0f}毫秒执行\n")
        elif delay_ms < 0:
            self.result_queue.put(f"延迟设置: 将在目标时间前{abs(delay_ms):.0f}毫秒执行\n")
        else:
            self.result_queue.put("延迟设置: 准确在目标时间执行\n")

    def get_precise_time(self):
        """获取精确的当前时间（考虑网络时间差）"""
        return time.time() + self.time_diff

    def get_network_time(self, url="http://f.m.suning.com/api/ct.do"):
        """获取网络时间，返回时间差（秒）"""
        try:
            start_time = time.time() * 1000
            response = requests.get(url, timeout=5)
            end_time = time.time() * 1000

            if response.status_code == 200:
                # 尝试解析JSON格式的响应
                try:
                    data = response.json()
                    if 'currentTime' in data:
                        network_time = int(data['currentTime'])
                    else:
                        # 如果没有currentTime字段，尝试其他可能的字段
                        network_time = int(data.get('time', data.get('timestamp', 0)))
                except (json.JSONDecodeError, ValueError):
                    # 如果不是JSON格式，尝试直接解析为数字
                    try:
                        network_time = int(response.text.strip())
                    except ValueError:
                        print(f"无法解析时间响应: {response.text}")
                        return 0

                local_time = (start_time + end_time) / 2
                time_diff = network_time - local_time
                return time_diff / 1000  # 转换为秒
            else:
                print(f"获取网络时间失败，状态码: {response.status_code}")
                return 0
        except Exception as e:
            print(f"获取网络时间失败: {e}")
            return 0

    def get_network_time_alternative(self):
        """备用的网络时间获取方法"""
        alternative_urls = [
            "http://api.m.taobao.com/rest/api3.do?api=mtop.common.getTimestamp",
            "https://www.baidu.com",  # 使用响应头的Date字段
        ]

        for url in alternative_urls:
            try:
                if "baidu.com" in url:
                    # 使用百度响应头获取时间
                    response = requests.head(url, timeout=5)
                    if response.status_code == 200 and 'Date' in response.headers:
                        from email.utils import parsedate_to_datetime
                        date_str = response.headers['Date']
                        dt = parsedate_to_datetime(date_str)
                        network_time = dt.timestamp() * 1000
                        local_time = time.time() * 1000
                        time_diff = network_time - local_time
                        return time_diff / 1000
                else:
                    # 尝试其他API
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            network_time = int(data.get('data', {}).get('t', 0))
                            if network_time > 0:
                                local_time = time.time() * 1000
                                time_diff = network_time - local_time
                                return time_diff / 1000

                        except Exception:
                            continue
            except Exception:
                continue

        return 0

    def sync_network_time_multi(self, samples=5, primary_url="http://f.m.suning.com/api/ct.do"):
        """多次采样网络时间，取中位数减少抖动/回跳"""
        diffs = []
        for _ in range(samples):
            d = self.get_network_time(primary_url)
            if d == 0:
                d = self.get_network_time_alternative()
            if d != 0:
                diffs.append(d)
            time.sleep(0.05)
        if not diffs:
            return 0
        diffs.sort()
        return diffs[len(diffs)//2]

    def sync_network_time(self):
        """同步网络时间"""
        def sync():
            # 多次采样，降低抖动/回跳
            self.time_diff = self.sync_network_time_multi()
            if self.time_diff == 0:
                self.time_diff = self.get_network_time_alternative()

            # 更新UI显示
            if self.time_diff == 0:
                status_text = "时间差: 同步失败"
                result_text = "网络时间同步失败，将使用本地时间\n"
            else:
                status_text = f"时间差: {self.time_diff:.3f}秒"
                result_text = f"网络时间同步完成，时间差: {self.time_diff:.3f}秒\n"

            self.root.after(0, lambda: self.time_diff_label.config(text=status_text))
            self.result_queue.put(result_text)

        threading.Thread(target=sync, daemon=True).start()

    def calculate_wait_time_ms(self, current_hour):
        """计算等待时间直到下一个抢购时间（10:00:00或16:00:00），精确到毫秒"""
        now = datetime.now()
        now_timestamp = now.timestamp()

        if 0 <= current_hour < 10:
            # 目标时间 10:00:00.000
            target_time = now.replace(hour=10, minute=0, second=0, microsecond=0)
            session_info = "10点场次"
        elif 10 <= current_hour < 17:
            # 目标时间 16:00:00.000
            target_time = now.replace(hour=16, minute=0, second=0, microsecond=0)
            session_info = "16点场次"
        else:
            # 目标下一天的10:00:00.000
            target_time = now.replace(hour=10, minute=0, second=0, microsecond=0) + timedelta(days=1)
            session_info = "次日10点场次"

        # 计算等待时间（秒，包含毫秒精度）
        target_timestamp = target_time.timestamp()
        wait_time = target_timestamp - now_timestamp

        return max(0, wait_time), session_info, target_time

    def update_countdown_ms(self):
        """更新毫秒级倒计时显示（以网络时间为基准）"""
        current_precise = self.get_precise_time()  # 网络时间校正后的时间戳（秒）
        display_now = datetime.fromtimestamp(current_precise)
        self.current_time_label.config(text=f"当前时间(网): {display_now.strftime('%H:%M:%S.%f')[:-3]}")

        if self.is_auto_waiting:
            current_hour = display_now.hour
            _, session_info, target_time = self.calculate_wait_time_ms(current_hour)
            # 将目标时间换算到“网络时间基准”的差（目标时间加上 time_diff 与 get_precise_time 对齐）
            target_ts = target_time.timestamp() + self.time_diff
            adjusted_wait_time = target_ts - current_precise - self.get_custom_delay()

            if adjusted_wait_time > 0:
                hours = int(adjusted_wait_time // 3600)
                minutes = int((adjusted_wait_time % 3600) // 60)
                seconds = int(adjusted_wait_time % 60)
                milliseconds = int((adjusted_wait_time % 1) * 1000)

                delay_info = ""
                custom_delay = self.get_custom_delay()
                if custom_delay != 0:
                    delay_info = f" (延迟{custom_delay*1000:.0f}ms)"

                countdown_text = f"距离{session_info}还有: {hours:02d}:{minutes:02d}:{seconds:02d}.{milliseconds:03d}{delay_info}"
                self.countdown_label.config(text=countdown_text, fg="red")
            else:
                self.countdown_label.config(text="🚀 正在执行抢购...", fg="green")
        else:
            self.countdown_label.config(text="")

        # 每50毫秒更新一次以获得平滑的显示效果
        self.root.after(50, self.update_countdown_ms)

    def import_accounts(self):
        self.accounts = []
        accounts_input = self.accounts_text.get("1.0", tk.END).strip().split('\n')
        for account in accounts_input:
            if '----' in account:
                self.accounts.append(account.split('----'))
        messagebox.showinfo("提示", f"已导入 {len(self.accounts)} 个账号")

    def seckill_single_request(self, account, token, is_even, round_num):
        """单次抢购请求 - 复用会话，加速发送，不检测返回内容"""
        item_code = self.item_code1.get() if is_even else self.item_code2.get()
        url = f"http://**************:8080/wxopen/app-activity/AHSecKill/lotteryAction?ticket={token}&itemCode={item_code}&range={self.range_value}"
        try:
            request_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            # 使用持久会话，降低握手成本；关闭重定向以减少开销
            response = self.session.post(url, data=b"{}", headers=self.headers, timeout=2.5, allow_redirects=False)
            response_text = response.text
            return f"[{request_time}] 账号: {account} 第{round_num+1}轮 - {response_text}"
        except Exception as e:
            error_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            return f"[{error_time}] 账号: {account} 第{round_num+1}轮 - 网络错误: {str(e)}"

    def save_results_to_file(self, all_results):
        """保存结果到测试111.txt文件"""
        try:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            with open('测试111.txt', 'w', encoding='utf-8') as f:
                f.write(f"========== 抢购结果导出 - {timestamp} ==========\n")
                f.write(f"总请求数: {len(all_results)}\n")
                f.write(f"账号数: {len(self.accounts)}\n")
                f.write(f"抢购轮数: 39轮\n")
                f.write(f"策略: 分轮抢购，每轮所有账号同时执行\n")
                f.write("=" * 80 + "\n\n")

                # 按账号分组显示结果
                for account, _ in self.accounts:
                    f.write(f"账号: {account}\n")
                    f.write("-" * 50 + "\n")
                    account_results = [r for r in all_results if f"账号: {account}" in r]
                    for result in account_results:
                        f.write(result + "\n")
                    f.write("\n")

                f.write("=" * 80 + "\n")

                # 统计信息
                success_count = len([r for r in all_results if 'true' in r.lower()])
                error_count = len([r for r in all_results if '网络错误' in r or '异常' in r])

                f.write(f"统计汇总:\n")
                f.write(f"- 成功响应(包含true): {success_count}个\n")
                f.write(f"- 网络错误: {error_count}个\n")
                f.write(f"- 其他响应: {len(all_results) - success_count - error_count}个\n")
                f.write("=" * 80 + "\n")
                f.write("结果导出完成\n")

            self.result_queue.put(f"结果已保存到: 测试111.txt (共{len(all_results)}条记录)\n")

        except Exception as e:
            self.result_queue.put(f"保存文件失败: {str(e)}\n")

    def execute_seckill(self):
        """执行秒杀 - 分39轮抢购，每轮所有账号同时执行"""
        if not self.accounts:
            self.result_queue.put("错误：请先导入账号！\n")
            return

        start_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        custom_delay = self.get_custom_delay() * 1000

        self.result_queue.put(f"========== 秒杀开始时间: {start_time} (延迟{custom_delay:.0f}ms) ==========\n")
        self.result_queue.put(f"策略: 分39轮抢购，每轮所有账号同时执行\n")
        self.result_queue.put(f"账号数: {len(self.accounts)} 个\n")
        self.result_queue.put(f"总轮数: 39轮\n")
        self.result_queue.put(f"预计总请求数: {len(self.accounts) * 39} 个\n")

        # 存储所有请求的结果
        all_results = []
        all_futures = []  # 存储所有Future对象

        # 使用线程池
        max_workers = min(300, len(self.accounts) * 39)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 分39轮执行
            for round_num in range(39):  # 0-38，共39轮
                self.result_queue.put(f"🚀 第{round_num + 1}轮抢购开始（所有账号同时执行）\n")

                # 每轮所有账号同时提交请求，不等待完成
                for account_index, (account, token) in enumerate(self.accounts):
                    is_even = (account_index % 2 == 0)

                    # 提交单个请求任务
                    future = executor.submit(
                        self.seckill_single_request,
                        account,
                        token,
                        is_even,
                        round_num
                    )
                    all_futures.append(future)

                # 不等待当前轮完成，直接进行下一轮
                # 这样可以实现每轮之间无间隔的连续抢购

            self.result_queue.put(f"✅ 已提交所有39轮请求，共 {len(all_futures)} 个任务\n")
            self.result_queue.put(f"所有轮次已同时启动，正在等待结果...\n")

            # 等待所有请求完成并收集结果
            completed_count = 0
            for future in all_futures:
                try:
                    result = future.result()
                    all_results.append(result)
                    completed_count += 1

                    # 每完成50个请求显示一次进度
                    if completed_count % 50 == 0 or completed_count == len(all_futures):
                        progress = (completed_count / len(all_futures)) * 100
                        self.result_queue.put(f"进度: {completed_count}/{len(all_futures)} ({progress:.1f}%)\n")

                except Exception as e:
                    error_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    all_results.append(f"[{error_time}] 任务执行异常: {str(e)}")

        # 统计结果
        end_time = datetime.now().strftime('%H:%M:%S.%f')[:-3]

        # 验证每个账号的执行次数
        account_round_count = {}
        for result in all_results:
            for account, _ in self.accounts:
                if f"账号: {account}" in result:
                    if account not in account_round_count:
                        account_round_count[account] = 0
                    account_round_count[account] += 1
                    break

        # 分析结果
        success_results = [r for r in all_results if 'true' in r.lower()]
        error_results = [r for r in all_results if '网络错误' in r or '异常' in r]

        # 显示汇总结果
        self.result_queue.put(f"\n========== 抢购汇总 ==========\n")
        self.result_queue.put(f"开始时间: {start_time}\n")
        self.result_queue.put(f"结束时间: {end_time}\n")
        self.result_queue.put(f"总账号数: {len(self.accounts)}\n")
        self.result_queue.put(f"执行轮数: 39轮\n")
        self.result_queue.put(f"总请求数: {len(all_results)}\n")
        self.result_queue.put(f"包含'true'的响应: {len(success_results)}个\n")
        self.result_queue.put(f"网络错误: {len(error_results)}个\n")

        # 验证每个账号的执行次数
        self.result_queue.put(f"\n========== 各账号轮次验证 ==========\n")
        for account, _ in self.accounts:
            actual_rounds = account_round_count.get(account, 0)
            status = "✅" if actual_rounds == 39 else "❌"
            self.result_queue.put(f"{status} 账号 {account}: 执行了 {actual_rounds} 轮\n")

        # 保存所有结果到文件
        self.save_results_to_file(all_results)

        self.result_queue.put(f"========== 所有结果已导出到测试111.txt ==========\n")
        self.result_queue.put(f"========== 秒杀完成 ==========\n\n")

    def update_results(self):
        # 从队列中获取结果并更新 GUI
        while not self.result_queue.empty():
            result = self.result_queue.get()
            self.results_text.insert(tk.END, result)
            self.results_text.see(tk.END)  # 自动滚动到最新结果
        self.root.after(100, self.update_results)  # 每 100ms 检查一次

    def start_seckill(self):
        """立即开始秒杀"""
        if self.is_seckilling:
            messagebox.showwarning("警告", "秒杀正在进行中，请等待完成！")
            return

        self.is_seckilling = True
        self.results_text.delete("1.0", tk.END)  # 清空结果

        def run_seckill():
            try:
                self.execute_seckill()
            finally:
                self.is_seckilling = False

        # 在单独线程中运行秒杀任务，避免阻塞 GUI
        threading.Thread(target=run_seckill, daemon=True).start()

    def precise_wait_until(self, target_timestamp):
        """精确等待到指定时间戳（毫秒精度）"""
        while True:
            current_time = self.get_precise_time()
            remaining = target_timestamp - current_time

            if remaining <= 0:
                break
            elif remaining > 1:
                # 如果还有超过1秒，粗略等待
                time.sleep(min(0.1, remaining - 0.1))
            elif remaining > 0.01:
                # 如果在1秒内，精细等待
                time.sleep(min(0.001, remaining / 2))
            else:
                # 最后10毫秒内，忙等待
                while self.get_precise_time() < target_timestamp:
                    pass
                break

    def start_auto_seckill(self):
        """开始自动抢购"""
        if self.is_auto_waiting:
            messagebox.showwarning("警告", "自动抢购已在运行中！")
            return

        if not self.accounts:
            messagebox.showerror("错误", "请先导入账号！")
            return

        self.is_auto_waiting = True
        self.auto_start_btn.config(state=tk.DISABLED)
        self.auto_stop_btn.config(state=tk.NORMAL)

        # 先同步时间和更新延迟状态
        self.sync_network_time()
        self.test_delay_setting()

        def auto_seckill_worker():
            try:
                # 等待一下让时间同步完成
                time.sleep(1)

                current_hour = datetime.now().hour
                wait_time, session_info, target_time = self.calculate_wait_time_ms(current_hour)
                custom_delay = self.get_custom_delay()

                self.result_queue.put(f"开始自动抢购 - 目标: {session_info}\n")
                self.result_queue.put(f"目标时间: {target_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}\n")
                self.result_queue.put(f"时间差补偿: {self.time_diff:.3f}秒\n")
                self.result_queue.put(f"自定义延迟: {custom_delay*1000:.0f}毫秒\n")

                # 应用自定义延迟调整等待时间
                adjusted_wait_time = wait_time - custom_delay

                if adjusted_wait_time > 5:  # 如果等待时间超过5秒
                    self.result_queue.put(f"调整后等待时间: {adjusted_wait_time:.3f}秒\n")

                    # 粗略等待到距离目标时间5秒
                    rough_wait = adjusted_wait_time - 5
                    start_wait = time.time()
                    while self.is_auto_waiting and (time.time() - start_wait) < rough_wait:
                        time.sleep(0.1)

                    if not self.is_auto_waiting:
                        self.result_queue.put("自动抢购已被停止\n")
                        return

                if self.is_auto_waiting:
                    # 计算精确的目标时间戳（应用自定义延迟）
                    target_timestamp = target_time.timestamp() + custom_delay

                    self.result_queue.put("进入精确等待模式...\n")

                    # 精确等待到目标时间（内部使用 get_precise_time 走网络校时）
                    self.precise_wait_until(target_timestamp)

                    if self.is_auto_waiting:
                        self.result_queue.put("========== 自动抢购开始 ==========\n")
                        self.execute_seckill()
                        self.result_queue.put("========== 自动抢购结束 ==========\n")

            except Exception as e:
                self.result_queue.put(f"自动抢购出错: {str(e)}\n")
            finally:
                self.is_auto_waiting = False
                self.root.after(0, lambda: self.auto_start_btn.config(state=tk.NORMAL))
                self.root.after(0, lambda: self.auto_stop_btn.config(state=tk.DISABLED))

        self.auto_thread = threading.Thread(target=auto_seckill_worker, daemon=True)
        self.auto_thread.start()

    def stop_auto_seckill(self):
        """停止自动抢购"""
        self.is_auto_waiting = False
        self.auto_start_btn.config(state=tk.NORMAL)
        self.auto_stop_btn.config(state=tk.DISABLED)
        self.result_queue.put("自动抢购已停止\n")

if __name__ == "__main__":
    root = tk.Tk()
    app = SecKillApp(root)
    root.mainloop()