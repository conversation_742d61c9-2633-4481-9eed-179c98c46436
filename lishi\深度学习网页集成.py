from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import torch
import json
import time
import os
import numpy as np
from 深度学习拼图破解器 import DeepPuzzleSolver

app = Flask(__name__)
CORS(app)

class DeepLearningPuzzleAPI:
    """深度学习拼图API服务"""
    def __init__(self, model_path="best_puzzle_model.pth"):
        self.solver = None
        self.model_loaded = False
        self.model_info = {}
        self.load_model(model_path)
    
    def load_model(self, model_path):
        """加载深度学习模型"""
        try:
            if os.path.exists(model_path):
                self.solver = DeepPuzzleSolver(model_path)
                self.model_loaded = True
                
                # 加载模型信息
                checkpoint = torch.load(model_path, map_location='cpu')
                if isinstance(checkpoint, dict):
                    self.model_info = {
                        'accuracy': checkpoint.get('accuracy', 0) * 100,
                        'epoch': checkpoint.get('epoch', 0),
                        'model_type': 'DeepLearning',
                        'device': str(self.solver.device)
                    }
                else:
                    self.model_info = {
                        'model_type': 'DeepLearning',
                        'device': str(self.solver.device)
                    }
                
                print(f"✅ 深度学习模型加载成功")
                print(f"📊 模型信息: {self.model_info}")
            else:
                print(f"❌ 模型文件不存在: {model_path}")
                print("💡 请先运行训练脚本生成模型")
        except Exception as e:
            print(f"❌ 加载模型失败: {e}")
            self.model_loaded = False
    
    def solve_puzzle(self, board, pieces, mode='rotating'):
        """使用深度学习求解拼图"""
        if not self.model_loaded:
            return {
                'success': False,
                'error': '深度学习模型未加载',
                'solution': [],
                'final_board': board,
                'time_ms': 0,
                'model_type': 'None'
            }
        
        try:
            start_time = time.time()
            
            # 使用深度学习求解
            solution_steps, final_board = self.solver.solve(board, pieces, mode)
            
            solve_time = (time.time() - start_time) * 1000
            
            # 转换解决方案格式
            formatted_solution = []
            for step in solution_steps:
                formatted_solution.append({
                    'step': step['step'],
                    'piece': step['piece'],
                    'position': step['position'],
                    'rotation': step.get('rotation', 0),
                    'confidence': step.get('confidence', 1.0)
                })
            
            return {
                'success': len(solution_steps) > 0,
                'solution': formatted_solution,
                'final_board': final_board,
                'time_ms': solve_time,
                'steps': len(solution_steps),
                'model_type': 'DeepLearning',
                'model_info': self.model_info
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'深度学习求解失败: {str(e)}',
                'solution': [],
                'final_board': board,
                'time_ms': 0,
                'model_type': 'DeepLearning'
            }
    
    def get_hint(self, board, pieces, mode='rotating'):
        """获取深度学习提示"""
        if not self.model_loaded:
            return {
                'success': False,
                'error': '深度学习模型未加载',
                'hint': {'message': '模型未加载'},
                'confidence': 0.0
            }
        
        try:
            hint = self.solver.get_hint(board, pieces, mode)
            return {
                'success': hint['success'],
                'hint': hint,
                'confidence': hint.get('confidence', 0.0),
                'model_type': 'DeepLearning'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'获取提示失败: {str(e)}',
                'hint': {'message': '获取提示失败'},
                'confidence': 0.0
            }
    
    def batch_solve(self, puzzles):
        """批量求解"""
        if not self.model_loaded:
            return {
                'success': False,
                'error': '深度学习模型未加载',
                'results': []
            }
        
        try:
            results = self.solver.batch_solve(puzzles)
            return {
                'success': True,
                'results': results,
                'total_puzzles': len(puzzles),
                'solved_count': sum(1 for r in results if r['success']),
                'model_type': 'DeepLearning'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'批量求解失败: {str(e)}',
                'results': []
            }

# 创建API实例
puzzle_api = DeepLearningPuzzleAPI()

@app.route('/')
def index():
    """返回网页"""
    return send_from_directory('.', 'cainiao699.html')

@app.route('/api/solve', methods=['POST'])
def api_solve():
    """深度学习求解API"""
    try:
        data = request.get_json()
        
        if not data or 'board' not in data or 'pieces' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必要参数: board 和 pieces'
            }), 400
        
        board = data['board']
        pieces = data['pieces']
        mode = data.get('mode', 'rotating')
        
        result = puzzle_api.solve_puzzle(board, pieces, mode)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500

@app.route('/api/hint', methods=['POST'])
def api_hint():
    """深度学习提示API"""
    try:
        data = request.get_json()
        board = data['board']
        pieces = data['pieces']
        mode = data.get('mode', 'rotating')
        
        result = puzzle_api.get_hint(board, pieces, mode)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取提示失败: {str(e)}'
        }), 500

@app.route('/api/status', methods=['GET'])
def api_status():
    """获取深度学习模型状态"""
    return jsonify({
        'model_loaded': puzzle_api.model_loaded,
        'model_info': puzzle_api.model_info,
        'device': puzzle_api.model_info.get('device', 'unknown'),
        'ready': puzzle_api.model_loaded,
        'model_type': 'DeepLearning'
    })

@app.route('/api/batch_solve', methods=['POST'])
def api_batch_solve():
    """批量求解API"""
    try:
        data = request.get_json()
        puzzles = data.get('puzzles', [])
        
        if not puzzles:
            return jsonify({
                'success': False,
                'error': '没有提供拼图数据'
            }), 400
        
        result = puzzle_api.batch_solve(puzzles)
        return jsonify(result)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'批量求解失败: {str(e)}'
        }), 500

@app.route('/api/model_info', methods=['GET'])
def api_model_info():
    """获取模型详细信息"""
    if not puzzle_api.model_loaded:
        return jsonify({
            'loaded': False,
            'error': '模型未加载'
        })
    
    # 获取模型参数数量
    total_params = sum(p.numel() for p in puzzle_api.solver.model.parameters())
    trainable_params = sum(p.numel() for p in puzzle_api.solver.model.parameters() if p.requires_grad)
    
    return jsonify({
        'loaded': True,
        'model_info': puzzle_api.model_info,
        'parameters': {
            'total': total_params,
            'trainable': trainable_params
        },
        'device': str(puzzle_api.solver.device),
        'model_type': 'DeepLearning Neural Network'
    })

@app.route('/api/benchmark', methods=['POST'])
def api_benchmark():
    """性能基准测试"""
    if not puzzle_api.model_loaded:
        return jsonify({
            'success': False,
            'error': '模型未加载'
        })
    
    # 标准测试用例
    benchmark_cases = [
        {
            'name': '3x3 简单',
            'board': [[1, 0, 0], [0, 0, 0], [0, 0, 2]],
            'pieces': {"T": 1, "田": 0, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '4x4 中等',
            'board': [[1, 0, 0, 1], [0, 0, 0, 0], [0, 0, 0, 0], [2, 0, 0, 2]],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 0, "Z": 0, "L": 0}
        },
        {
            'name': '5x5 复杂',
            'board': [[0, 1, 0, 0, 0], [0, 0, 0, 1, 0], [2, 0, 0, 0, 0], [0, 0, 1, 0, 2], [0, 0, 0, 0, 0]],
            'pieces': {"T": 1, "田": 1, "横杠竖条": 1, "Z": 0, "L": 0}
        }
    ]
    
    results = []
    total_time = 0
    success_count = 0
    
    for case in benchmark_cases:
        start_time = time.time()
        result = puzzle_api.solve_puzzle(case['board'], case['pieces'])
        solve_time = time.time() - start_time
        total_time += solve_time
        
        if result['success']:
            success_count += 1
        
        results.append({
            'name': case['name'],
            'success': result['success'],
            'time_ms': solve_time * 1000,
            'steps': result.get('steps', 0)
        })
    
    return jsonify({
        'success': True,
        'results': results,
        'summary': {
            'success_rate': success_count / len(benchmark_cases),
            'average_time_ms': (total_time / len(benchmark_cases)) * 1000,
            'total_tests': len(benchmark_cases),
            'passed_tests': success_count
        },
        'model_type': 'DeepLearning'
    })

if __name__ == '__main__':
    print("🧠 深度学习拼图破解器 Web服务")
    print("=" * 60)
    print(f"📊 模型状态: {'已加载' if puzzle_api.model_loaded else '未加载'}")
    if puzzle_api.model_loaded:
        print(f"🖥️ 设备: {puzzle_api.model_info.get('device', 'unknown')}")
        if 'accuracy' in puzzle_api.model_info:
            print(f"🎯 模型准确率: {puzzle_api.model_info['accuracy']:.1f}%")
    print("🌐 访问地址: http://localhost:5000")
    print("📋 API端点:")
    print("   POST /api/solve - 深度学习求解")
    print("   POST /api/hint - 深度学习提示")
    print("   GET  /api/status - 模型状态")
    print("   POST /api/batch_solve - 批量求解")
    print("   GET  /api/model_info - 模型详情")
    print("   POST /api/benchmark - 性能测试")
    print("\n💡 提示: 确保已运行训练脚本生成模型文件")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
