#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
getSmsCodeRandom请求Sign分析器
专门用于分析短信验证码API中的签名生成算法
"""

import hashlib
import hmac
import time
import json
import base64
import urllib.parse
import random
import string
import requests
from datetime import datetime

class GetSmsCodeRandomAnalyzer:
    def __init__(self):
        self.common_secrets = [
            'secret', 'app_secret', 'api_secret', 'sms_secret',
            '123456', 'abcdef', 'mobile_key', 'sms_key',
            'your_secret_key', 'default_secret', 'api_key_123'
        ]
    
    def analyze_with_real_data(self, phone, known_sign=None, timestamp=None):
        """使用真实数据分析签名算法"""
        print(f"🔍 分析getSmsCodeRandom请求签名")
        print(f"手机号: {phone}")
        if known_sign:
            print(f"已知签名: {known_sign}")
        if timestamp:
            print(f"时间戳: {timestamp}")
        print("=" * 60)
        
        # 如果没有提供时间戳，使用当前时间
        if not timestamp:
            timestamp = str(int(time.time()))
        
        # 生成各种可能的签名
        possible_signs = self.generate_all_possible_signs(phone, timestamp)
        
        if known_sign:
            # 尝试匹配已知签名
            matches = self.find_matching_algorithms(possible_signs, known_sign)
            if matches:
                print("🎯 找到匹配的算法:")
                for match in matches:
                    print(f"  ✅ {match['algorithm']}")
                    print(f"     签名字符串: {match['sign_string']}")
                    print(f"     生成的签名: {match['generated_sign']}")
                    print(f"     密钥: {match.get('secret', 'N/A')}")
                    print()
            else:
                print("❌ 未找到匹配的算法，可能需要更多信息")
        else:
            # 显示所有可能的签名
            print("🔧 可能的签名算法和结果:")
            for sign_info in possible_signs[:10]:  # 只显示前10个
                print(f"  算法: {sign_info['algorithm']}")
                print(f"  签名: {sign_info['generated_sign']}")
                print(f"  字符串: {sign_info['sign_string']}")
                print()
    
    def generate_all_possible_signs(self, phone, timestamp):
        """生成所有可能的签名组合"""
        results = []
        
        # 生成随机数
        nonce = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        
        for secret in self.common_secrets:
            # 模式1: MD5(phone + timestamp + secret)
            sign_string = f"{phone}{timestamp}{secret}"
            sign = hashlib.md5(sign_string.encode()).hexdigest()
            results.append({
                'algorithm': 'MD5(phone+timestamp+secret)',
                'sign_string': sign_string,
                'generated_sign': sign,
                'secret': secret
            })
            
            # 模式2: MD5(secret + phone + timestamp)
            sign_string = f"{secret}{phone}{timestamp}"
            sign = hashlib.md5(sign_string.encode()).hexdigest()
            results.append({
                'algorithm': 'MD5(secret+phone+timestamp)',
                'sign_string': sign_string,
                'generated_sign': sign,
                'secret': secret
            })
            
            # 模式3: MD5(phone={phone}&timestamp={timestamp}&key={secret})
            sign_string = f"phone={phone}&timestamp={timestamp}&key={secret}"
            sign = hashlib.md5(sign_string.encode()).hexdigest()
            results.append({
                'algorithm': 'MD5(params_string)',
                'sign_string': sign_string,
                'generated_sign': sign,
                'secret': secret
            })
            
            # 模式4: HMAC-SHA1
            sign_string = f"phone={phone}&timestamp={timestamp}"
            sign = hmac.new(secret.encode(), sign_string.encode(), hashlib.sha1).hexdigest()
            results.append({
                'algorithm': 'HMAC-SHA1',
                'sign_string': sign_string,
                'generated_sign': sign,
                'secret': secret
            })
            
            # 模式5: MD5(getSmsCodeRandom + phone + timestamp + secret)
            sign_string = f"getSmsCodeRandom{phone}{timestamp}{secret}"
            sign = hashlib.md5(sign_string.encode()).hexdigest()
            results.append({
                'algorithm': 'MD5(method+phone+timestamp+secret)',
                'sign_string': sign_string,
                'generated_sign': sign,
                'secret': secret
            })
            
            # 模式6: 参数排序后MD5
            params = {'phone': phone, 'timestamp': timestamp, 'method': 'getSmsCodeRandom'}
            sorted_params = '&'.join([f'{k}={v}' for k, v in sorted(params.items())])
            sign_string = sorted_params + '&key=' + secret
            sign = hashlib.md5(sign_string.encode()).hexdigest()
            results.append({
                'algorithm': 'MD5(sorted_params+key)',
                'sign_string': sign_string,
                'generated_sign': sign,
                'secret': secret
            })
        
        return results
    
    def find_matching_algorithms(self, possible_signs, target_sign):
        """查找匹配的算法"""
        matches = []
        for sign_info in possible_signs:
            if sign_info['generated_sign'] == target_sign:
                matches.append(sign_info)
            elif sign_info['generated_sign'].upper() == target_sign.upper():
                sign_info['note'] = '大小写匹配'
                matches.append(sign_info)
            elif sign_info['generated_sign'][:len(target_sign)] == target_sign:
                sign_info['note'] = '前缀匹配'
                matches.append(sign_info)
        return matches
    
    def test_with_multiple_samples(self, samples):
        """使用多个样本测试算法一致性"""
        print("🧪 多样本一致性测试")
        print("=" * 60)
        
        if len(samples) < 2:
            print("❌ 需要至少2个样本进行一致性测试")
            return
        
        # 对每个样本生成可能的签名
        all_algorithms = {}
        
        for i, sample in enumerate(samples):
            phone = sample['phone']
            timestamp = sample['timestamp']
            known_sign = sample['sign']
            
            print(f"样本 {i+1}: {phone}, {timestamp}, {known_sign}")
            
            possible_signs = self.generate_all_possible_signs(phone, timestamp)
            matches = self.find_matching_algorithms(possible_signs, known_sign)
            
            for match in matches:
                algo_key = f"{match['algorithm']}_{match.get('secret', '')}"
                if algo_key not in all_algorithms:
                    all_algorithms[algo_key] = []
                all_algorithms[algo_key].append(i)
        
        # 找出在所有样本中都匹配的算法
        consistent_algorithms = []
        for algo_key, sample_indices in all_algorithms.items():
            if len(sample_indices) == len(samples):
                consistent_algorithms.append(algo_key)
        
        if consistent_algorithms:
            print("\n✅ 一致性算法:")
            for algo in consistent_algorithms:
                print(f"  {algo}")
        else:
            print("\n❌ 未找到在所有样本中都一致的算法")
    
    def generate_sign_code(self, algorithm, secret):
        """生成对应算法的Python代码"""
        print(f"\n📝 {algorithm} 的Python实现代码:")
        print("=" * 40)
        
        if 'MD5(phone+timestamp+secret)' in algorithm:
            code = f'''
def generate_sign(phone):
    import hashlib
    import time
    
    timestamp = str(int(time.time()))
    secret = "{secret}"
    sign_string = f"{{phone}}{{timestamp}}{{secret}}"
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    
    return sign, timestamp
'''
        elif 'HMAC-SHA1' in algorithm:
            code = f'''
def generate_sign(phone):
    import hmac
    import hashlib
    import time
    
    timestamp = str(int(time.time()))
    secret = "{secret}"
    sign_string = f"phone={{phone}}&timestamp={{timestamp}}"
    sign = hmac.new(secret.encode(), sign_string.encode(), hashlib.sha1).hexdigest()
    
    return sign, timestamp
'''
        elif 'sorted_params' in algorithm:
            code = f'''
def generate_sign(phone):
    import hashlib
    import time
    
    timestamp = str(int(time.time()))
    secret = "{secret}"
    
    params = {{'phone': phone, 'timestamp': timestamp, 'method': 'getSmsCodeRandom'}}
    sorted_params = '&'.join([f'{{k}}={{v}}' for k, v in sorted(params.items())])
    sign_string = sorted_params + '&key=' + secret
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    
    return sign, timestamp, params
'''
        else:
            code = "# 请根据具体算法实现"
        
        print(code)

def interactive_analysis():
    """交互式分析"""
    print("🎯 getSmsCodeRandom签名分析器")
    print("=" * 60)
    
    analyzer = GetSmsCodeRandomAnalyzer()
    
    while True:
        print("\n选择分析模式:")
        print("1. 单个请求分析")
        print("2. 多样本一致性测试")
        print("3. 生成测试签名")
        print("4. 退出")
        
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            phone = input("请输入手机号: ").strip()
            known_sign = input("请输入已知签名 (可选): ").strip() or None
            timestamp = input("请输入时间戳 (可选): ").strip() or None
            
            analyzer.analyze_with_real_data(phone, known_sign, timestamp)
            
        elif choice == '2':
            print("请输入多个样本 (格式: 手机号,时间戳,签名)")
            print("输入空行结束:")
            
            samples = []
            while True:
                line = input().strip()
                if not line:
                    break
                try:
                    phone, timestamp, sign = line.split(',')
                    samples.append({
                        'phone': phone.strip(),
                        'timestamp': timestamp.strip(),
                        'sign': sign.strip()
                    })
                except ValueError:
                    print("格式错误，请重新输入")
            
            if samples:
                analyzer.test_with_multiple_samples(samples)
            
        elif choice == '3':
            phone = input("请输入手机号: ").strip()
            print("\n生成的测试签名:")
            analyzer.analyze_with_real_data(phone)
            
        elif choice == '4':
            break
        
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    # 可以直接运行交互式分析
    interactive_analysis()
    
    # 或者使用示例数据测试
    # analyzer = GetSmsCodeRandomAnalyzer()
    # analyzer.analyze_with_real_data("13800138000", "your_known_sign_here", "1640995200")
