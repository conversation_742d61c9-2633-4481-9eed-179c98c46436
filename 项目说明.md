# 短信发送工具项目说明

## 项目概述
本项目是将易语言短信发送工具转换为Python GUI应用程序的完整实现。

## 转换完成的功能

### ✅ 已实现的需求
1. **密码验证**: 默认密码1995，通过验证才能进入软件
2. **编辑框界面**: 
   - 标签0（配置保存）
   - 编辑框1（网址）
   - 选择框2（GET/POST访问方式）
   - 编辑框3（提交内容）
   - 编辑框4（协议头）
   - 手机号输入框（对应易语言中的编辑框1）
3. **快捷触发区域**: 9个指定按钮的请求功能
4. **一键发送**: 可以一次性发送所有请求
5. **单独发送**: 每个请求都可以单独触发
6. **日志系统**: 实时显示请求过程和结果

### 🎯 对应的易语言按钮映射
- **按钮25** → 大众工商网短信
- **按钮2** → 索尼音乐短信（包含验证码处理）
- **按钮28** → 人大金仓论坛短信
- **按钮24** → 水果批发平台短信
- **按钮4** → 水利部网站短信
- **按钮5** → 云标科技短信
- **按钮23** → 温柔怪物短信
- **按钮8** → 7net平台短信（包含验证码处理）
- **按钮3** → 天启教育短信

## 文件结构
```
├── 短信发送工具.py          # 主程序文件
├── 短信工具使用说明.md       # 详细使用说明
├── 项目说明.md             # 项目概述文档
├── 启动工具.bat            # Windows启动脚本
├── 启动工具.sh             # Linux/Mac启动脚本
├── 易语言.txt              # 原始易语言代码
└── sms_tool_config.json    # 配置文件（运行后生成）
```

## 技术特点

### 界面设计
- 使用tkinter构建现代化GUI界面
- 响应式布局，支持窗口缩放
- 分区域组织功能，界面清晰易用

### 网络请求
- 基于requests库实现HTTP请求
- 支持GET和POST方法
- 自动处理编码和协议头
- 30秒超时保护

### 多线程处理
- 所有网络请求在后台线程执行
- 避免界面卡死，提升用户体验
- 支持并发请求处理

### 配置管理
- JSON格式配置文件
- 支持保存和加载常用设置
- 自动保存用户输入

### 日志系统
- 实时显示请求过程
- 详细记录响应结果
- 支持日志清空功能

## 安全特性

### 访问控制
- 密码验证机制
- 防止未授权使用

### 请求保护
- 请求间隔控制
- 超时机制
- 错误处理

### 隐私保护
- 本地配置存储
- 不上传敏感信息

## 使用方式

### 快速启动
1. **Windows**: 双击 `启动工具.bat`
2. **Linux/Mac**: 运行 `bash 启动工具.sh`
3. **手动**: `python 短信发送工具.py`

### 基本操作
1. 输入密码：1995
2. 填写手机号
3. 选择功能按钮或一键发送
4. 查看日志结果

## 依赖要求
- Python 3.6+
- requests库
- tkinter（通常随Python安装）

## 注意事项

### 合规使用
- 仅用于测试和学习目的
- 不得用于骚扰他人
- 遵守相关法律法规

### 技术限制
- 部分平台需要验证码，目前使用默认值
- 网络环境需要稳定
- 某些平台可能有频率限制

## 后续优化建议

### 功能增强
1. 添加验证码识别功能
2. 支持代理设置
3. 添加请求统计功能
4. 支持批量手机号处理

### 界面优化
1. 添加主题切换
2. 支持字体大小调整
3. 添加快捷键支持
4. 优化响应式布局

### 安全增强
1. 加密配置文件
2. 添加访问日志
3. 支持多用户管理
4. 增强密码策略

## 版本历史
- **v1.0**: 初始版本，完成易语言代码转换

## 技术支持
如有问题或建议，请查看使用说明文档或联系开发者。
