# 🧩 GUI拼图破解器使用说明

## 🎯 功能概述

这是一个带图形界面的GPU加速拼图破解器，完全兼容您的网页端功能，并添加了以下特性：

### ✅ **核心功能**
- 🖥️ **图形界面操作** - 直观的棋盘编辑和结果展示
- 🚀 **GPU加速求解** - 利用RTX 3060进行高速计算
- 📁 **导入导出兼容** - 完全兼容网页端的JSON格式
- 🏆 **排行榜系统** - 记录前10名最佳成绩
- 👁️ **可视化展示** - 实时显示解决方案

### 🎮 **界面布局**

```
┌─────────────────────────────────────────────────────────────┐
│ 🧩 GPU拼图破解器 - 专业版                                    │
├─────────────────────┬───────────────────────────────────────┤
│ 🎮 棋盘操作          │ 🧩 方块设置                           │
│ ┌─────────────────┐ │ T: [1] 田: [1] 横杠竖条: [1]          │
│ │ 📋 棋盘编辑     │ │ Z: [1] L: [1]                         │
│ │ ✓ ✗ ✓ ✗ ✓ ✗ ✓ │ │                                       │
│ │ ✗ ✓ ✗ ✓ ✗ ✓ ✗ │ │ 🚀 GPU求解                            │
│ │ ✓ ✗ ✓ ✗ ✓ ✗ ✓ │ │ [🚀 GPU加速求解] 最大解数: [10]       │
│ │ ✗ ✓ ✗ ✓ ✗ ✓ ✗ │ │ ████████████ 求解中...                │
│ │ ✓ ✗ ✓ ✗ ✓ ✗ ✓ │ │                                       │
│ │ ✗ ✓ ✗ ✓ ✗ ✓ ✗ │ │ 📊 求解结果                           │
│ │ ✓ ✗ ✓ ✗ ✓ ✗ ✓ │ │ ┌─────────────────────────────────┐   │
│ └─────────────────┘ │ │ 解决方案1  0.123s  5步  ✅     │   │
│ [📁导入] [💾导出]    │ │ 解决方案2  0.145s  6步  ✅     │   │
│ [🎲随机棋盘]         │ │ 解决方案3  0.167s  7步  ✅     │   │
│                     │ └─────────────────────────────────┘   │
│                     │ [👁️查看] [💾保存] [🏆添加排行榜]      │
│                     │                                       │
│                     │ 🏆 前10名排行榜                       │
│                     │ ┌─────────────────────────────────┐   │
│                     │ │ #1  0.123s  7x7  2024-01-15    │   │
│                     │ │ #2  0.145s  8x8  2024-01-14    │   │
│                     │ │ #3  0.167s  6x6  2024-01-13    │   │
│                     │ └─────────────────────────────────┘   │
│                     │ [📋加载棋盘] [🗑️清空排行榜]          │
└─────────────────────┴───────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 启动程序
```bash
python 启动GPU拼图破解器.py
```
选择 "1. 🖥️ GUI界面版本"

### 2. 创建棋盘
- **调整大小**: 使用左上角的棋盘大小控制器 (3-10)
- **设置必需位置**: 左键点击格子 → 红色✓
- **设置禁止位置**: 右键点击格子 → 灰色✗
- **清空格子**: 中键点击格子

### 3. 设置方块
在右侧"🧩 方块设置"区域调整各种方块的数量：
- **T**: T形方块
- **田**: 正方形方块
- **横杠竖条**: 长条方块
- **Z**: Z形方块
- **L**: L形方块

### 4. GPU求解
- 点击"🚀 GPU加速求解"按钮
- 设置最大解决方案数量 (1-50)
- 观察GPU使用率上升
- 等待求解完成

### 5. 查看结果
- 双击结果列表查看详情
- 点击"👁️ 查看详情"显示解决方案
- 点击"💾 保存结果"导出完整结果

## 📁 导入导出功能

### 🔄 **完全兼容网页端格式**

#### 导入棋盘：
1. 点击"📁 导入棋盘"
2. 选择JSON文件
3. 自动加载棋盘和方块设置

#### 导出棋盘：
1. 点击"💾 导出棋盘"
2. 选择保存位置
3. 生成兼容网页端的JSON文件

#### JSON格式示例：
```json
{
  "board_size": 8,
  "board": [
    [0, 0, 0, 0, 0, 1, 2, 0],
    [0, 0, 0, 0, 0, 1, 1, 0],
    [0, 0, 0, 0, 0, 0, 1, 2],
    [0, 0, 0, 0, 0, 0, 1, 0],
    [0, 0, 0, 0, 0, 0, 2, 0],
    [0, 0, 0, 0, 0, 0, 0, 0],
    [0, 2, 0, 0, 0, 0, 0, 0],
    [0, 0, 0, 0, 0, 0, 0, 0]
  ],
  "piece_counts": {
    "T": 1,
    "田": 1,
    "横杠竖条": 1,
    "Z": 1,
    "L": 1
  },
  "show_process": false
}
```

## 🏆 排行榜系统

### **前10名展示**
- 自动按求解时间排序
- 显示排名、时间、棋盘大小、日期
- 双击可加载对应棋盘

### **添加到排行榜**
1. 完成求解后点击"🏆 添加到排行榜"
2. 自动保存当前成绩
3. 按时间排序，保留前10名

### **加载排行榜棋盘**
1. 双击排行榜条目
2. 或选中后点击"📋 加载棋盘"
3. 自动加载棋盘和方块设置

## 🎮 操作技巧

### **棋盘编辑**
- **快速设置**: 按住鼠标拖拽可连续设置
- **批量清空**: 中键拖拽可批量清空
- **撤销操作**: 使用"🔄 重置棋盘"

### **方块设置**
- **快速配置**: 使用数字键盘快速输入
- **预设配置**: 导入已保存的配置文件
- **随机测试**: 使用"🎲 随机棋盘"生成测试用例

### **结果查看**
- **实时预览**: 解决方案会在棋盘上短暂显示
- **详细信息**: 双击查看完整步骤和统计
- **图片保存**: 可保存解决方案为PNG图片

## ⚡ GPU性能优化

### **最佳设置**
- **棋盘大小**: 6x6 到 8x8 最佳
- **方块数量**: 3-5个方块
- **最大解数**: 10-20个

### **性能监控**
- 任务管理器 → 性能 → GPU
- 观察"计算"使用率
- 应该达到50-90%

### **故障排除**
1. **GPU使用率低**: 重启程序，检查CuPy安装
2. **求解失败**: 减少方块数量或调整棋盘
3. **界面卡顿**: 降低最大解决方案数量

## 📊 文件说明

### **自动生成的文件**
- `leaderboard.json` - 排行榜数据
- `solution_*.png` - 解决方案图片
- `*_results.json` - 求解结果文件

### **配置文件**
- 棋盘配置: 导出的JSON文件
- 排行榜: 自动保存到本地
- 结果记录: 可选择保存位置

## 🎯 使用场景

### **日常使用**
1. 创建或导入棋盘
2. 设置方块数量
3. GPU加速求解
4. 查看和保存结果

### **性能测试**
1. 使用不同大小的棋盘
2. 对比GPU和CPU性能
3. 记录最佳成绩到排行榜

### **批量处理**
1. 准备多个棋盘文件
2. 逐个导入求解
3. 批量保存结果

## 🔧 高级功能

### **自定义求解参数**
- 最大解决方案数量
- GPU并行度设置
- 求解超时时间

### **结果分析**
- GPU计算时间统计
- 求解步骤详情
- 性能对比分析

### **数据管理**
- 排行榜导入导出
- 批量结果处理
- 历史记录查询

---

## 🎉 总结

这个GUI版本提供了：
- ✅ **完整的图形界面操作**
- ✅ **兼容网页端的导入导出**
- ✅ **前10名排行榜展示**
- ✅ **实时GPU性能监控**
- ✅ **可视化结果展示**

现在您可以完全脱离网页端，使用这个功能更强大的桌面版本！🚀
