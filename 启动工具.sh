#!/bin/bash

echo "================================"
echo "     短信发送工具启动脚本"
echo "================================"
echo

echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误：未找到Python环境！"
        echo "请先安装Python 3.x"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "Python环境检查通过！"
echo

echo "正在检查依赖库..."
$PYTHON_CMD -c "import requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装requests库..."
    $PYTHON_CMD -m pip install requests
    if [ $? -ne 0 ]; then
        echo "错误：requests库安装失败！"
        exit 1
    fi
fi

echo "依赖库检查完成！"
echo

echo "正在启动短信发送工具..."
echo "默认密码：1995"
echo

$PYTHON_CMD "短信工具可编辑版.py"

if [ $? -ne 0 ]; then
    echo
    echo "程序运行出错！"
    read -p "按任意键退出..."
fi
