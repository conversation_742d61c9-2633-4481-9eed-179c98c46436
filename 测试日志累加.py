#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试安徽联通话费领取工具的日志累加功能
"""

from datetime import datetime
import os

def test_log_append():
    """测试日志累加功能"""
    print("🔍 测试日志累加功能...")
    print("=" * 50)
    
    log_file = "安徽话费.txt"
    
    # 模拟多次运行的日志写入
    test_data = [
        {
            "timestamp": "2024-01-01 10:00:00",
            "accounts": ["***********", "***********"],
            "results": [
                "[10:00:01.123] 账号: *********** 第1轮 - ✅领取成功: {\"success\":true}",
                "[10:00:01.456] 账号: *********** 第1轮 - ❌领取失败: {\"success\":false}",
            ]
        },
        {
            "timestamp": "2024-01-01 15:00:00", 
            "accounts": ["***********", "***********"],
            "results": [
                "[15:00:01.789] 账号: *********** 第1轮 - ✅领取成功: {\"success\":true}",
                "[15:00:01.999] 账号: *********** 第1轮 - ✅领取成功: {\"success\":true}",
            ]
        }
    ]
    
    # 清空现有日志文件
    if os.path.exists(log_file):
        print(f"📄 发现现有日志文件，大小: {os.path.getsize(log_file)} 字节")
    
    # 模拟第一次运行
    print("\n🚀 模拟第一次运行...")
    data1 = test_data[0]
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write("\n" + "=" * 100 + "\n")
        f.write(f"========== 安徽联通话费领取结果 - {data1['timestamp']} ==========\n")
        f.write(f"总请求数: {len(data1['results'])}\n")
        f.write(f"账号数: {len(data1['accounts'])}\n")
        f.write("=" * 80 + "\n\n")
        
        for result in data1['results']:
            f.write(result + "\n")
        
        f.write("\n" + "=" * 80 + "\n")
        f.write("本次统计汇总:\n")
        f.write("- 领取成功: 1个\n")
        f.write("- 领取失败: 1个\n")
        f.write(f"本次结果导出完成 - {data1['timestamp']}\n")
        f.write("=" * 100 + "\n\n")
    
    print(f"✅ 第一次运行完成，日志文件大小: {os.path.getsize(log_file)} 字节")
    
    # 模拟第二次运行
    print("\n🚀 模拟第二次运行...")
    data2 = test_data[1]
    with open(log_file, 'a', encoding='utf-8') as f:
        f.write("\n" + "=" * 100 + "\n")
        f.write(f"========== 安徽联通话费领取结果 - {data2['timestamp']} ==========\n")
        f.write(f"总请求数: {len(data2['results'])}\n")
        f.write(f"账号数: {len(data2['accounts'])}\n")
        f.write("=" * 80 + "\n\n")
        
        for result in data2['results']:
            f.write(result + "\n")
        
        f.write("\n" + "=" * 80 + "\n")
        f.write("本次统计汇总:\n")
        f.write("- 领取成功: 2个\n")
        f.write("- 领取失败: 0个\n")
        f.write(f"本次结果导出完成 - {data2['timestamp']}\n")
        f.write("=" * 100 + "\n\n")
    
    print(f"✅ 第二次运行完成，日志文件大小: {os.path.getsize(log_file)} 字节")
    
    # 读取并显示日志内容
    print("\n📖 日志文件内容预览:")
    print("-" * 50)
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.split('\n')
        
        # 显示前20行和后20行
        if len(lines) > 40:
            print("前20行:")
            for i, line in enumerate(lines[:20]):
                print(f"{i+1:2d}: {line}")
            print("...")
            print("后20行:")
            for i, line in enumerate(lines[-20:]):
                print(f"{len(lines)-20+i+1:2d}: {line}")
        else:
            for i, line in enumerate(lines):
                print(f"{i+1:2d}: {line}")
    
    print(f"\n📊 统计信息:")
    print(f"- 日志文件大小: {os.path.getsize(log_file)} 字节")
    print(f"- 总行数: {len(lines)}")
    print(f"- 包含'领取成功'的行数: {content.count('✅领取成功')}")
    print(f"- 包含'领取失败'的行数: {content.count('❌领取失败')}")
    print(f"- 运行次数: {content.count('安徽联通话费领取结果')}")

def test_clear_log():
    """测试清空日志功能"""
    print("\n\n🗑️ 测试清空日志功能...")
    print("=" * 50)
    
    log_file = "安徽话费.txt"
    
    if os.path.exists(log_file):
        original_size = os.path.getsize(log_file)
        print(f"清空前文件大小: {original_size} 字节")
        
        # 模拟清空操作
        with open(log_file, 'w', encoding='utf-8') as f:
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"========== 安徽联通话费领取日志 - 已清空 {timestamp} ==========\n")
            f.write("日志文件已清空，新的记录将从此开始累加。\n")
            f.write("=" * 80 + "\n\n")
        
        new_size = os.path.getsize(log_file)
        print(f"清空后文件大小: {new_size} 字节")
        print("✅ 日志清空功能正常")
    else:
        print("❌ 日志文件不存在")

if __name__ == "__main__":
    print("🚀 安徽联通话费领取工具 - 日志累加功能测试")
    print("=" * 60)
    
    # 测试日志累加
    test_log_append()
    
    # 测试清空日志
    test_clear_log()
    
    print("\n✨ 测试完成！")
    print("💡 功能说明:")
    print("- 日志文件采用累加模式，每次运行都会在文件末尾追加新记录")
    print("- 每次运行都有明确的时间戳和分隔线")
    print("- 可以通过'清空日志'按钮清空历史记录")
    print("- 日志文件会自动创建，无需手动创建")
