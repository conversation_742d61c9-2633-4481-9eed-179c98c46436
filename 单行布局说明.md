# 短信发送工具 - 单行布局版本

## 界面改进

### 🎯 新的单行表格布局
现在每个请求表单都排列在一行中，大大节省了界面空间，可以在一个屏幕内查看更多请求。

### 📊 表格列说明

| 列名 | 说明 | 宽度 | 示例 |
|------|------|------|------|
| **名称** | 请求的自定义名称 | 窄 | `大众工商网` |
| **网址** | 完整的请求URL | 宽 | `https://example.com/api?phone={phone}` |
| **方式** | GET或POST | 窄 | `POST` |
| **数据** | POST请求的数据内容 | 中 | `phone={phone}&code=123` |
| **协议头** | HTTP请求头（用\|分隔） | 中 | `Content-Type: application/json \| Accept: */*` |
| **发送** | 发送此请求的按钮 | 窄 | `[发送]` |
| **删除** | 删除此表单的按钮 | 窄 | `[删除]` |

## 使用方法

### 快速编辑
1. **直接编辑**: 点击任意单元格直接编辑内容
2. **占位符**: 在网址和数据中使用 `{phone}` 代表手机号
3. **协议头**: 多个协议头用 ` | ` 分隔，如：`Content-Type: application/json | Accept: */*`

### 表单操作
- **发送**: 点击行末的"发送"按钮发送单个请求
- **删除**: 点击"删除"按钮移除该行
- **添加**: 使用上方的"添加空白表单"按钮
- **批量发送**: 使用"一键发送所有"发送所有有效表单

### 协议头格式
由于空间限制，协议头需要用 ` | ` 分隔多行：
```
原格式：
Content-Type: application/json
Accept: application/json, text/plain, */*
User-Agent: Mozilla/5.0

单行格式：
Content-Type: application/json | Accept: application/json, text/plain, */* | User-Agent: Mozilla/5.0
```

## 优势

### ✅ 空间效率
- 每个请求只占一行，可以显示更多内容
- 表格式布局，信息对齐清晰
- 减少滚动，提高操作效率

### ✅ 快速编辑
- 所有参数都在一行内，编辑更直观
- 列标题清楚标识每个字段的作用
- 操作按钮就在行末，方便点击

### ✅ 批量管理
- 可以快速浏览所有请求的配置
- 便于比较不同请求的参数
- 支持快速复制和修改类似请求

## 注意事项

1. **协议头格式**: 记住使用 ` | ` 分隔多个协议头
2. **字段宽度**: 网址字段较宽，其他字段相对较窄
3. **占位符**: `{phone}` 会自动替换为输入的手机号
4. **保存配置**: 记得保存配置以便下次使用

## 快捷操作

- **Tab键**: 在字段间快速切换
- **Enter键**: 在某些字段中确认输入
- **双击**: 快速选中字段内容
- **Ctrl+A**: 全选字段内容

## 预设表单

程序仍然包含9个预设的短信平台表单，现在以更紧凑的方式显示：

1. 大众工商网
2. 索尼音乐  
3. 人大金仓
4. 水果批发
5. 水利部
6. 云标科技
7. 温柔怪物
8. 7net平台
9. 天启教育

每个预设表单都已经配置好了正确的参数，可以直接使用或根据需要修改。

## 启动方式

```bash
# Windows
启动工具.bat

# Linux/Mac  
bash 启动工具.sh

# 直接运行
python 短信工具可编辑版.py
```

**密码**: 1995

现在您可以在更紧凑的界面中管理更多的短信请求了！
