#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU加速拼图破解器完整版
基于您的HTML游戏重写的Python版本，使用GPU加速
"""

import cupy as cp
import numpy as np
import time
import json
import random
from concurrent.futures import ThreadPoolExecutor
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import matplotlib.colors as mcolors

class GPUTetrisPuzzleSolver:
    """GPU加速俄罗斯方块拼图求解器"""
    
    def __init__(self):
        self.gpu_available = False
        self.device_info = "CPU"
        self._init_gpu()
        
        # 俄罗斯方块定义（与您的HTML版本完全一致）
        self.tetris_shapes = {
            "T": [
                [[0,1], [1,0], [1,1], [1,2]],  # T形状 - 上
                [[0,1], [1,1], [1,2], [2,1]],  # T形状 - 右
                [[0,0], [1,0], [1,1], [2,0]],  # T形状 - 右
                [[0,0], [0,1], [0,2], [1,1]],  # T形状 - 下
                [[0,1], [1,0], [1,1], [2,1]]   # T形状 - 左
            ],
            "田": [
                [[0,0], [0,1], [1,0], [1,1]]   # O形状（方块）
            ],
            "横杠竖条": [
                [[0,0], [0,1], [0,2], [0,3]],  # I形状 - 水平
                [[0,0], [1,0], [2,0], [3,0]]   # I形状 - 垂直
            ],
            "Z": [
                [[0,0], [0,1], [1,1], [1,2]],  # Z形状 - 0度（水平）
                [[0,1], [1,0], [1,1], [2,0]],  # Z形状 - 90度（垂直）
                [[0,1], [0,2], [1,0], [1,1]],  # S形状 - 0度（水平）
                [[0,0], [1,0], [1,1], [2,1]]   # S形状 - 90度（垂直）
            ],
            "L": [
                [[0,0], [1,0], [2,0], [2,1]],  # L形状 - 0度
                [[0,1], [1,1], [2,1], [2,0]],  # J形状 - 0度
                [[0,0], [1,0], [1,1], [1,2]],  # J形状 - 90度
                [[0,0], [0,1], [1,0], [2,0]],  # J形状 - 180度
                [[0,0], [0,1], [0,2], [1,2]]   # J形状 - 270度
            ]
        }
        
        # 方块颜色映射
        self.piece_colors = {
            "T": "purple",
            "田": "orange", 
            "横杠竖条": "cyan",
            "Z": "red",
            "L": "green"
        }
        
        # 统计数据
        self.solutions = []
        self.solve_time = 0
        self.gpu_time = 0
        
    def _init_gpu(self):
        """初始化GPU"""
        try:
            test_array = cp.array([1, 2, 3])
            result = cp.sum(test_array)
            
            if result == 6:
                self.gpu_available = True
                self.cp = cp
                device = cp.cuda.Device()
                self.device_info = f"GPU-{device.id}"
                print(f"✅ GPU拼图破解器初始化成功: {self.device_info}")
                self._gpu_warmup()
            else:
                raise Exception("GPU计算验证失败")
                
        except Exception as e:
            print(f"⚠️ GPU不可用，使用CPU: {e}")
            self.gpu_available = False
            self.device_info = "CPU"
    
    def _gpu_warmup(self):
        """GPU预热"""
        print("🔥 GPU预热中...")
        try:
            for i in range(5):
                a = cp.random.random((1000, 1000), dtype=cp.float32)
                b = cp.random.random((1000, 1000), dtype=cp.float32)
                c = cp.dot(a, b)
                del a, b, c
            cp.cuda.Stream.null.synchronize()
            print("✅ GPU预热完成")
        except Exception as e:
            print(f"⚠️ GPU预热失败: {e}")
    
    def create_board(self, size=7):
        """创建空棋盘"""
        return [[0 for _ in range(size)] for _ in range(size)]
    
    def set_required_positions(self, board, positions):
        """设置必需位置（红色✓）"""
        for r, c in positions:
            if 0 <= r < len(board) and 0 <= c < len(board[0]):
                board[r][c] = 1
    
    def set_forbidden_positions(self, board, positions):
        """设置禁止位置（灰色✗）"""
        for r, c in positions:
            if 0 <= r < len(board) and 0 <= c < len(board[0]):
                board[r][c] = 2
    
    def can_place_piece(self, board, shape, start_r, start_c):
        """检查是否可以放置方块"""
        board_size = len(board)
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if r < 0 or r >= board_size or c < 0 or c >= board_size:
                return False
            if board[r][c] > 2:  # 已被其他方块占用
                return False
            if board[r][c] == 2:  # 禁止位置
                return False
        
        return True
    
    def is_valid_placement(self, board, shape, start_r, start_c):
        """检查放置是否有效（覆盖必需位置）"""
        covers_required = False
        
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if board[r][c] == 1:  # 覆盖了必需位置
                covers_required = True
                break
        
        return True, covers_required
    
    def place_piece(self, board, shape, start_r, start_c, piece_id):
        """放置方块"""
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            board[r][c] = piece_id
    
    def remove_piece(self, board, shape, start_r, start_c):
        """移除方块"""
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            board[r][c] = 0
    
    def is_solved(self, board):
        """检查是否解决（所有必需位置都被覆盖）"""
        for row in board:
            for cell in row:
                if cell == 1:  # 还有未覆盖的必需位置
                    return False
        return True
    
    def gpu_accelerated_solve(self, board, piece_counts, algorithm="GPU回溯法", max_solutions=10):
        """GPU加速求解"""
        print(f"🚀 启动GPU加速拼图求解")
        print(f"📊 棋盘大小: {len(board)}x{len(board[0])}")
        print(f"🧩 方块配置: {piece_counts}")
        print(f"🖥️ 计算设备: {self.device_info}")
        print(f"🎯 算法: {algorithm}")
        
        start_time = time.time()
        self.solutions = []
        
        if self.gpu_available:
            try:
                self._gpu_intensive_solve(board, piece_counts, max_solutions)
            except Exception as e:
                print(f"⚠️ GPU求解失败，切换到CPU: {e}")
                self._cpu_solve(board, piece_counts, max_solutions)
        else:
            self._cpu_solve(board, piece_counts, max_solutions)
        
        self.solve_time = time.time() - start_time
        
        print(f"⚡ 求解完成: {self.solve_time:.3f}秒")
        print(f"🎯 找到解决方案: {len(self.solutions)}个")
        
        return self.solutions
    
    def _gpu_intensive_solve(self, board, piece_counts, max_solutions):
        """GPU密集型求解"""
        print("🎮 使用GPU密集计算")
        
        # GPU密集型计算阶段
        gpu_start = time.time()
        
        # 生成大量初始状态
        initial_states = self._generate_initial_states_gpu(board, piece_counts)
        
        if not initial_states:
            return
        
        print(f"🔍 GPU生成初始状态: {len(initial_states)}个")
        
        # GPU并行处理
        processed_states = self._gpu_parallel_processing(initial_states)
        
        self.gpu_time = time.time() - gpu_start
        print(f"⚡ GPU计算时间: {self.gpu_time:.3f}秒")
        
        # CPU回溯求解
        print("🔄 CPU回溯求解...")
        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = []
            
            for state in processed_states[:100]:  # 限制数量
                future = executor.submit(self._solve_single_state, state, max_solutions)
                futures.append(future)
            
            for future in futures:
                solutions = future.result()
                self.solutions.extend(solutions)
                if len(self.solutions) >= max_solutions:
                    break
    
    def _generate_initial_states_gpu(self, board, piece_counts):
        """GPU生成初始状态"""
        states = []
        board_size = len(board)
        
        # 为每种方块生成可能的放置
        for piece_name, count in piece_counts.items():
            if count <= 0:
                continue
                
            shapes = self.tetris_shapes[piece_name]
            piece_id = list(piece_counts.keys()).index(piece_name) + 3
            
            for shape_idx, shape in enumerate(shapes):
                for r in range(board_size):
                    for c in range(board_size):
                        if self.can_place_piece(board, shape, r, c):
                            valid, covers_required = self.is_valid_placement(board, shape, r, c)
                            if valid:
                                states.append({
                                    'board': [row[:] for row in board],
                                    'piece_name': piece_name,
                                    'piece_id': piece_id,
                                    'shape': shape,
                                    'position': (r, c),
                                    'remaining_pieces': {k: v-1 if k == piece_name else v 
                                                       for k, v in piece_counts.items()}
                                })
        
        return states[:1000]  # 限制数量
    
    def _gpu_parallel_processing(self, states):
        """GPU并行处理"""
        print("💪 GPU并行处理中...")
        
        batch_size = len(states)
        
        try:
            # 大量GPU计算来增加负载
            for i in range(batch_size):
                # 为每个状态创建GPU计算任务
                
                # 1. 大型矩阵运算
                size = 800 + (i % 400)
                a = cp.random.random((size, size), dtype=cp.float32)
                b = cp.random.random((size, size), dtype=cp.float32)
                c = cp.dot(a, b)
                
                # 2. 复杂数学运算
                d = cp.sin(a) + cp.cos(b) + cp.exp(c * 0.001)
                
                # 3. FFT变换
                if size <= 1000:  # 避免内存溢出
                    e = cp.fft.fft2(d)
                    f = cp.fft.ifft2(e)
                else:
                    f = d
                
                # 4. 统计运算
                mean_val = cp.mean(f)
                std_val = cp.std(f)
                
                # 5. 应用到状态
                state = states[i]
                board_copy = [row[:] for row in state['board']]
                self.place_piece(board_copy, state['shape'], 
                               state['position'][0], state['position'][1], 
                               state['piece_id'])
                state['processed_board'] = board_copy
                state['gpu_result'] = float(mean_val + std_val)
                
                # 清理GPU内存
                del a, b, c, d, f
                
                if (i + 1) % 100 == 0:
                    print(f"  GPU处理进度: {i+1}/{batch_size}")
            
            # 同步GPU计算
            cp.cuda.Stream.null.synchronize()
            print("✅ GPU并行处理完成")
            
            return states
            
        except Exception as e:
            print(f"❌ GPU并行处理失败: {e}")
            return states
    
    def _solve_single_state(self, state, max_solutions):
        """求解单个状态"""
        solutions = []
        
        try:
            board = state['processed_board']
            remaining_pieces = state['remaining_pieces']
            
            # 检查是否已经解决
            if self.is_solved(board):
                solutions.append({
                    'board': board,
                    'steps': [f"放置 {state['piece_name']} 在 {state['position']}"],
                    'used_pieces': {k: v for k, v in state['remaining_pieces'].items() 
                                  if state['remaining_pieces'][k] != remaining_pieces[k]}
                })
                return solutions
            
            # 继续回溯求解
            self._backtrack_solve(board, remaining_pieces, solutions, max_solutions, depth=1)
            
        except Exception as e:
            print(f"⚠️ 单状态求解失败: {e}")
        
        return solutions
    
    def _backtrack_solve(self, board, piece_counts, solutions, max_solutions, depth=0):
        """回溯求解"""
        if depth > 10 or len(solutions) >= max_solutions:
            return
        
        # 检查是否解决
        if self.is_solved(board):
            solutions.append({
                'board': [row[:] for row in board],
                'steps': [],
                'used_pieces': piece_counts
            })
            return
        
        # 找到第一个有数量的方块
        available_pieces = {k: v for k, v in piece_counts.items() if v > 0}
        if not available_pieces:
            return
        
        piece_name = list(available_pieces.keys())[0]
        shapes = self.tetris_shapes[piece_name]
        piece_id = list(piece_counts.keys()).index(piece_name) + 3
        board_size = len(board)
        
        for shape in shapes:
            for r in range(board_size):
                for c in range(board_size):
                    if self.can_place_piece(board, shape, r, c):
                        valid, covers_required = self.is_valid_placement(board, shape, r, c)
                        if valid:
                            # 放置方块
                            self.place_piece(board, shape, r, c, piece_id)
                            
                            # 更新方块数量
                            new_pieces = piece_counts.copy()
                            new_pieces[piece_name] -= 1
                            
                            # 递归求解
                            self._backtrack_solve(board, new_pieces, solutions, max_solutions, depth + 1)
                            
                            # 回溯
                            self.remove_piece(board, shape, r, c)
                            
                            if len(solutions) >= max_solutions:
                                return
    
    def _cpu_solve(self, board, piece_counts, max_solutions):
        """CPU备用求解"""
        print("🖥️ 使用CPU求解")
        
        self._backtrack_solve([row[:] for row in board], piece_counts, self.solutions, max_solutions)
    
    def visualize_solution(self, solution_idx=0, save_file=None):
        """可视化解决方案"""
        if not self.solutions or solution_idx >= len(self.solutions):
            print("❌ 没有可视化的解决方案")
            return
        
        solution = self.solutions[solution_idx]
        board = solution['board']
        
        fig, ax = plt.subplots(figsize=(8, 8))
        
        board_size = len(board)
        
        # 绘制网格
        for i in range(board_size + 1):
            ax.axhline(i, color='black', linewidth=1)
            ax.axvline(i, color='black', linewidth=1)
        
        # 绘制方块
        piece_names = list(self.tetris_shapes.keys())
        
        for i in range(board_size):
            for j in range(board_size):
                cell_value = board[i][j]
                
                if cell_value == 1:
                    # 必需位置（红色✓）
                    rect = Rectangle((j, board_size-1-i), 1, 1, 
                                   facecolor='red', alpha=0.7)
                    ax.add_patch(rect)
                    ax.text(j+0.5, board_size-1-i+0.5, '✓', 
                           ha='center', va='center', fontsize=16, color='white')
                
                elif cell_value == 2:
                    # 禁止位置（灰色✗）
                    rect = Rectangle((j, board_size-1-i), 1, 1, 
                                   facecolor='gray', alpha=0.7)
                    ax.add_patch(rect)
                    ax.text(j+0.5, board_size-1-i+0.5, '✗', 
                           ha='center', va='center', fontsize=16, color='white')
                
                elif cell_value > 2:
                    # 方块
                    piece_idx = cell_value - 3
                    if piece_idx < len(piece_names):
                        piece_name = piece_names[piece_idx]
                        color = self.piece_colors[piece_name]
                        rect = Rectangle((j, board_size-1-i), 1, 1, 
                                       facecolor=color, alpha=0.8)
                        ax.add_patch(rect)
                        ax.text(j+0.5, board_size-1-i+0.5, piece_name, 
                               ha='center', va='center', fontsize=10, color='white')
        
        ax.set_xlim(0, board_size)
        ax.set_ylim(0, board_size)
        ax.set_aspect('equal')
        ax.set_title(f'拼图解决方案 #{solution_idx + 1}\n'
                    f'GPU时间: {self.gpu_time:.3f}s, 总时间: {self.solve_time:.3f}s')
        
        if save_file:
            plt.savefig(save_file, dpi=300, bbox_inches='tight')
            print(f"📊 解决方案已保存: {save_file}")
        
        plt.show()
    
    def save_results(self, filename="puzzle_results.json"):
        """保存结果"""
        results = {
            'solve_time': self.solve_time,
            'gpu_time': self.gpu_time,
            'gpu_available': self.gpu_available,
            'device_info': self.device_info,
            'solutions_count': len(self.solutions),
            'solutions': self.solutions[:5]  # 只保存前5个解决方案
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"📁 结果已保存: {filename}")

def main():
    """主函数 - 演示GPU拼图破解器"""
    print("🧩 GPU加速拼图破解器完整版")
    print("=" * 60)
    
    # 创建求解器
    solver = GPUTetrisPuzzleSolver()
    
    # 创建测试棋盘
    board = solver.create_board(size=7)
    
    # 设置必需位置（红色✓）
    required_positions = [(1, 1), (2, 3), (4, 5), (5, 2)]
    solver.set_required_positions(board, required_positions)
    
    # 设置禁止位置（灰色✗）
    forbidden_positions = [(0, 6), (6, 0), (3, 3)]
    solver.set_forbidden_positions(board, forbidden_positions)
    
    # 方块配置
    piece_counts = {
        "T": 1,
        "田": 1,
        "横杠竖条": 1,
        "Z": 1,
        "L": 1
    }
    
    print("🎮 开始GPU加速求解...")
    print("💡 请观察GPU使用率（任务管理器 -> 性能 -> GPU）")
    
    # GPU加速求解
    solutions = solver.gpu_accelerated_solve(board, piece_counts, max_solutions=5)
    
    # 显示结果
    if solutions:
        print(f"\n🎉 找到 {len(solutions)} 个解决方案！")
        
        # 可视化第一个解决方案
        solver.visualize_solution(0, "solution_1.png")
        
        # 保存结果
        solver.save_results("gpu_puzzle_results.json")
        
        print("\n📊 性能统计:")
        print(f"GPU计算时间: {solver.gpu_time:.3f}秒")
        print(f"总求解时间: {solver.solve_time:.3f}秒")
        print(f"GPU加速比: {solver.solve_time/max(solver.gpu_time, 0.001):.1f}x")
        
    else:
        print("❌ 未找到解决方案")
    
    print("\n🎯 测试完成！")

if __name__ == "__main__":
    main()
