#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
短信发送工具 - 可编辑表单版本
从易语言代码转换而来，支持自定义编辑多个请求表单
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
import json
import time
import threading
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class RequestForm:
    """单个请求表单类"""
    def __init__(self, parent_frame, row_start, title="", url="", method="GET", data="", headers="", app_ref=None):
        self.parent_frame = parent_frame
        self.row_start = row_start
        self.app_ref = app_ref
        
        # 创建表单控件
        self.create_widgets(title, url, method, data, headers)
    
    def create_widgets(self, title, url, method, data, headers):
        """创建表单控件 - 单行布局"""
        row = self.row_start

        # 名称 - 列0
        self.title_var = tk.StringVar(value=title)
        self.title_entry = ttk.Entry(self.parent_frame, textvariable=self.title_var, width=12)
        self.title_entry.grid(row=row, column=0, sticky=(tk.W, tk.E), pady=2, padx=(2, 2))

        # 网址 - 列1
        self.url_var = tk.StringVar(value=url)
        self.url_entry = ttk.Entry(self.parent_frame, textvariable=self.url_var, width=35)
        self.url_entry.grid(row=row, column=1, sticky=(tk.W, tk.E), pady=2, padx=(2, 2))

        # 方式 - 列2
        self.method_var = tk.StringVar(value=method)
        self.method_combo = ttk.Combobox(self.parent_frame, textvariable=self.method_var,
                                        values=["GET", "POST"], state="readonly", width=6)
        self.method_combo.grid(row=row, column=2, sticky=(tk.W, tk.E), pady=2, padx=(2, 2))

        # 数据 - 列3
        self.data_var = tk.StringVar(value=data)
        self.data_entry = ttk.Entry(self.parent_frame, textvariable=self.data_var, width=25)
        self.data_entry.grid(row=row, column=3, sticky=(tk.W, tk.E), pady=2, padx=(2, 2))

        # 协议头 - 列4
        self.headers_var = tk.StringVar(value=headers.replace('\n', ' | '))  # 用 | 分隔多行
        self.headers_entry = ttk.Entry(self.parent_frame, textvariable=self.headers_var, width=25)
        self.headers_entry.grid(row=row, column=4, sticky=(tk.W, tk.E), pady=2, padx=(2, 2))

        # 发送按钮 - 列5
        self.send_button = ttk.Button(self.parent_frame, text="发送", command=self.send_request, width=6)
        self.send_button.grid(row=row, column=5, pady=2, padx=(2, 2))

        # 删除按钮 - 列6
        self.delete_button = ttk.Button(self.parent_frame, text="删除", command=self.delete_form, width=6)
        self.delete_button.grid(row=row, column=6, pady=2, padx=(2, 2))
    
    def get_data(self):
        """获取表单数据"""
        # 将协议头中的 | 分隔符转换回换行符
        headers_text = self.headers_var.get().replace(' | ', '\n')
        return {
            'title': self.title_var.get(),
            'url': self.url_var.get(),
            'method': self.method_var.get(),
            'data': self.data_var.get(),
            'headers': headers_text
        }
    
    def send_request(self):
        """发送请求"""
        if self.app_ref:
            self.app_ref.send_single_request(self)
    
    def delete_form(self):
        """删除表单"""
        if self.app_ref:
            self.app_ref.delete_request_form(self)

class SMSToolApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("短信发送工具 - 可编辑表单版")
        self.root.geometry("1000x800")
        self.root.resizable(True, True)
        
        # 请求表单列表
        self.request_forms = []
        
        # 密码验证
        self.authenticated = False
        self.show_login()
    
    def show_login(self):
        """显示登录界面"""
        login_window = tk.Toplevel(self.root)
        login_window.title("身份验证")
        login_window.geometry("300x150")
        login_window.resizable(False, False)
        login_window.grab_set()
        
        # 居中显示
        login_window.transient(self.root)
        login_window.geometry("+%d+%d" % (
            self.root.winfo_rootx() + 350,
            self.root.winfo_rooty() + 300
        ))
        
        tk.Label(login_window, text="请输入密码:", font=("Arial", 12)).pack(pady=20)
        
        password_var = tk.StringVar()
        password_entry = tk.Entry(login_window, textvariable=password_var, show="*", font=("Arial", 12))
        password_entry.pack(pady=10)
        password_entry.focus()
        
        def check_password():
            if password_var.get() == "1995":
                self.authenticated = True
                login_window.destroy()
                self.create_main_interface()
            else:
                messagebox.showerror("错误", "密码错误！")
                password_entry.delete(0, tk.END)
        
        def on_enter(event):
            check_password()
        
        password_entry.bind('<Return>', on_enter)
        
        tk.Button(login_window, text="确定", command=check_password, 
                 font=("Arial", 10)).pack(pady=10)
        
        # 如果关闭登录窗口，退出程序
        def on_closing():
            self.root.quit()
        
        login_window.protocol("WM_DELETE_WINDOW", on_closing)
    
    def create_main_interface(self):
        """创建主界面"""
        if not self.authenticated:
            return
        
        # 创建主框架和滚动条
        self.create_scrollable_frame()
        
        # 手机号输入区域
        self.create_phone_input()
        
        # 控制按钮区域
        self.create_control_buttons()
        
        # 请求表单区域
        self.create_request_forms_area()
        
        # 日志区域
        self.create_log_area()
        
        # 初始化预设请求表单
        self.init_preset_forms()
        
        # 创建菜单
        self.create_menu()
    
    def create_scrollable_frame(self):
        """创建可滚动的主框架"""
        # 主容器
        self.main_container = ttk.Frame(self.root)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(self.main_container)
        self.scrollbar = ttk.Scrollbar(self.main_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # 布局
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # 鼠标滚轮绑定
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        self.canvas.bind_all("<MouseWheel>", _on_mousewheel)
    
    def create_phone_input(self):
        """创建手机号输入区域"""
        phone_frame = ttk.LabelFrame(self.scrollable_frame, text="基本设置", padding="10")
        phone_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(phone_frame, text="手机号:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.phone_var = tk.StringVar()
        ttk.Entry(phone_frame, textvariable=self.phone_var, width=20).grid(
            row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        
        phone_frame.columnconfigure(1, weight=1)

    def create_control_buttons(self):
        """创建控制按钮区域"""
        control_frame = ttk.LabelFrame(self.scrollable_frame, text="操作控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行按钮
        button_frame1 = ttk.Frame(control_frame)
        button_frame1.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame1, text="一键发送所有",
                  command=self.send_all_requests).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame1, text="添加空白表单",
                  command=self.add_blank_form).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame1, text="保存所有配置",
                  command=self.save_all_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame1, text="加载配置",
                  command=self.load_all_config).pack(side=tk.LEFT, padx=(0, 5))

        # 第二行按钮
        button_frame2 = ttk.Frame(control_frame)
        button_frame2.pack(fill=tk.X)

        ttk.Button(button_frame2, text="清空所有表单",
                  command=self.clear_all_forms).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame2, text="重置为预设",
                  command=self.reset_to_preset).pack(side=tk.LEFT, padx=(0, 5))

    def create_request_forms_area(self):
        """创建请求表单区域"""
        self.forms_frame = ttk.LabelFrame(self.scrollable_frame, text="请求表单列表", padding="10")
        self.forms_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建列标题
        headers = ["名称", "网址", "方式", "数据", "协议头", "发送", "删除"]
        for i, header in enumerate(headers):
            ttk.Label(self.forms_frame, text=header, font=("Arial", 9, "bold")).grid(
                row=0, column=i, sticky=tk.W, pady=(0, 5), padx=(2, 2))

        # 配置列权重
        self.forms_frame.columnconfigure(1, weight=2)  # 网址列更宽
        self.forms_frame.columnconfigure(3, weight=1)  # 数据列
        self.forms_frame.columnconfigure(4, weight=1)  # 协议头列

    def create_log_area(self):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(self.scrollable_frame, text="执行日志", padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # 日志控制按钮
        log_button_frame = ttk.Frame(log_frame)
        log_button_frame.pack(fill=tk.X)

        ttk.Button(log_button_frame, text="清空日志",
                  command=self.clear_log).pack(side=tk.LEFT)

    def init_preset_forms(self):
        """初始化预设的请求表单"""
        preset_data = [
            {
                'title': '大众工商网',
                'url': 'https://www.dzzgsw.com/phoneCaptcha?phone={phone}&token=',
                'method': 'POST',
                'data': '',
                'headers': 'Content-Type: application/x-www-form-urlencoded\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            {
                'title': '索尼音乐',
                'url': 'https://music.sonyselect.net/getSmsAuthCode',
                'method': 'POST',
                'data': 'phoneNo={phone}&smsAuthCode=1234',
                'headers': 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8\nX-Requested-With: XMLHttpRequest\nUser-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            {
                'title': '人大金仓',
                'url': 'https://bbs.kingbase.com.cn/web-api/web/system/user/getRegisterSmsCode',
                'method': 'POST',
                'data': '{phone}',
                'headers': 'Content-Type: application/json;charset=UTF-8\nAccept: application/json, text/plain, */*\nOrigin: https://bbs.kingbase.com.cn\nReferer: https://bbs.kingbase.com.cn/register'
            },
            {
                'title': '水果批发',
                'url': 'https://shuiguopp.com/bapi/sms/code',
                'method': 'POST',
                'data': '{"phoneNumber": "{phone}"}',
                'headers': 'Content-Type: application/json\nAccept: application/json, text/plain, */*\nOrigin: https://shuiguopp.com\nReferer: https://shuiguopp.com/login'
            },
            {
                'title': '水利部',
                'url': 'https://www.waterchina.com/api/sys-service/security/sendVerifyCodeByCopmany?phone={phone}&companyCode=SXWEB&templateCodeEnum=USER_LOGIN',
                'method': 'GET',
                'data': '',
                'headers': 'Accept: application/json, text/plain, */*\nclientKey: WEB\nappAccessToken: ZFZW1aMmFYTnBkRzl5ZF.2s7YYih7VJ\nReferer: https://www.waterchina.com/login'
            },
            {
                'title': '云标科技',
                'url': 'https://www.yunbiao.tv/user/sendShortMsg.html',
                'method': 'POST',
                'data': 'userPhone={phone}&date=Mon+Jan+01+2024+15%3A11%3A39+GMT%2B0800',
                'headers': 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8\nAccept: application/json, text/javascript, */*; q=0.01\nX-Requested-With: XMLHttpRequest\nOrigin: https://www.yunbiao.tv'
            },
            {
                'title': '温柔怪物',
                'url': 'https://www.gentlemonster.com/cn/customer/api/get_verification_code?phone_number={phone}',
                'method': 'POST',
                'data': '{"phone_number": "{phone}"}',
                'headers': 'Content-Type: application/json\nAccept: application/json, text/plain, */*\nOrigin: https://www.gentlemonster.com\nReferer: https://www.gentlemonster.com/cn/customer/account_register'
            },
            {
                'title': '7net平台',
                'url': 'https://www.7net.cc/User/SmsRegistSend',
                'method': 'POST',
                'data': 'RegisterCode=3944&phone={phone}',
                'headers': 'Content-Type: application/x-www-form-urlencoded; charset=UTF-8\nAccept: application/json, text/javascript, */*; q=0.01\nX-Requested-With: XMLHttpRequest\nOrigin: https://www.7net.cc'
            },
            {
                'title': '天启教育',
                'url': 'http://bb.tqjy666.com:10005/wind/public/login/registerphone.html',
                'method': 'POST',
                'data': '__token__=e7b9841c8a85bf3b4d91588ce0c011&phone={phone}&inviter=0',
                'headers': 'Content-Type: application/x-www-form-urlencoded\nAccept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8\nOrigin: http://bb.tqjy666.com:10005'
            }
        ]

        # 创建预设表单
        for data in preset_data:
            self.add_request_form(**data)

        # 添加5个空白表单
        for i in range(5):
            self.add_blank_form()

    def add_request_form(self, title="", url="", method="GET", data="", headers=""):
        """添加一个请求表单"""
        row_start = len(self.request_forms) + 1  # 每个表单占1行，第0行是标题

        form = RequestForm(self.forms_frame, row_start, title, url, method, data, headers, self)
        self.request_forms.append(form)

        # 更新滚动区域
        self.root.after(100, self.update_scroll_region)

        return form

    def add_blank_form(self):
        """添加空白表单"""
        self.add_request_form()

    def delete_request_form(self, form):
        """删除指定的请求表单"""
        if form in self.request_forms:
            # 销毁表单控件
            widgets_to_destroy = []
            for widget in self.forms_frame.winfo_children():
                widget_row = widget.grid_info().get('row', -1)
                if widget_row == form.row_start:  # 现在每个表单只占一行
                    widgets_to_destroy.append(widget)

            for widget in widgets_to_destroy:
                widget.destroy()

            # 从列表中移除
            self.request_forms.remove(form)

            # 重新布局所有表单
            self.refresh_forms_layout()

    def refresh_forms_layout(self):
        """刷新表单布局"""
        # 清空框架
        for widget in self.forms_frame.winfo_children():
            widget.destroy()

        # 重新创建所有表单
        temp_forms_data = []
        for form in self.request_forms:
            temp_forms_data.append(form.get_data())

        self.request_forms.clear()

        for form_data in temp_forms_data:
            self.add_request_form(**form_data)

    def update_scroll_region(self):
        """更新滚动区域"""
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def send_single_request(self, form):
        """发送单个请求"""
        def run():
            try:
                data = form.get_data()
                phone = self.phone_var.get().strip()

                if not phone:
                    self.log_message(f"[{data['title']}] 错误: 请输入手机号！")
                    return

                # 替换手机号占位符
                url = data['url'].replace('{phone}', phone)
                request_data = data['data'].replace('{phone}', phone) if data['data'] else None

                # 解析协议头
                headers = {}
                if data['headers']:
                    for line in data['headers'].split('\n'):
                        if ':' in line:
                            key, value = line.split(':', 1)
                            headers[key.strip()] = value.strip()

                self.make_request(url, data['method'], request_data, headers, data['title'])

            except Exception as e:
                self.log_message(f"[{data.get('title', '未知')}] 请求失败: {str(e)}")

        threading.Thread(target=run, daemon=True).start()

    def send_all_requests(self):
        """一键发送所有请求"""
        def run_all():
            phone = self.phone_var.get().strip()
            if not phone:
                self.log_message("错误: 请输入手机号！")
                messagebox.showerror("错误", "请输入手机号！")
                return

            self.log_message("开始一键发送所有请求...")

            sent_count = 0
            for i, form in enumerate(self.request_forms):
                data = form.get_data()
                if data['title'] and data['url']:  # 只发送有标题和网址的表单
                    try:
                        self.log_message(f"正在发送第 {sent_count+1} 个请求: {data['title']}")
                        self.send_single_request(form)
                        sent_count += 1
                        time.sleep(2)  # 请求间隔2秒
                    except Exception as e:
                        self.log_message(f"发送 {data['title']} 时出错: {str(e)}")

            self.log_message(f"一键发送完成！共发送 {sent_count} 个请求")

        threading.Thread(target=run_all, daemon=True).start()

    def save_all_config(self):
        """保存所有配置"""
        try:
            config = {
                'phone': self.phone_var.get(),
                'forms': []
            }

            for form in self.request_forms:
                config['forms'].append(form.get_data())

            with open("sms_tool_config.json", "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            self.log_message("配置保存成功！")
            messagebox.showinfo("成功", "配置保存成功！")

        except Exception as e:
            error_msg = f"配置保存失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def load_all_config(self):
        """加载所有配置"""
        try:
            with open("sms_tool_config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            # 加载手机号
            self.phone_var.set(config.get('phone', ''))

            # 清空现有表单
            self.clear_all_forms()

            # 加载表单数据
            forms_data = config.get('forms', [])
            for form_data in forms_data:
                self.add_request_form(**form_data)

            # 如果没有表单，添加空白表单
            if not forms_data:
                self.add_blank_form()

            self.log_message("配置加载成功！")
            messagebox.showinfo("成功", "配置加载成功！")

        except FileNotFoundError:
            self.log_message("配置文件不存在，将创建新配置")
            messagebox.showwarning("提示", "配置文件不存在")
        except Exception as e:
            error_msg = f"配置加载失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def clear_all_forms(self):
        """清空所有表单"""
        # 销毁所有表单控件
        for widget in self.forms_frame.winfo_children():
            widget.destroy()

        # 清空表单列表
        self.request_forms.clear()

        self.log_message("已清空所有表单")

    def reset_to_preset(self):
        """重置为预设表单"""
        if messagebox.askyesno("确认", "确定要重置为预设表单吗？这将清除所有自定义内容。"):
            self.clear_all_forms()
            self.init_preset_forms()
            self.log_message("已重置为预设表单")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} - {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def make_request(self, url, method="GET", data=None, headers=None, description=""):
        """发送HTTP请求"""
        try:
            self.log_message(f"[{description}] 开始请求")
            self.log_message(f"[{description}] URL: {url}")
            self.log_message(f"[{description}] 方法: {method}")

            # 准备请求参数
            request_kwargs = {
                'url': url,
                'timeout': 30,
                'verify': False  # 忽略SSL证书验证
            }

            if headers:
                request_kwargs['headers'] = headers

            if method.upper() == "POST" and data:
                request_kwargs['data'] = data

            # 发送请求
            if method.upper() == "GET":
                response = requests.get(**request_kwargs)
            else:
                response = requests.post(**request_kwargs)

            # 记录响应
            self.log_message(f"[{description}] 响应状态码: {response.status_code}")
            response_text = response.text[:300] + "..." if len(response.text) > 300 else response.text
            self.log_message(f"[{description}] 响应内容: {response_text}")

            return response.text

        except Exception as e:
            error_msg = f"[{description}] 请求失败: {str(e)}"
            self.log_message(error_msg)
            return error_msg

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="保存配置", command=self.save_all_config)
        file_menu.add_command(label="加载配置", command=self.load_all_config)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 编辑菜单
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="编辑", menu=edit_menu)
        edit_menu.add_command(label="添加空白表单", command=self.add_blank_form)
        edit_menu.add_command(label="清空所有表单", command=self.clear_all_forms)
        edit_menu.add_command(label="重置为预设", command=self.reset_to_preset)

        # 请求菜单
        request_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="请求", menu=request_menu)
        request_menu.add_command(label="一键发送所有", command=self.send_all_requests)
        request_menu.add_separator()
        request_menu.add_command(label="清空日志", command=self.clear_log)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)

    def show_help(self):
        """显示使用说明"""
        help_text = """短信发送工具使用说明

基本操作：
1. 输入手机号
2. 编辑请求表单（名称、网址、方式、数据、协议头）
3. 点击"发送此请求"发送单个请求
4. 点击"一键发送所有"发送所有有效请求

表单管理：
- 添加空白表单：创建新的请求表单
- 删除：删除不需要的表单
- 保存配置：保存所有表单到文件
- 加载配置：从文件加载表单

占位符：
- 在网址和数据中使用 {phone} 会自动替换为手机号

注意事项：
- 请合理使用，遵守法律法规
- 建议设置适当的请求间隔
- 部分网站可能需要验证码

预设表单：
程序包含9个预设的短信平台表单，可以直接使用或修改"""

        messagebox.showinfo("使用说明", help_text)

    def show_about(self):
        """显示关于信息"""
        about_text = """短信发送工具 v2.0 - 可编辑表单版

从易语言代码转换而来的Python GUI工具
支持自定义编辑多个平台的短信发送请求

特点：
- 可视化表单编辑界面
- 支持GET/POST请求方式
- 自动手机号占位符替换
- 配置保存和加载功能
- 实时日志显示
- 可滚动界面支持大量表单

技术栈：
- Python 3.x
- tkinter GUI框架
- requests HTTP库

请合理使用，遵守相关法律法规
密码：1995"""

        messagebox.showinfo("关于", about_text)

    def run(self):
        """运行应用"""
        if self.authenticated:
            # 尝试加载配置
            try:
                self.load_all_config()
            except:
                pass  # 如果加载失败，使用默认设置

        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SMSToolApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
