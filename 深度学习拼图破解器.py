#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度学习拼图破解器 - GPU训练版
使用深度强化学习训练AI破解俄罗斯方块拼图
目标：6x6棋盘，T、田、Z、L四种方块，成功率90%+
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import random
import json
import time
from datetime import datetime
import matplotlib.pyplot as plt
from collections import deque, namedtuple
import os

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)
random.seed(42)

class PuzzleEnvironment:
    """拼图环境类"""
    
    def __init__(self, board_size=6):
        self.board_size = board_size
        self.pieces = {
            "T": [
                [(0,1), (1,0), (1,1), (1,2)],  # T形状
                [(0,1), (1,1), (1,2), (2,1)],
                [(0,0), (1,0), (1,1), (2,0)],
                [(0,0), (0,1), (0,2), (1,1)]
            ],
            "田": [[(0,0), (0,1), (1,0), (1,1)]],  # 方块
            "Z": [
                [(0,0), (0,1), (1,1), (1,2)],  # Z形状
                [(0,1), (1,0), (1,1), (2,0)]
            ],
            "L": [
                [(0,0), (1,0), (2,0), (2,1)],  # L形状
                [(0,1), (1,1), (2,1), (2,0)],
                [(0,0), (1,0), (1,1), (1,2)],
                [(0,0), (0,1), (1,0), (2,0)]
            ]
        }
        self.piece_names = ["T", "田", "Z", "L"]
        self.reset()
    
    def reset(self):
        """重置环境"""
        self.board = np.zeros((self.board_size, self.board_size), dtype=np.int32)
        self.remaining_pieces = {"T": 1, "田": 1, "Z": 1, "L": 1}
        
        # 随机生成必需位置（1-3个）
        num_required = random.randint(1, 3)
        for _ in range(num_required):
            r, c = random.randint(0, self.board_size-1), random.randint(0, self.board_size-1)
            self.board[r, c] = 1
        
        # 随机生成禁止位置（0-2个）
        num_forbidden = random.randint(0, 2)
        for _ in range(num_forbidden):
            r, c = random.randint(0, self.board_size-1), random.randint(0, self.board_size-1)
            if self.board[r, c] == 0:
                self.board[r, c] = 2
        
        self.initial_board = self.board.copy()
        return self.get_state()
    
    def get_state(self):
        """获取当前状态"""
        # 状态包括：棋盘 + 剩余方块信息
        state = np.zeros((self.board_size, self.board_size, 5), dtype=np.float32)
        
        # 棋盘状态（4个通道：空、必需、禁止、已放置）
        state[:, :, 0] = (self.board == 0).astype(np.float32)  # 空位置
        state[:, :, 1] = (self.board == 1).astype(np.float32)  # 必需位置
        state[:, :, 2] = (self.board == 2).astype(np.float32)  # 禁止位置
        state[:, :, 3] = (self.board > 2).astype(np.float32)   # 已放置方块
        
        # 剩余方块信息（1个通道）
        piece_info = 0
        for i, piece in enumerate(self.piece_names):
            if self.remaining_pieces[piece] > 0:
                piece_info += (i + 1) * 0.1
        state[:, :, 4] = piece_info
        
        return state
    
    def get_valid_actions(self):
        """获取有效动作"""
        valid_actions = []
        action_id = 0
        
        for piece_name in self.piece_names:
            if self.remaining_pieces[piece_name] <= 0:
                action_id += len(self.pieces[piece_name]) * self.board_size * self.board_size
                continue
            
            piece_id = self.piece_names.index(piece_name) + 3
            
            for shape_idx, shape in enumerate(self.pieces[piece_name]):
                for r in range(self.board_size):
                    for c in range(self.board_size):
                        if self.can_place(shape, r, c):
                            valid_actions.append(action_id)
                        action_id += 1
        
        return valid_actions
    
    def can_place(self, shape, start_r, start_c):
        """检查是否可以放置方块"""
        for dr, dc in shape:
            r, c = start_r + dr, start_c + dc
            if (r < 0 or r >= self.board_size or c < 0 or c >= self.board_size or
                self.board[r, c] > 2 or self.board[r, c] == 2):
                return False
        return True
    
    def step(self, action):
        """执行动作"""
        # 解码动作
        piece_idx, shape_idx, r, c = self.decode_action(action)
        
        if piece_idx >= len(self.piece_names):
            return self.get_state(), -10, True, {}  # 无效动作
        
        piece_name = self.piece_names[piece_idx]
        
        if self.remaining_pieces[piece_name] <= 0:
            return self.get_state(), -10, True, {}  # 方块已用完
        
        shape = self.pieces[piece_name][shape_idx]
        
        if not self.can_place(shape, r, c):
            return self.get_state(), -5, False, {}  # 无效放置
        
        # 放置方块
        piece_id = piece_idx + 3
        covers_required = False
        
        for dr, dc in shape:
            nr, nc = r + dr, c + dc
            if self.board[nr, nc] == 1:
                covers_required = True
            self.board[nr, nc] = piece_id
        
        self.remaining_pieces[piece_name] -= 1
        
        # 计算奖励
        reward = self.calculate_reward(covers_required)
        
        # 检查是否完成
        done = self.is_solved() or sum(self.remaining_pieces.values()) == 0
        
        return self.get_state(), reward, done, {"solved": self.is_solved()}
    
    def decode_action(self, action):
        """解码动作"""
        total_actions_per_piece = len(self.pieces["T"]) * self.board_size * self.board_size
        
        piece_idx = action // total_actions_per_piece
        remaining = action % total_actions_per_piece
        
        if piece_idx < len(self.piece_names):
            piece_name = self.piece_names[piece_idx]
            shapes_count = len(self.pieces[piece_name])
            actions_per_shape = self.board_size * self.board_size
            
            shape_idx = remaining // actions_per_shape
            pos_idx = remaining % actions_per_shape
            
            r = pos_idx // self.board_size
            c = pos_idx % self.board_size
            
            if shape_idx < shapes_count:
                return piece_idx, shape_idx, r, c
        
        return len(self.piece_names), 0, 0, 0  # 无效动作
    
    def calculate_reward(self, covers_required):
        """计算奖励"""
        reward = 0
        
        if covers_required:
            reward += 10  # 覆盖必需位置
        
        if self.is_solved():
            reward += 100  # 完成拼图
        
        # 根据剩余方块数量给予奖励
        used_pieces = 4 - sum(self.remaining_pieces.values())
        reward += used_pieces * 2
        
        return reward
    
    def is_solved(self):
        """检查是否解决"""
        return not np.any(self.board == 1)  # 没有未覆盖的必需位置

class DQN(nn.Module):
    """深度Q网络"""
    
    def __init__(self, board_size=6, action_size=576):  # 4*4*6*6 = 576
        super(DQN, self).__init__()
        self.board_size = board_size
        
        # 卷积层
        self.conv1 = nn.Conv2d(5, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        
        # 全连接层
        self.fc1 = nn.Linear(128 * board_size * board_size, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, action_size)
        
        self.dropout = nn.Dropout(0.3)
    
    def forward(self, x):
        # x shape: (batch, board_size, board_size, 5)
        x = x.permute(0, 3, 1, 2)  # (batch, 5, board_size, board_size)
        
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        
        x = x.reshape(x.size(0), -1)
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        
        return x

class DQNAgent:
    """DQN智能体"""
    
    def __init__(self, board_size=6, action_size=576, lr=0.001):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        print(f"🖥️ 使用设备: {self.device}")
        
        self.board_size = board_size
        self.action_size = action_size
        self.memory = deque(maxlen=10000)
        self.epsilon = 1.0
        self.epsilon_min = 0.01
        self.epsilon_decay = 0.995
        self.learning_rate = lr
        self.batch_size = 32
        self.target_update = 100
        
        # 神经网络
        self.q_network = DQN(board_size, action_size).to(self.device)
        self.target_network = DQN(board_size, action_size).to(self.device)
        self.optimizer = optim.Adam(self.q_network.parameters(), lr=lr)
        
        self.update_target_network()
        
        # 训练统计
        self.training_stats = {
            'episodes': [],
            'rewards': [],
            'success_rate': [],
            'epsilon': [],
            'loss': []
        }
    
    def update_target_network(self):
        """更新目标网络"""
        self.target_network.load_state_dict(self.q_network.state_dict())
    
    def remember(self, state, action, reward, next_state, done):
        """存储经验"""
        self.memory.append((state, action, reward, next_state, done))
    
    def act(self, state, valid_actions):
        """选择动作"""
        if np.random.random() <= self.epsilon:
            return random.choice(valid_actions) if valid_actions else 0
        
        state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
        q_values = self.q_network(state_tensor)
        
        # 只考虑有效动作
        if valid_actions:
            valid_q_values = q_values[0][valid_actions]
            best_valid_idx = torch.argmax(valid_q_values).item()
            return valid_actions[best_valid_idx]
        
        return torch.argmax(q_values[0]).item()
    
    def replay(self):
        """经验回放"""
        if len(self.memory) < self.batch_size:
            return 0
        
        batch = random.sample(self.memory, self.batch_size)

        # 更高效的张量创建方式
        states = torch.FloatTensor(np.array([e[0] for e in batch])).to(self.device)
        actions = torch.LongTensor(np.array([e[1] for e in batch])).to(self.device)
        rewards = torch.FloatTensor(np.array([e[2] for e in batch])).to(self.device)
        next_states = torch.FloatTensor(np.array([e[3] for e in batch])).to(self.device)
        dones = torch.BoolTensor(np.array([e[4] for e in batch])).to(self.device)
        
        current_q_values = self.q_network(states).gather(1, actions.unsqueeze(1))
        next_q_values = self.target_network(next_states).max(1)[0].detach()
        target_q_values = rewards + (0.99 * next_q_values * ~dones)
        
        loss = F.mse_loss(current_q_values.squeeze(), target_q_values)
        
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        if self.epsilon > self.epsilon_min:
            self.epsilon *= self.epsilon_decay
        
        return loss.item()
    
    def save_model(self, filepath):
        """保存模型"""
        torch.save({
            'q_network_state_dict': self.q_network.state_dict(),
            'target_network_state_dict': self.target_network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'epsilon': self.epsilon,
            'training_stats': self.training_stats
        }, filepath)
        print(f"💾 模型已保存: {filepath}")
    
    def load_model(self, filepath):
        """加载模型"""
        if os.path.exists(filepath):
            checkpoint = torch.load(filepath, map_location=self.device)
            self.q_network.load_state_dict(checkpoint['q_network_state_dict'])
            self.target_network.load_state_dict(checkpoint['target_network_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.epsilon = checkpoint['epsilon']
            self.training_stats = checkpoint['training_stats']
            print(f"📁 模型已加载: {filepath}")
            return True
        return False

class PuzzleTrainer:
    """拼图训练器"""
    
    def __init__(self):
        self.env = PuzzleEnvironment(board_size=6)
        self.agent = DQNAgent(board_size=6, action_size=576)
        self.model_path = "puzzle_dqn_model.pth"
        self.web_model_path = "puzzle_model_for_web.json"
        
    def train(self, episodes=5000, target_success_rate=0.9):
        """训练模型"""
        print("🚀 开始深度学习训练")
        print(f"🎯 目标: {episodes}轮训练，成功率达到{target_success_rate*100}%")
        print(f"🖥️ 设备: {self.agent.device}")
        
        scores = deque(maxlen=100)
        success_count = 0
        best_success_rate = 0
        
        for episode in range(episodes):
            state = self.env.reset()
            total_reward = 0
            steps = 0
            max_steps = 50
            
            while steps < max_steps:
                valid_actions = self.env.get_valid_actions()
                if not valid_actions:
                    break
                
                action = self.agent.act(state, valid_actions)
                next_state, reward, done, info = self.env.step(action)
                
                self.agent.remember(state, action, reward, next_state, done)
                state = next_state
                total_reward += reward
                steps += 1
                
                if done:
                    if info.get("solved", False):
                        success_count += 1
                    break
            
            scores.append(total_reward)
            
            # 经验回放
            loss = self.agent.replay()
            
            # 更新目标网络
            if episode % self.agent.target_update == 0:
                self.agent.update_target_network()
            
            # 统计和显示
            if episode % 100 == 0:
                avg_score = np.mean(scores)
                success_rate = success_count / 100 if episode >= 100 else success_count / (episode + 1)
                
                print(f"轮次 {episode:4d} | 平均分数: {avg_score:6.2f} | "
                      f"成功率: {success_rate:.3f} | ε: {self.agent.epsilon:.3f} | "
                      f"损失: {loss:.4f}")
                
                # 记录统计数据
                self.agent.training_stats['episodes'].append(episode)
                self.agent.training_stats['rewards'].append(avg_score)
                self.agent.training_stats['success_rate'].append(success_rate)
                self.agent.training_stats['epsilon'].append(self.agent.epsilon)
                self.agent.training_stats['loss'].append(loss)
                
                # 保存最佳模型
                if success_rate > best_success_rate:
                    best_success_rate = success_rate
                    self.agent.save_model(self.model_path)
                
                # 检查是否达到目标
                if success_rate >= target_success_rate:
                    print(f"🎉 达到目标成功率 {success_rate:.3f}!")
                    break
                
                success_count = 0
        
        print("✅ 训练完成!")
        self.export_for_web()
        self.plot_training_stats()
        return best_success_rate
    
    def test(self, num_tests=100):
        """测试模型"""
        print(f"🧪 测试模型性能 ({num_tests}次)")
        
        self.agent.epsilon = 0  # 关闭探索
        success_count = 0
        total_steps = 0
        
        for test in range(num_tests):
            state = self.env.reset()
            steps = 0
            max_steps = 50
            
            while steps < max_steps:
                valid_actions = self.env.get_valid_actions()
                if not valid_actions:
                    break
                
                action = self.agent.act(state, valid_actions)
                state, reward, done, info = self.env.step(action)
                steps += 1
                
                if done:
                    if info.get("solved", False):
                        success_count += 1
                    break
            
            total_steps += steps
        
        success_rate = success_count / num_tests
        avg_steps = total_steps / num_tests
        
        print(f"📊 测试结果:")
        print(f"   成功率: {success_rate:.3f} ({success_count}/{num_tests})")
        print(f"   平均步数: {avg_steps:.1f}")
        
        return success_rate
    
    def export_for_web(self):
        """导出模型供网页端使用"""
        print("📤 导出模型供网页端使用...")
        
        # 导出模型权重和结构信息
        model_data = {
            "model_type": "DQN_Puzzle_Solver",
            "board_size": 6,
            "action_size": 576,
            "piece_names": ["T", "田", "Z", "L"],
            "success_rate": self.agent.training_stats['success_rate'][-1] if self.agent.training_stats['success_rate'] else 0,
            "training_episodes": len(self.agent.training_stats['episodes']),
            "export_time": datetime.now().isoformat(),
            "device_trained": str(self.agent.device),
            "usage_instructions": {
                "description": "深度学习训练的拼图求解器",
                "input_format": "6x6棋盘状态矩阵",
                "output_format": "最佳动作ID",
                "pieces": "T、田、Z、L各一个"
            }
        }
        
        # 保存简化的权重信息（用于网页端参考）
        try:
            # 获取网络的关键参数
            state_dict = self.agent.q_network.state_dict()
            
            # 只保存关键层的权重统计信息（不保存完整权重，太大了）
            model_data["network_info"] = {
                "conv1_weight_shape": list(state_dict['conv1.weight'].shape),
                "conv2_weight_shape": list(state_dict['conv2.weight'].shape),
                "conv3_weight_shape": list(state_dict['conv3.weight'].shape),
                "fc1_weight_shape": list(state_dict['fc1.weight'].shape),
                "fc2_weight_shape": list(state_dict['fc2.weight'].shape),
                "fc3_weight_shape": list(state_dict['fc3.weight'].shape),
            }
            
            # 保存训练统计
            model_data["training_stats"] = self.agent.training_stats
            
        except Exception as e:
            print(f"⚠️ 导出权重信息失败: {e}")
        
        # 保存到JSON文件
        with open(self.web_model_path, 'w', encoding='utf-8') as f:
            json.dump(model_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 网页端模型数据已导出: {self.web_model_path}")
        
        # 创建网页端集成说明
        integration_guide = f"""
# 网页端集成说明

## 模型信息
- 训练设备: {self.agent.device}
- 成功率: {model_data.get('success_rate', 0):.3f}
- 训练轮次: {model_data.get('training_episodes', 0)}

## 集成到 cainiao699.html

### 1. 加载模型数据
```javascript
// 在网页中加载模型数据
fetch('puzzle_model_for_web.json')
    .then(response => response.json())
    .then(modelData => {{
        console.log('AI模型加载成功:', modelData);
        window.puzzleAI = modelData;
    }});
```

### 2. AI求解函数
```javascript
function aiSolvePuzzle(board, pieces) {{
    // 使用训练好的AI模型进行求解
    // 这里需要实现简化的推理逻辑
    
    if (!window.puzzleAI) {{
        console.log('AI模型未加载');
        return null;
    }}
    
    // 基于训练统计数据的启发式求解
    const stats = window.puzzleAI.training_stats;
    const successRate = stats.success_rate[stats.success_rate.length - 1];
    
    console.log(`AI模型成功率: ${{successRate}}`);
    
    // 实现简化的AI逻辑...
    return aiSolveWithHeuristics(board, pieces);
}}
```

### 3. 添加AI按钮
在网页中添加AI求解按钮:
```html
<button onclick="aiSolvePuzzle(currentBoard, currentPieces)">
    🤖 AI求解 (成功率: {model_data.get('success_rate', 0):.1%})
</button>
```

## 使用方法
1. 将 {self.web_model_path} 放在网页同目录
2. 在 cainiao699.html 中添加上述代码
3. 用户点击AI按钮即可使用训练好的AI
"""
        
        with open("网页端集成说明.md", 'w', encoding='utf-8') as f:
            f.write(integration_guide)
        
        print("📖 网页端集成说明已生成: 网页端集成说明.md")
    
    def plot_training_stats(self):
        """绘制训练统计图"""
        if not self.agent.training_stats['episodes']:
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 8))
        
        episodes = self.agent.training_stats['episodes']
        
        # 奖励曲线
        ax1.plot(episodes, self.agent.training_stats['rewards'])
        ax1.set_title('平均奖励')
        ax1.set_xlabel('训练轮次')
        ax1.set_ylabel('奖励')
        
        # 成功率曲线
        ax2.plot(episodes, self.agent.training_stats['success_rate'])
        ax2.set_title('成功率')
        ax2.set_xlabel('训练轮次')
        ax2.set_ylabel('成功率')
        ax2.axhline(y=0.9, color='r', linestyle='--', label='目标成功率')
        ax2.legend()
        
        # Epsilon曲线
        ax3.plot(episodes, self.agent.training_stats['epsilon'])
        ax3.set_title('探索率 (Epsilon)')
        ax3.set_xlabel('训练轮次')
        ax3.set_ylabel('Epsilon')
        
        # 损失曲线
        ax4.plot(episodes, self.agent.training_stats['loss'])
        ax4.set_title('训练损失')
        ax4.set_xlabel('训练轮次')
        ax4.set_ylabel('损失')
        
        plt.tight_layout()
        plt.savefig('training_stats.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 训练统计图已保存: training_stats.png")

class WebIntegration:
    """网页端集成工具"""

    @staticmethod
    def generate_js_solver(model_data_path="puzzle_model_for_web.json"):
        """生成JavaScript求解器代码"""
        js_code = f'''
// AI拼图求解器 - 基于深度学习训练
class AIPuzzleSolver {{
    constructor() {{
        this.modelData = null;
        this.isLoaded = false;
        this.loadModel();
    }}

    async loadModel() {{
        try {{
            const response = await fetch('{model_data_path}');
            this.modelData = await response.json();
            this.isLoaded = true;
            console.log('🤖 AI模型加载成功:', this.modelData);

            // 更新UI显示成功率
            const successRate = this.getSuccessRate();
            document.getElementById('ai-success-rate')?.textContent =
                `成功率: ${{(successRate * 100).toFixed(1)}}%`;
        }} catch (error) {{
            console.error('❌ AI模型加载失败:', error);
        }}
    }}

    getSuccessRate() {{
        if (!this.modelData?.training_stats?.success_rate) return 0;
        const rates = this.modelData.training_stats.success_rate;
        return rates[rates.length - 1] || 0;
    }}

    // 主要求解函数
    solvePuzzle(board, pieces) {{
        if (!this.isLoaded) {{
            console.log('⚠️ AI模型未加载完成');
            return null;
        }}

        console.log('🤖 AI开始求解...');
        const startTime = Date.now();

        // 使用训练好的启发式策略
        const solution = this.aiSolveWithStrategy(board, pieces);

        const solveTime = Date.now() - startTime;
        console.log(`🎯 AI求解完成，耗时: ${{solveTime}}ms`);

        return solution;
    }}

    // AI求解策略（基于训练经验）
    aiSolveWithStrategy(board, pieces) {{
        const boardSize = board.length;
        const solution = [];
        const workingBoard = board.map(row => [...row]);
        const remainingPieces = {{...pieces}};

        // 方块定义（与训练时一致）
        const pieceShapes = {{
            "T": [
                [[0,1], [1,0], [1,1], [1,2]],
                [[0,1], [1,1], [1,2], [2,1]],
                [[0,0], [1,0], [1,1], [2,0]],
                [[0,0], [0,1], [0,2], [1,1]]
            ],
            "田": [[[0,0], [0,1], [1,0], [1,1]]],
            "Z": [
                [[0,0], [0,1], [1,1], [1,2]],
                [[0,1], [1,0], [1,1], [2,0]]
            ],
            "L": [
                [[0,0], [1,0], [2,0], [2,1]],
                [[0,1], [1,1], [2,1], [2,0]],
                [[0,0], [1,0], [1,1], [1,2]],
                [[0,0], [0,1], [1,0], [2,0]]
            ]
        }};

        // AI策略：优先覆盖必需位置
        const requiredPositions = this.findRequiredPositions(workingBoard);

        for (const [pieceName, count] of Object.entries(remainingPieces)) {{
            if (count <= 0) continue;

            const shapes = pieceShapes[pieceName];
            const pieceId = Object.keys(pieces).indexOf(pieceName) + 3;

            let bestPlacement = null;
            let maxCoverage = -1;

            // 尝试所有可能的放置
            for (let shapeIdx = 0; shapeIdx < shapes.length; shapeIdx++) {{
                const shape = shapes[shapeIdx];

                for (let r = 0; r < boardSize; r++) {{
                    for (let c = 0; c < boardSize; c++) {{
                        if (this.canPlace(workingBoard, shape, r, c)) {{
                            const coverage = this.calculateCoverage(workingBoard, shape, r, c, requiredPositions);

                            if (coverage > maxCoverage) {{
                                maxCoverage = coverage;
                                bestPlacement = {{
                                    piece: pieceName,
                                    shape: shape,
                                    position: [r, c],
                                    shapeIdx: shapeIdx,
                                    pieceId: pieceId
                                }};
                            }}
                        }}
                    }}
                }}
            }}

            // 应用最佳放置
            if (bestPlacement) {{
                this.placePiece(workingBoard, bestPlacement.shape,
                              bestPlacement.position[0], bestPlacement.position[1],
                              bestPlacement.pieceId);

                solution.push({{
                    step: solution.length + 1,
                    piece: bestPlacement.piece,
                    position: bestPlacement.position,
                    rotation: bestPlacement.shapeIdx
                }});

                remainingPieces[pieceName]--;
                break;
            }}
        }}

        // 检查是否解决
        const isSolved = this.isSolved(workingBoard);

        return {{
            success: isSolved,
            steps: solution,
            finalBoard: workingBoard,
            aiUsed: true,
            successRate: this.getSuccessRate()
        }};
    }}

    findRequiredPositions(board) {{
        const required = [];
        for (let r = 0; r < board.length; r++) {{
            for (let c = 0; c < board[0].length; c++) {{
                if (board[r][c] === 1) {{
                    required.push([r, c]);
                }}
            }}
        }}
        return required;
    }}

    canPlace(board, shape, startR, startC) {{
        for (const [dr, dc] of shape) {{
            const r = startR + dr;
            const c = startC + dc;
            if (r < 0 || r >= board.length || c < 0 || c >= board[0].length ||
                board[r][c] > 2 || board[r][c] === 2) {{
                return false;
            }}
        }}
        return true;
    }}

    calculateCoverage(board, shape, startR, startC, requiredPositions) {{
        let coverage = 0;
        for (const [dr, dc] of shape) {{
            const r = startR + dr;
            const c = startC + dc;
            if (board[r][c] === 1) {{ // 覆盖必需位置
                coverage += 10;
            }}
        }}
        return coverage;
    }}

    placePiece(board, shape, startR, startC, pieceId) {{
        for (const [dr, dc] of shape) {{
            const r = startR + dr;
            const c = startC + dc;
            board[r][c] = pieceId;
        }}
    }}

    isSolved(board) {{
        for (const row of board) {{
            for (const cell of row) {{
                if (cell === 1) return false; // 还有未覆盖的必需位置
            }}
        }}
        return true;
    }}
}}

// 全局AI实例
window.aiSolver = new AIPuzzleSolver();

// 添加到现有的求解函数中
function aiSolvePuzzle() {{
    if (!window.aiSolver?.isLoaded) {{
        alert('AI模型未加载完成，请稍后再试');
        return;
    }}

    const currentBoard = game.board; // 假设game是当前游戏实例
    const currentPieces = game.pieceCounts;

    const result = window.aiSolver.solvePuzzle(currentBoard, currentPieces);

    if (result?.success) {{
        console.log('🎉 AI求解成功!', result);

        // 显示解决方案
        game.showAISolution(result);

        // 更新统计
        document.getElementById('ai-stats')?.textContent =
            `AI成功率: ${{(result.successRate * 100).toFixed(1)}}% | 步数: ${{result.steps.length}}`;
    }} else {{
        console.log('❌ AI求解失败');
        alert('AI未能找到解决方案，请尝试调整棋盘或方块配置');
    }}
}}
'''

        with open("ai_puzzle_solver.js", 'w', encoding='utf-8') as f:
            f.write(js_code)

        print("📄 JavaScript AI求解器已生成: ai_puzzle_solver.js")
        return js_code

def main():
    """主函数"""
    print("🧩 深度学习拼图破解器")
    print("=" * 60)
    print("🎯 目标: 6x6棋盘，T、田、Z、L四种方块，成功率90%+")
    print("🖥️ 使用GPU加速训练")

    # 检查GPU可用性
    if torch.cuda.is_available():
        print(f"✅ GPU可用: {torch.cuda.get_device_name()}")
        print(f"📊 显存: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")
    else:
        print("⚠️ GPU不可用，将使用CPU训练（速度较慢）")

    trainer = PuzzleTrainer()

    print("\n📋 选择操作:")
    print("1. 🚀 开始训练 (目标90%成功率)")
    print("2. 🧪 测试现有模型")
    print("3. 📤 导出网页端文件")
    print("4. 📊 查看训练统计")
    print("5. 🎮 交互式测试")

    try:
        choice = input("\n请选择 (1-5): ").strip()

        if choice == "1":
            # 训练模式
            if trainer.agent.load_model(trainer.model_path):
                print("📁 发现已训练的模型")
                cont = input("是否继续训练? (y/n): ").lower().strip()
                if cont != 'y':
                    return

            episodes = int(input("训练轮次 (默认5000): ") or "5000")
            target_rate = float(input("目标成功率 (默认0.9): ") or "0.9")

            print(f"\n🚀 开始训练 {episodes} 轮，目标成功率 {target_rate}")
            final_success_rate = trainer.train(episodes=episodes, target_success_rate=target_rate)

            # 最终测试
            print("\n🧪 最终测试...")
            test_success_rate = trainer.test(num_tests=200)

            print(f"\n🎯 训练结果:")
            print(f"   最佳训练成功率: {final_success_rate:.3f}")
            print(f"   最终测试成功率: {test_success_rate:.3f}")

            if test_success_rate >= target_rate:
                print("🎉 成功达到目标成功率!")
                trainer.export_for_web()
                WebIntegration.generate_js_solver()
            else:
                print("⚠️ 未达到目标，建议继续训练")

        elif choice == "2":
            # 测试模式
            if not trainer.agent.load_model(trainer.model_path):
                print("❌ 未找到训练好的模型，请先训练")
                return

            num_tests = int(input("测试次数 (默认100): ") or "100")
            success_rate = trainer.test(num_tests=num_tests)

            if success_rate >= 0.9:
                print("🎉 模型已达到目标成功率!")

        elif choice == "3":
            # 导出模式
            if not trainer.agent.load_model(trainer.model_path):
                print("❌ 未找到训练好的模型，请先训练")
                return

            trainer.export_for_web()
            WebIntegration.generate_js_solver()
            print("✅ 网页端文件导出完成!")

        elif choice == "4":
            # 查看统计
            if trainer.agent.load_model(trainer.model_path):
                trainer.plot_training_stats()
            else:
                print("❌ 未找到训练数据")

        elif choice == "5":
            # 交互式测试
            if not trainer.agent.load_model(trainer.model_path):
                print("❌ 未找到训练好的模型，请先训练")
                return

            print("🎮 交互式测试模式")
            while True:
                print("\n生成随机测试棋盘...")
                state = trainer.env.reset()
                print("初始棋盘:")
                print(trainer.env.board)
                print(f"剩余方块: {trainer.env.remaining_pieces}")

                # AI求解
                trainer.agent.epsilon = 0  # 关闭探索
                steps = 0
                max_steps = 50

                while steps < max_steps:
                    valid_actions = trainer.env.get_valid_actions()
                    if not valid_actions:
                        break

                    action = trainer.agent.act(state, valid_actions)
                    state, reward, done, info = trainer.env.step(action)
                    steps += 1

                    if done:
                        print(f"\n结果: {'✅ 成功' if info.get('solved', False) else '❌ 失败'}")
                        print(f"步数: {steps}")
                        print("最终棋盘:")
                        print(trainer.env.board)
                        break

                if input("\n继续测试? (y/n): ").lower().strip() != 'y':
                    break

        else:
            print("❌ 无效选择")

    except KeyboardInterrupt:
        print("\n👋 用户中断")
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

    print(f"\n📁 输出文件:")
    print(f"   模型文件: {trainer.model_path}")
    print(f"   网页端数据: {trainer.web_model_path}")
    print(f"   JavaScript求解器: ai_puzzle_solver.js")
    print(f"   集成说明: 网页端集成说明.md")
    print(f"   训练图表: training_stats.png")

if __name__ == "__main__":
    main()
