# 安徽联通话费领取工具 - 代理抓包使用说明

## 🎯 功能概述

本工具已升级支持Proxifier+Fiddler代理抓包调试，可以方便地进行网络请求分析和调试。

## 🔧 主要修改

### 1. 新增代理功能
- ✅ 支持HTTP/HTTPS代理
- ✅ 可配置代理地址和端口
- ✅ 支持SSL证书信任设置
- ✅ 代理开关控制

### 2. 更新的请求参数
- ✅ 新的API地址：`https://huodong.10155.com/wo_activity/hy/t-biz-hy-right-receive-order/receiveRightHY`
- ✅ POST请求方式
- ✅ 新的请求头和数据格式
- ✅ 账号格式：`账号----token`

### 3. 增强的调试功能
- ✅ 代理连接测试
- ✅ SSL证书信任控制
- ✅ 详细的网络状态显示

## 🚀 使用步骤

### 第一步：准备Fiddler
1. 下载并安装 **Fiddler Classic**
2. 启动Fiddler
3. 配置HTTPS解密：
   - 菜单：`Tools` → `Options` → `HTTPS`
   - 勾选：`Capture HTTPS CONNECTs`
   - 勾选：`Decrypt HTTPS traffic`
   - 点击：`Actions` → `Trust Root Certificate`
4. 配置连接设置：
   - 菜单：`Tools` → `Options` → `Connections`
   - 确认端口：`8888`
   - 勾选：`Allow remote computers to connect`
5. 重启Fiddler

### 第二步：配置工具
1. 启动 `安徽联通领话费.py`
2. 在右侧"代理调试设置"区域：
   - ✅ 勾选"启用代理 (Fiddler/Proxifier)"
   - 设置代理地址：`127.0.0.1`
   - 设置端口：`8888`
   - ❌ 取消勾选"验证SSL证书"（重要！）
3. 点击"测试代理"按钮验证连接

### 第三步：导入账号
1. 准备账号文件，格式：
   ```
   手机号1----token1
   手机号2----token2
   手机号3----token3
   ```
2. 点击"导入账号"按钮
3. 选择账号文件

### 第四步：开始抓包调试
1. 确保Fiddler正在运行
2. 点击"立即领取"或设置自动领取
3. 在Fiddler中观察网络请求

## 📊 抓包分析

### 请求信息
- **URL**: `https://huodong.10155.com/wo_activity/hy/t-biz-hy-right-receive-order/receiveRightHY`
- **方法**: POST
- **Content-Type**: `application/x-www-form-urlencoded;charset=UTF-8`

### 关键请求头
```
Host: huodong.10155.com
accessToken: Bearer {token}
Origin: https://huodong.10155.com
Referer: https://huodong.10155.com/h5/hdact4/bojin20220509_lottery/
```

### POST数据
```
productId=11269449&activityId=625&channel=bac27baa773c5bc2d3060edeb9881c64&rightsSkuId=95787a32c8bab4e76215ba66d25386d5
```

### 成功响应特征
- 响应内容包含 `"success":true` 或类似的成功标识
- 工具会自动检测响应中是否包含"true"

## 🔍 调试技巧

### 1. 修改请求参数
在Fiddler中可以：
- 修改请求头中的token
- 修改POST数据中的参数
- 重放请求进行测试

### 2. 分析响应内容
观察响应中的：
- 成功/失败状态
- 错误信息
- 返回的数据结构

### 3. 网络问题排查
如果代理连接失败：
- 检查Fiddler是否启动
- 确认端口号正确（默认8888）
- 检查防火墙设置
- 确认SSL证书已信任

## ⚠️ 注意事项

### 安全提醒
- 仅用于学习和调试目的
- 不要在生产环境中禁用SSL验证
- 调试完成后记得关闭代理

### 性能提醒
- 启用代理会略微降低请求速度
- 大量请求时建议关闭代理
- Fiddler会记录所有请求，注意清理日志

### 兼容性
- 支持Windows 10+
- 需要Python 3.6+
- 需要安装requests、urllib3等依赖

## 🛠️ 故障排除

### 问题1：代理连接失败
**症状**：测试代理时显示连接失败
**解决**：
1. 确认Fiddler已启动
2. 检查端口号是否为8888
3. 重启Fiddler
4. 检查Windows防火墙

### 问题2：SSL证书错误
**症状**：HTTPS请求失败
**解决**：
1. 在工具中取消勾选"验证SSL证书"
2. 在Fiddler中安装根证书
3. 重启浏览器和工具

### 问题3：请求被拦截但无响应
**症状**：Fiddler显示请求但工具无响应
**解决**：
1. 检查Fiddler的AutoResponder设置
2. 确认没有设置断点
3. 检查Fiddler的过滤器设置

## 📝 日志文件

所有请求结果会保存到 `安徽话费.txt` 文件中，包括：
- 请求时间（毫秒精度）
- 账号信息
- 响应内容
- 成功/失败状态
- 统计信息

## 🎉 总结

通过这次升级，工具现在完全支持代理抓包调试，可以：
- 🔍 实时查看所有网络请求
- 🛠️ 修改请求参数进行测试
- 📊 分析响应数据结构
- 🐛 快速定位网络问题

这大大提高了调试效率和问题排查能力！
