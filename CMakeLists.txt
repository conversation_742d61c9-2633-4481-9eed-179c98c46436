cmake_minimum_required(VERSION 3.16)

project(TetrisPuzzleGame VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# Enable Qt MOC (Meta-Object Compiler)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Add executable
add_executable(TetrisPuzzleGame
    main.cpp
    TetrisPuzzleGame.cpp
    TetrisPuzzleGame.h
)

# Link Qt libraries
target_link_libraries(TetrisPuzzleGame
    Qt6::Core
    Qt6::Widgets
)

# Set target properties
set_target_properties(TetrisPuzzleGame PROPERTIES
    WIN32_EXECUTABLE TRUE
    MACOSX_BUNDLE TRUE
)

# Platform-specific settings
if(WIN32)
    # Windows specific settings
    set_target_properties(TetrisPuzzleGame PROPERTIES
        OUTPUT_NAME "俄罗斯方块拼图游戏"
    )
endif()

if(APPLE)
    # macOS specific settings
    set_target_properties(TetrisPuzzleGame PROPERTIES
        MACOSX_BUNDLE_INFO_PLIST ${CMAKE_CURRENT_SOURCE_DIR}/Info.plist
        MACOSX_BUNDLE_BUNDLE_NAME "俄罗斯方块拼图游戏"
        MACOSX_BUNDLE_DISPLAY_NAME "俄罗斯方块拼图游戏"
        MACOSX_BUNDLE_IDENTIFIER "com.tetrispuzzle.game"
        MACOSX_BUNDLE_VERSION ${PROJECT_VERSION}
        MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION}
    )
endif()

# Install target
install(TARGETS TetrisPuzzleGame
    BUNDLE DESTINATION .
    RUNTIME DESTINATION bin
)

# Qt deployment
if(WIN32)
    # Find Qt6 installation path
    get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
    get_filename_component(QT_WINDEPLOYQT_EXECUTABLE ${QT_QMAKE_EXECUTABLE} PATH)
    set(QT_WINDEPLOYQT_EXECUTABLE "${QT_WINDEPLOYQT_EXECUTABLE}/windeployqt.exe")

    # Add a post-build step to deploy Qt
    add_custom_command(TARGET TetrisPuzzleGame POST_BUILD
        COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} $<TARGET_FILE:TetrisPuzzleGame>
        COMMENT "Deploying Qt libraries")
endif()

if(APPLE)
    # Find Qt6 installation path
    get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
    get_filename_component(QT_MACDEPLOYQT_EXECUTABLE ${QT_QMAKE_EXECUTABLE} PATH)
    set(QT_MACDEPLOYQT_EXECUTABLE "${QT_MACDEPLOYQT_EXECUTABLE}/macdeployqt")

    # Add a post-build step to deploy Qt
    add_custom_command(TARGET TetrisPuzzleGame POST_BUILD
        COMMAND ${QT_MACDEPLOYQT_EXECUTABLE} $<TARGET_BUNDLE_DIR:TetrisPuzzleGame>
        COMMENT "Deploying Qt libraries")
endif()
